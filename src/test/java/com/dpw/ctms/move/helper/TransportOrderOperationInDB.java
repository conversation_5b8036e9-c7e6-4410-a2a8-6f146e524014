package com.dpw.ctms.move.helper;

import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.fakers.Fakers;
import com.dpw.ctms.move.service.ITransportOrderService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
public class TransportOrderOperationInDB {

    private final ITransportOrderService transportOrderService;

    public TransportOrder createTransportOrder() {
        return transportOrderService.saveTransportOrder(Fakers.createTransportOrder());
    }

    public TransportOrder findById(Long id) {
        return transportOrderService.findTransportOrderById(id);
    }
}
