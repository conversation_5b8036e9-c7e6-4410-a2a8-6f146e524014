package com.dpw.ctms.move.helper;

import com.dpw.ctms.move.dto.EntityTypeWithIdsDTO;
import com.dpw.ctms.move.dto.GetDocumentsDto;
import com.dpw.ctms.move.dto.document.DeliveryTaskDocumentDTO;
import com.dpw.ctms.move.entity.Document;
import com.dpw.ctms.move.enums.*;
import com.dpw.ctms.move.mapper.DocumentMapper;
import com.dpw.ctms.move.request.documentEvent.PreSignedUrlEvent;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DocumentHelperTest {

    @Mock
    private DocumentMapper documentMapper;

    @InjectMocks
    private DocumentHelper documentHelper;

    @Test
    void extractAsyncMappingUUID_shouldReturnSingleUUIDForPreSignedUrlEvent() {
        // Given
        PreSignedUrlEvent event = new PreSignedUrlEvent();
        event.setFileKey("file-uuid-123");

        // When
        List<String> result = documentHelper.extractAsyncMappingUUID(event);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("file-uuid-123", result.get(0));
    }

    @Test
    void extractAsyncMappingUUID_shouldReturnMultipleUUIDsForDeliveryTaskDocumentDTO() {
        // Given
        List<String> uuids = Arrays.asList("uuid1", "uuid2", "uuid3");
        DeliveryTaskDocumentDTO dto = DeliveryTaskDocumentDTO.builder()
                .asyncMappingUUIDs(uuids)
                .build();

        // When
        List<String> result = documentHelper.extractAsyncMappingUUID(dto);

        // Then
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals(uuids, result);
    }

    @Test
    void extractAsyncMappingUUID_shouldThrowExceptionForUnsupportedType() {
        // Given
        String unsupportedType = "unsupported";

        // When & Then
        assertThrows(IllegalArgumentException.class, 
                () -> documentHelper.extractAsyncMappingUUID(unsupportedType));
    }

    @Test
    void processDocuments_shouldActivateInactiveDocument() {
        // Given
        String asyncMappingUUID = "uuid123";
        PreSignedUrlEvent event = new PreSignedUrlEvent();
        event.setFileKey(asyncMappingUUID);
        event.setFileIdentifier("file-id-123");
        
        Document inactiveDoc = Document.builder()
                .asyncMappingUUID(asyncMappingUUID)
                .status(DocumentStatus.INACTIVE)
                .fileIdentifier("file-id-123")
                .entityId("entity123")
                .build();
                
        Map<String, Document> existingDocs = new HashMap<>();
        existingDocs.put(asyncMappingUUID, inactiveDoc);
        
        // When
        List<Document> result = documentHelper.processDocuments(event, existingDocs);
        
        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(DocumentStatus.ACTIVE, result.get(0).getStatus());
        verify(documentMapper).updateDocument(inactiveDoc, event);
    }

    @Test
    void processDocuments_shouldSkipActiveDocument() {
        // Given
        String asyncMappingUUID = "uuid123";
        PreSignedUrlEvent event = new PreSignedUrlEvent();
        event.setFileKey(asyncMappingUUID);
        
        Document activeDoc = Document.builder()
                .asyncMappingUUID(asyncMappingUUID)
                .status(DocumentStatus.ACTIVE)
                .build();
                
        Map<String, Document> existingDocs = new HashMap<>();
        existingDocs.put(asyncMappingUUID, activeDoc);
        
        // When
        List<Document> result = documentHelper.processDocuments(event, existingDocs);
        
        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(documentMapper, never()).updateDocument(any(), (PreSignedUrlEvent) any());
    }

    @Test
    void processDocuments_shouldSkipDiscardedDocument() {
        // Given
        String asyncMappingUUID = "uuid123";
        PreSignedUrlEvent event = new PreSignedUrlEvent();
        event.setFileKey(asyncMappingUUID);
        
        Document discardedDoc = Document.builder()
                .asyncMappingUUID(asyncMappingUUID)
                .status(DocumentStatus.DISCARDED)
                .build();
                
        Map<String, Document> existingDocs = new HashMap<>();
        existingDocs.put(asyncMappingUUID, discardedDoc);
        
        // When
        List<Document> result = documentHelper.processDocuments(event, existingDocs);
        
        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(documentMapper, never()).updateDocument(any(), (PreSignedUrlEvent) any());
    }

    @Test
    void processDocuments_shouldCreateNewDocumentWhenNotExists() {
        // Given
        String asyncMappingUUID = "uuid123";
        PreSignedUrlEvent event = new PreSignedUrlEvent();
        event.setFileKey(asyncMappingUUID);
        
        Document newDoc = Document.builder()
                .asyncMappingUUID(asyncMappingUUID)
                .status(DocumentStatus.INACTIVE)
                .build();
                
        when(documentMapper.createDocument(event)).thenReturn(newDoc);
        
        Map<String, Document> existingDocs = new HashMap<>();
        
        // When
        List<Document> result = documentHelper.processDocuments(event, existingDocs);
        
        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(newDoc, result.get(0));
        verify(documentMapper).createDocument(event);
    }

    @Test
    void buildActivePodDocumentFilters_shouldGroupByEntityType() {
        // Given
        Document doc1 = Document.builder()
                .entityId("TASK-001")
                .entityType("TASK")
                .build();
                
        Document doc2 = Document.builder()
                .entityId("TASK-002")
                .entityType("TASK")
                .build();
                
        Document doc3 = Document.builder()
                .entityId("SHIP-001")
                .entityType("SHIPMENT")
                .build();
                
        List<Document> documents = Arrays.asList(doc1, doc2, doc3);
        
        // When
        List<GetDocumentsDto> result = documentHelper.buildActivePodDocumentFilters(documents);
        
        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        
        // Verify TASK group
        GetDocumentsDto taskFilter = result.stream()
                .filter(dto -> dto.getEntityType().equals("TASK"))
                .findFirst()
                .orElse(null);
        assertNotNull(taskFilter);
        assertEquals(Set.of("TASK-001", "TASK-002"), Set.copyOf(taskFilter.getEntityIds()));
        assertEquals(DocumentType.POD, taskFilter.getDocumentType());
        assertEquals(DocumentOperationType.UPLOAD, taskFilter.getOperationType());
        assertEquals(DocumentStatus.ACTIVE, taskFilter.getStatus());
        
        // Verify SHIPMENT group
        GetDocumentsDto shipmentFilter = result.stream()
                .filter(dto -> dto.getEntityType().equals("SHIPMENT"))
                .findFirst()
                .orElse(null);
        assertNotNull(shipmentFilter);
        assertEquals(List.of("SHIP-001"), shipmentFilter.getEntityIds());
        assertEquals(DocumentType.POD, shipmentFilter.getDocumentType());
        assertEquals(DocumentOperationType.UPLOAD, shipmentFilter.getOperationType());
        assertEquals(DocumentStatus.ACTIVE, shipmentFilter.getStatus());
    }
    
    @Test
    void filterActivePodUploadDocuments_shouldFilterCorrectly() {
        // Given
        Document podUploadDoc = Document.builder()
                .documentType(DocumentType.POD)
                .documentOperationType(DocumentOperationType.UPLOAD)
                .status(DocumentStatus.ACTIVE)
                .build();
                
        Document bolUploadDoc = Document.builder()
                .documentType(DocumentType.BOL)
                .documentOperationType(DocumentOperationType.UPLOAD)
                .status(DocumentStatus.ACTIVE)
                .build();
                
        Document podDownloadDoc = Document.builder()
                .documentType(DocumentType.POD)
                .documentOperationType(DocumentOperationType.DOWNLOAD)
                .status(DocumentStatus.ACTIVE)
                .build();
                
        Document inactivePodDoc = Document.builder()
                .documentType(DocumentType.POD)
                .documentOperationType(DocumentOperationType.UPLOAD)
                .status(DocumentStatus.INACTIVE)
                .build();
                
        List<Document> documents = Arrays.asList(podUploadDoc, bolUploadDoc, podDownloadDoc, inactivePodDoc);
        
        // When
        List<Document> result = documentHelper.filterActivePodUploadDocuments(documents);
        
        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(podUploadDoc, result.get(0));
    }
    
    @Test
    void filterDiscardedPodUploadDocuments_shouldFilterCorrectly() {
        // Given
        Document discardedPodUploadDoc = Document.builder()
                .documentType(DocumentType.POD)
                .documentOperationType(DocumentOperationType.UPLOAD)
                .status(DocumentStatus.DISCARDED)
                .build();
                
        Document activePodUploadDoc = Document.builder()
                .documentType(DocumentType.POD)
                .documentOperationType(DocumentOperationType.UPLOAD)
                .status(DocumentStatus.ACTIVE)
                .build();
                
        Document discardedBolDoc = Document.builder()
                .documentType(DocumentType.BOL)
                .documentOperationType(DocumentOperationType.UPLOAD)
                .status(DocumentStatus.DISCARDED)
                .build();
                
        List<Document> documents = Arrays.asList(discardedPodUploadDoc, activePodUploadDoc, discardedBolDoc);
        
        // When
        List<Document> result = documentHelper.filterDiscardedPodUploadDocuments(documents);
        
        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(discardedPodUploadDoc, result.get(0));
    }
    
    @Test
    void groupDocumentsByEntityType_shouldGroupCorrectly() {
        // Given
        Document taskDoc1 = Document.builder()
                .entityId("TASK-001")
                .entityType(EntityType.TASK.name())
                .build();
                
        Document taskDoc2 = Document.builder()
                .entityId("TASK-002")
                .entityType(EntityType.TASK.name())
                .build();
                
        Document shipmentDoc = Document.builder()
                .entityId("SHIP-001")
                .entityType(EntityType.SHIPMENT.name())
                .build();
                
        Document tripDoc = Document.builder()
                .entityId("TRIP-001")
                .entityType(EntityType.TRIP.name())
                .build();
                
        List<Document> documents = Arrays.asList(taskDoc1, taskDoc2, shipmentDoc, tripDoc);
        
        // When
        List<EntityTypeWithIdsDTO> result = documentHelper.groupDocumentsByEntityType(documents);
        
        // Then
        assertNotNull(result);
        assertEquals(3, result.size());
        
        // Verify TASK group
        EntityTypeWithIdsDTO taskGroup = result.stream()
                .filter(dto -> dto.getEntityType().equals(EntityType.TASK.name()))
                .findFirst()
                .orElse(null);
        assertNotNull(taskGroup);
        assertEquals(Set.of("TASK-001", "TASK-002"), taskGroup.getEntityIds());
        
        // Verify SHIPMENT group
        EntityTypeWithIdsDTO shipmentGroup = result.stream()
                .filter(dto -> dto.getEntityType().equals(EntityType.SHIPMENT.name()))
                .findFirst()
                .orElse(null);
        assertNotNull(shipmentGroup);
        assertEquals(Set.of("SHIP-001"), shipmentGroup.getEntityIds());
        
        // Verify TRIP group
        EntityTypeWithIdsDTO tripGroup = result.stream()
                .filter(dto -> dto.getEntityType().equals(EntityType.TRIP.name()))
                .findFirst()
                .orElse(null);
        assertNotNull(tripGroup);
        assertEquals(Set.of("TRIP-001"), tripGroup.getEntityIds());
    }
    
    @Test
    void discardDocuments_shouldUpdateStatusCorrectly() {
        // Given
        Document activeDoc1 = Document.builder()
                .status(DocumentStatus.ACTIVE)
                .build();
                
        Document activeDoc2 = Document.builder()
                .status(DocumentStatus.ACTIVE)
                .build();
                
        List<Document> documents = Arrays.asList(activeDoc1, activeDoc2);
        
        // When
        List<Document> result = documentHelper.discardDocuments(documents);
        
        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(DocumentStatus.DISCARDED, result.get(0).getStatus());
        assertEquals(DocumentStatus.DISCARDED, result.get(1).getStatus());
    }
    
    @Test
    void filterActivatedDocuments_shouldReturnOnlyActivatedDocuments() {
        // Given
        Document activatedDoc = Document.builder()
                .status(DocumentStatus.ACTIVE)
                .build();
                
        Document inactiveDoc = Document.builder()
                .status(DocumentStatus.INACTIVE)
                .build();
                
        Document discardedDoc = Document.builder()
                .status(DocumentStatus.DISCARDED)
                .build();
                
        List<Document> documents = Arrays.asList(activatedDoc, inactiveDoc, discardedDoc);
        
        // When
        List<Document> result = documentHelper.filterActivatedDocuments(documents);
        
        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(activatedDoc, result.get(0));
    }
    
    @Test
    void getDocumentsWithNoActiveEntityDocuments_shouldReturnCorrectDocuments() {
        // Given
        Document discardedDoc1 = Document.builder()
                .entityId("ENTITY-001")
                .entityType(EntityType.TASK.name())
                .documentType(DocumentType.POD)
                .status(DocumentStatus.DISCARDED)
                .build();
                
        Document discardedDoc2 = Document.builder()
                .entityId("ENTITY-002")
                .entityType(EntityType.TASK.name())
                .documentType(DocumentType.POD)
                .status(DocumentStatus.DISCARDED)
                .build();
                
        List<Document> discardedDocuments = Arrays.asList(discardedDoc1, discardedDoc2);
        
        // Mock active document for ENTITY-001 (should exclude discardedDoc1)
        Document activeDoc = Document.builder()
                .entityId("ENTITY-001")
                .entityType(EntityType.TASK.name())
                .documentType(DocumentType.POD)
                .status(DocumentStatus.ACTIVE)
                .build();
                
        List<Document> activeDocuments = Arrays.asList(activeDoc);
        
        // When
        List<Document> result = documentHelper.getDocumentsWithNoActiveEntityDocuments(
                discardedDocuments, activeDocuments, DocumentType.POD);
        
        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(discardedDoc2, result.get(0)); // Only ENTITY-002 should remain
    }
    
    @Test
    void processDocuments_withDeliveryTaskDocumentDTO_shouldCreateNewDocuments() {
        // Given
        List<String> asyncMappingUUIDs = Arrays.asList("uuid1", "uuid2");
        DeliveryTaskDocumentDTO dto = DeliveryTaskDocumentDTO.builder()
                .entityId("TASK-001")
                .entityType(EntityType.TASK.name())
                .asyncMappingUUIDs(asyncMappingUUIDs)
                .operationType(DocumentOperationType.UPLOAD)
                .documentType(DocumentType.POD)
                .build();
                
        Document newDoc1 = Document.builder()
                .asyncMappingUUID("uuid1")
                .entityId("TASK-001")
                .status(DocumentStatus.INACTIVE)
                .build();
                
        Document newDoc2 = Document.builder()
                .asyncMappingUUID("uuid2")
                .entityId("TASK-001")
                .status(DocumentStatus.INACTIVE)
                .build();
                
        when(documentMapper.createDocument(dto, "uuid1")).thenReturn(newDoc1);
        when(documentMapper.createDocument(dto, "uuid2")).thenReturn(newDoc2);
        
        Map<String, Document> existingDocs = new HashMap<>();
        
        // When
        List<Document> result = documentHelper.processDocuments(dto, existingDocs);
        
        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(newDoc1, result.get(0));
        assertEquals(newDoc2, result.get(1));
        verify(documentMapper).createDocument(dto, "uuid1");
        verify(documentMapper).createDocument(dto, "uuid2");
    }
    
    @Test
    void processDocuments_withDeliveryTaskDocumentDTO_shouldUpdateExistingInactiveDocuments() {
        // Given
        List<String> asyncMappingUUIDs = Arrays.asList("uuid1");
        DeliveryTaskDocumentDTO dto = DeliveryTaskDocumentDTO.builder()
                .entityId("TASK-001")
                .entityType(EntityType.TASK.name())
                .asyncMappingUUIDs(asyncMappingUUIDs)
                .operationType(DocumentOperationType.UPLOAD)
                .documentType(DocumentType.POD)
                .build();
                
        Document existingDoc = Document.builder()
                .asyncMappingUUID("uuid1")
                .entityId("TASK-001")
                .fileIdentifier("file-id-1")
                .status(DocumentStatus.INACTIVE)
                .build();
                
        Map<String, Document> existingDocs = new HashMap<>();
        existingDocs.put("uuid1", existingDoc);
        
        // When
        List<Document> result = documentHelper.processDocuments(dto, existingDocs);
        
        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(DocumentStatus.ACTIVE, result.get(0).getStatus());
        verify(documentMapper).updateDocument(existingDoc, dto);
    }
}