package com.dpw.ctms.move.helper;


import com.dpw.ctms.move.dto.ConsignmentDetailsDTO;
import com.dpw.ctms.move.dto.CustomerOrderMetaDataDTO;
import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.integration.adapter.TrackingServiceAdapter;
import com.dpw.ctms.move.dto.FacilityDetailsDTO;
import com.dpw.ctms.move.integration.dto.oms.OmsConsignmentDto;
import com.dpw.ctms.move.integration.dto.resource.ResourceFacilitiesDto;
import com.dpw.ctms.move.integration.dto.trackingservice.TripTrackingDTO;
import com.dpw.ctms.move.integration.mapper.trackingservice.TrackingServiceRequestMapper;
import com.dpw.ctms.move.integration.request.trackingservice.TripTrackingRequest;
import com.dpw.ctms.move.integration.response.trackingservice.TripTrackingResponse;
import com.dpw.ctms.move.integration.service.IOmsIntegratorService;
import com.dpw.ctms.move.integration.service.IResourceIntegratorService;
import com.dpw.ctms.move.service.ITripService;
import com.dpw.ctms.move.validator.TripValidator;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import java.util.List;
import java.util.Set;

import static com.dpw.ctms.move.constants.TrackingServiceConstants.CLOSE_TRIP;
import static com.dpw.ctms.move.constants.TrackingServiceConstants.START_TRIP;
import static org.mockito.Mockito.*;
import static org.assertj.core.api.Assertions.assertThat;

class TrackingServiceIntegrationHelperTest {

    @Mock
    private TrackingServiceAdapter trackingServiceAdapter;

    @Mock
    private TrackingServiceRequestMapper trackingServiceRequestMapper;

    @Mock
    private TripValidator tripValidator;

    @Mock
    private ITripService tripService;

    @Mock
    private IOmsIntegratorService omsIntegratorService;

    @Mock
    private IResourceIntegratorService resourceIntegratorService;

    @InjectMocks
    private TrackingServiceIntegrationHelper helper;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testSendTripTrackingEvent_whenTripStart_shouldCallTrackingAdapter() throws JsonProcessingException {
        // Arrange
        Trip initialTrip = new Trip();
        initialTrip.setId(1L);

        Trip currentTrip = new Trip();
        currentTrip.setId(1L);
        currentTrip.setExternalOriginLocationCode("fac1");
        currentTrip.setExternalDestinationLocationCode("fac2");

        Shipment shipment = new Shipment();
        shipment.setExternalConsignmentId("EXT123");
        currentTrip.setShipments(Set.of(shipment));

        TripTrackingDTO tripTrackingDTO = TripTrackingDTO.builder()
                .initialTrip(initialTrip)
                .build();

        OmsConsignmentDto consignmentDetailsDTO = OmsConsignmentDto.builder()
                .customerOrderMetadata(OmsConsignmentDto.CustomerOrderMetadata.builder()
                        .customerOrderNumber("ORD-123")
                        .build())
                .build();

        ResourceFacilitiesDto facilityDetailsDTO = ResourceFacilitiesDto.builder()
                .code("FAC1")
                .name("Mock Facility")
                .build();


        TripTrackingRequest tripTrackingRequest = TripTrackingRequest.builder().build();
        TripTrackingResponse trackingResponse = new TripTrackingResponse();
        trackingResponse.setCode("200");
        trackingResponse.setStatus("success");

        when(tripService.findTripById(1L)).thenReturn(currentTrip);
        when(tripValidator.validateTripStart(initialTrip, currentTrip)).thenReturn(true);
        when(omsIntegratorService.getOmsConsignmentDtos(List.of("EXT123")))
                .thenReturn(List.of(consignmentDetailsDTO));
        when(resourceIntegratorService.getFacilitiesDTOs(List.of("fac1")))
                .thenReturn(List.of(facilityDetailsDTO));
        when(trackingServiceRequestMapper.mapTripTrackingRequest(any())).thenReturn(tripTrackingRequest);
        when(trackingServiceAdapter.trackTrip(any())).thenReturn(trackingResponse);

        // Act
        helper.sendTripTrackingEvent(tripTrackingDTO);

        // Assert
        verify(tripService).findTripById(1L);
        verify(tripValidator).validateTripStart(initialTrip, currentTrip);
        verify(omsIntegratorService).getOmsConsignmentDtos(List.of("EXT123"));
        verify(trackingServiceRequestMapper).mapTripTrackingRequest(any());

        assertThat(tripTrackingDTO.getEventType()).isEqualTo(START_TRIP);
        assertThat(tripTrackingDTO.getExternalCustomerOrderReferenceNumbers()).containsExactly("ORD-123");
    }

    @Test
    void testSendTripTrackingEvent_whenTripEnd_shouldCallTrackingAdapter() throws JsonProcessingException {
        // Arrange
        Trip initialTrip = new Trip();
        initialTrip.setId(2L);

        Trip currentTrip = new Trip();
        currentTrip.setId(2L);

        currentTrip.setExternalOriginLocationCode("fac1");
        currentTrip.setExternalDestinationLocationCode("fac2");

        Shipment shipment = new Shipment();
        shipment.setExternalConsignmentId("EXT999");
        currentTrip.setShipments(Set.of(shipment));

        TripTrackingDTO tripTrackingDTO = TripTrackingDTO.builder()
                .initialTrip(initialTrip)
                .build();

        ConsignmentDetailsDTO consignmentDetailsDTO = ConsignmentDetailsDTO.builder()
                .customerOrderMetaData(CustomerOrderMetaDataDTO.builder()
                        .customerOrderNumber("ORD-999")
                        .build())
                .build();

        FacilityDetailsDTO facilityDetailsDTO = FacilityDetailsDTO.builder()
                .code("FAC1")
                .name("Mock Facility")
                .build();

        TripTrackingRequest tripTrackingRequest = TripTrackingRequest.builder().build();
        TripTrackingResponse trackingResponse = new TripTrackingResponse();

        when(tripService.findTripById(2L)).thenReturn(currentTrip);
        when(tripValidator.validateTripStart(initialTrip, currentTrip)).thenReturn(false);
        when(tripValidator.validateTripEnd(initialTrip, currentTrip)).thenReturn(true);
        when(omsIntegratorService.getOmsConsignmentDtos(List.of("EXT999"))).thenReturn(List.of(OmsConsignmentDto.builder()
                .customerOrderMetadata(OmsConsignmentDto.CustomerOrderMetadata.builder()
                        .customerOrderNumber("ORD-999")
                        .build())
                .build()));
        when(resourceIntegratorService.getFacilitiesDTOs(List.of("fac1")))
                .thenReturn(List.of(ResourceFacilitiesDto.builder()
                        .code("FAC1")
                        .name("Mock Facility")
                        .build()));
        when(trackingServiceRequestMapper.mapTripTrackingRequest(any())).thenReturn(tripTrackingRequest);
        when(trackingServiceAdapter.trackTrip(any())).thenReturn(trackingResponse);

        // Act
        helper.sendTripTrackingEvent(tripTrackingDTO);

        // Assert
        verify(tripValidator).validateTripEnd(initialTrip, currentTrip);
        assertThat(tripTrackingDTO.getEventType()).isEqualTo(CLOSE_TRIP);
        assertThat(tripTrackingDTO.getExternalCustomerOrderReferenceNumbers()).containsExactly("ORD-999");
    }

    @Test
    void testSendTripTrackingEvent_whenNoChange_shouldNotSendEvent() throws JsonProcessingException {
        // Arrange
        Trip initialTrip = new Trip();
        initialTrip.setId(3L);
        Trip currentTrip = new Trip();
        currentTrip.setId(3L);

        TripTrackingDTO dto = TripTrackingDTO.builder()
                .initialTrip(initialTrip)
                .build();

        when(tripService.findTripById(3L)).thenReturn(currentTrip);
        when(tripValidator.validateTripStart(initialTrip, currentTrip)).thenReturn(false);
        when(tripValidator.validateTripEnd(initialTrip, currentTrip)).thenReturn(false);

        // Act
        helper.sendTripTrackingEvent(dto);

        // Assert
        verify(trackingServiceAdapter, never()).trackTrip(any());
    }
}
