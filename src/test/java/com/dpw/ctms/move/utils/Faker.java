package com.dpw.ctms.move.utils;

import com.dpw.ctms.move.constants.TripFieldConstants;
import com.dpw.ctms.move.dto.ParamValueShipmentDTO;
import com.dpw.ctms.move.dto.ParamValueStopDTO;
import com.dpw.ctms.move.enums.AssignmentStatus;
import com.dpw.ctms.move.enums.AssignmentType;
import com.dpw.ctms.move.enums.ResourceAssignmentType;
import com.dpw.ctms.move.enums.ShipmentStatus;
import com.dpw.ctms.move.enums.TransportOrderStatus;
import com.dpw.ctms.move.enums.TripStatus;
import com.dpw.ctms.move.integration.response.taskmanager.TaskInstanceDeRegistrationResponse;
import com.dpw.ctms.move.request.AssignedStopRangeRequest;
import com.dpw.ctms.move.request.ResourceAssignmentDetailsRequest;
import com.dpw.ctms.move.request.SegmentDetailsRequest;
import com.dpw.ctms.move.request.ShipmentRequest;
import com.dpw.ctms.move.request.StaticDataRequest;
import com.dpw.ctms.move.request.StopDetailsRequest;
import com.dpw.ctms.move.request.StopRequest;
import com.dpw.ctms.move.request.TaskParamRequest;
import com.dpw.ctms.move.request.TaskRequest;
import com.dpw.ctms.move.request.TrailerResourceRequest;
import com.dpw.ctms.move.request.TransportOrderFTLCreateRequest;
import com.dpw.ctms.move.request.TransportOrderListingRequest;
import com.dpw.ctms.move.request.TripCreateRequest;
import com.dpw.ctms.move.request.DateRange;
import com.dpw.ctms.move.request.TripListingRequest;
import com.dpw.ctms.move.request.ShipmentListingRequest;
import com.dpw.ctms.move.request.VehicleOperatorResourceRequest;
import com.dpw.ctms.move.request.VehicleResourceRequest;
import com.dpw.ctms.move.request.common.Pagination;
import com.dpw.ctms.move.request.common.Sort;
import com.dpw.ctms.move.integration.response.taskmanager.TaskInstanceRegistrationResponse;
import com.dpw.ctms.move.request.common.TimeRequest;
import com.dpw.tmsutils.utils.ObjectMapperUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class Faker {

    // Common pagination configurations
    public static final Pagination DEFAULT_PAGINATION = new Pagination(0, 10);
    public static final Pagination SINGLE_PAGE_PAGINATION = new Pagination(0, 1);
    public static final Pagination LARGE_PAGE_PAGINATION = new Pagination(0, 100);

    // Common sort configurations
    public static final Sort DEFAULT_SORT = new Sort(TripFieldConstants.SortFields.API_CODE, "ASC");
    public static final Sort CREATED_AT_DESC_SORT = new Sort(TripFieldConstants.SortFields.API_CREATED_AT, "DESC");
    public static final Sort STATUS_ASC_SORT = new Sort(TripFieldConstants.SortFields.API_STATUS, "ASC");

    // Common filters
    public static final TripListingRequest.Filter EMPTY_FILTER = new TripListingRequest.Filter();
    public static final ShipmentListingRequest.Filter EMPTY_SHIPMENT_FILTER = new ShipmentListingRequest.Filter();

    private static final com.github.javafaker.Faker faker = new com.github.javafaker.Faker();

    public static TransportOrderFTLCreateRequest createDummyTransportOrderCreateRequest() {
        String code = "TO" + faker.regexify("[0-9]{3}");
        String status = TransportOrderStatus.ASSIGNED.toString();
        AssignmentType assignmentType = AssignmentType.INTERNAL;
        String assignmentCode = "cost center " + faker.regexify("[A-Z0-9]{4}");
        String assigneeIdentifier = "Vendor " + faker.letterify("???") + "/ Cost center " + faker.numerify("###");
        List<TripCreateRequest> trips = List.of(createDummyTrip());
        return new TransportOrderFTLCreateRequest(code, status, assignmentType, assignmentCode, assigneeIdentifier, trips);
    }

    private static TripCreateRequest createDummyTrip() {
        String code = "TRP" + faker.regexify("[0-9]{3}");
        String status = TripStatus.CREATED.toString();
        String externalOrigin = faker.regexify("[a-z0-9]{10}");
        String externalDestination = faker.regexify("[a-z0-9]{10}");
        TimeRequest expectedStartAt = TimeRequest.builder().epoch(1L).timezone("Asia/Kolkata").build();
        TimeRequest expectedEndAt = TimeRequest.builder().epoch(2L).timezone("Asia/Kolkata").build();


        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode details = objectMapper.createObjectNode();

        List<StopRequest> stops = Arrays.asList(
                createDummyStop(1, externalOrigin, "SEG1", null),
                createDummyStop(2, externalDestination, null, "SEG1")
        );

        TaskRequest taskRequest = createDummyTask();


        List<ShipmentRequest> shipments = List.of(createDummyShipment(stops));
        VehicleResourceRequest vehicleResource = createDummyVehicleResource();
        List<TrailerResourceRequest> trailerResources = List.of(createDummyTrailerResource(stops));
        List<VehicleOperatorResourceRequest> vehicleOperatorResources = List.of(createDummyVehicleOperatorResource(stops));

        return TripCreateRequest.builder()
                .code(code)
                .status(status)
                .tasks(List.of(taskRequest))
                .externalOriginLocationCode(externalOrigin)
                .externalDestinationLocationCode(externalDestination)
                .expectedStartAt(expectedStartAt)
                .expectedEndAt(expectedEndAt)
                .stops(stops)
                .shipments(shipments)
                .vehicleResource(vehicleResource)
                .trailerResources(trailerResources)
                .vehicleOperatorResources(vehicleOperatorResources)
                .details(details)
                .build();
    }

    private static StopRequest createDummyStop(int sequence, String locationCode, String departureSegment, String arrivalSegment) {
        SegmentDetailsRequest segmentDetails = new SegmentDetailsRequest(arrivalSegment, departureSegment);
        return StopRequest.builder()
                .externalLocationCode(locationCode)
                .sequence(sequence)
                .segmentDetails(segmentDetails)
                .build();

    }


    private static TaskRequest createDummyTask() {
        return TaskRequest.builder()
                .code("TSK-1")
                .status("CREATED")
                .expectedStartAt(TimeRequest.builder().epoch(1L).timezone("Asia/Kolkata").build())
                .expectedEndAt(TimeRequest.builder().epoch(2L).timezone("Asia/Kolkata").build())
                .sequence(1)
                .externalTaskMasterCode("TSK-INST-1")
                .taskParams(createDummyTaskParams())
                .build();
    }

    private static List<TaskParamRequest> createDummyTaskParams() {
        ParamValueStopDTO paramValueStopDTO = ParamValueStopDTO.builder()
                .externalLocationCode("1")
                .sequence(1)
                .taskEvent("DEPARTURE")
                .build();

        JsonNode paramStopValue = ObjectMapperUtil.getObjectMapper().valueToTree(paramValueStopDTO);
        TaskParamRequest taskParamStopRequest = TaskParamRequest.builder()
                .paramName("STOP")
                .paramValue(paramStopValue)
                .build();

        ParamValueShipmentDTO paramValueShipmentDTO = ParamValueShipmentDTO.builder()
                .code("1")
                .build();
        JsonNode taskParamShipmentValue = ObjectMapperUtil.getObjectMapper().valueToTree(paramValueShipmentDTO);

        TaskParamRequest taskParamShipmentRequest = TaskParamRequest.builder()
                .paramName("SHIPMENT")
                .paramValue(taskParamShipmentValue)
                .build();
        return List.of(taskParamStopRequest, taskParamShipmentRequest);
    }

    private static ShipmentRequest createDummyShipment(List<StopRequest> stops) {
        String code = "SHIP_" + faker.regexify("[0-9]{3}");
        StopDetailsRequest origin = new StopDetailsRequest(stops.get(0).getExternalLocationCode(), stops.get(0).getSequence());
        StopDetailsRequest destination = new StopDetailsRequest(stops.get(1).getExternalLocationCode(), stops.get(1).getSequence());
        TimeRequest expectedPickupAt = TimeRequest.builder().epoch(System.currentTimeMillis()).timezone("Asia/Kolkata").build();
        TimeRequest expectedDeliveryAt = TimeRequest.builder().epoch(System.currentTimeMillis() + 7200000).timezone("Asia/Kolkata").build();
        String externalConsignmentId = "C" + faker.regexify("[0-9]");
        String externalCustomerOrderId = "CO" + faker.regexify("[0-9]");
        String status = ShipmentStatus.ASSIGNED.toString();
        BigDecimal weight = BigDecimal.valueOf(faker.number().randomDouble(1, 1, 100));
        String weightUom = "kg";
        BigDecimal volume = BigDecimal.valueOf(faker.number().randomDouble(1, 1, 500));
        String volumeUom = "l";

        return new ShipmentRequest(code, origin, destination, expectedPickupAt,
                expectedDeliveryAt, externalConsignmentId, externalCustomerOrderId,
                status, weight, weightUom, volume, volumeUom, null, null);
    }


    private static VehicleResourceRequest createDummyVehicleResource() {
        String code = "VEH_" + faker.regexify("[0-9]{3}");
        String externalResourceId = faker.regexify("[0-9]{6}");
        ResourceAssignmentDetailsRequest assignmentDetails = new ResourceAssignmentDetailsRequest(AssignmentStatus.PENDING.toString(),
                ResourceAssignmentType.COST_CENTER.toString(),
                System.currentTimeMillis(),
                "assigned_by_" + faker.name().username());

        String registrationNumber = "VRN_" + faker.regexify("[A-Z0-9]{5}");
        String externalVehicleTypeId = "VT_" + faker.regexify("[0-9]{3}");
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode details = objectMapper.createObjectNode();
        return VehicleResourceRequest.builder()
                .code(code)
                .externalResourceId(externalResourceId)
                .externalVehicleTypeId(externalVehicleTypeId)
                .resourceAssignmentDetails(assignmentDetails)
                .registrationNumber(registrationNumber)
                .details(details)
                .build();
    }

    private static TrailerResourceRequest createDummyTrailerResource(List<StopRequest> stops) {
        String code = "TR_" + faker.regexify("[0-9]{3}");
        String externalResourceId = faker.regexify("[0-9]{6}");
        ResourceAssignmentDetailsRequest assignmentDetails = new ResourceAssignmentDetailsRequest(AssignmentStatus.PENDING.toString(),
                ResourceAssignmentType.COST_CENTER.toString(),
                System.currentTimeMillis(),
                "assigned_by_" + faker.name().username());

        List<AssignedStopRangeRequest> stopRanges = new ArrayList<>();

        if (!stops.isEmpty()) {
            StopRequest first = stops.getFirst();
            StopRequest last = stops.getLast();
            stopRanges.add(new AssignedStopRangeRequest(
                    new StopDetailsRequest(first.getExternalLocationCode(), first.getSequence()),
                    new StopDetailsRequest(last.getExternalLocationCode(), last.getSequence())
            ));
        }

        return TrailerResourceRequest.builder()
                .assignedStopRanges(stopRanges)
                .code(code)
                .externalResourceId(externalResourceId)
                .resourceAssignmentDetails(assignmentDetails)
                .details(null)
                .build();
    }

    private static VehicleOperatorResourceRequest createDummyVehicleOperatorResource(List<StopRequest> stops) {
        String code = "VO_" + faker.regexify("[0-9]{3}");
        String externalResourceId = faker.regexify("[0-9]{6}");

        ResourceAssignmentDetailsRequest assignmentDetails = new ResourceAssignmentDetailsRequest(AssignmentStatus.PENDING.toString(),
                ResourceAssignmentType.COST_CENTER.toString(),
                System.currentTimeMillis(),
                "assigned_by_" + faker.name().username());

        List<AssignedStopRangeRequest> stopRanges = new ArrayList<>();

        if (!stops.isEmpty()) {
            StopRequest first = stops.getFirst();
            StopRequest last = stops.getLast();
            stopRanges.add(new AssignedStopRangeRequest(
                    new StopDetailsRequest(first.getExternalLocationCode(), first.getSequence()),
                    new StopDetailsRequest(last.getExternalLocationCode(), last.getSequence())
            ));
        }

        return VehicleOperatorResourceRequest.builder()
                .code(code)
                .externalResourceId(externalResourceId)
                .resourceAssignmentDetails(assignmentDetails)
                .assignedStopRanges(stopRanges)
                .details(null)
                .build();
    }

    /**
     * Creates a basic TripListingRequest with default pagination, sort, and empty filter
     */
    public static TripListingRequest createBasicTripListingRequest() {
        return TripListingRequest.builder()
                .pagination(DEFAULT_PAGINATION)
                .sort(DEFAULT_SORT)
                .filter(EMPTY_FILTER)
                .build();
    }

    public static TransportOrderListingRequest createBasicTransportOrderListingRequest() {
        return TransportOrderListingRequest.builder()
                .pagination(DEFAULT_PAGINATION)
                .sort(DEFAULT_SORT)
                .filter(null)
                .build();
    }

    public static TransportOrderListingRequest createBasicTransportOrderListingRequestWithFilters(TransportOrderListingRequest.Filter filter) {
        return TransportOrderListingRequest.builder()
                .pagination(DEFAULT_PAGINATION)
                .sort(DEFAULT_SORT)
                .filter(filter)
                .build();
    }

    public static TransportOrderListingRequest createTransportOrderListingRequestWithPagination(Pagination pagination) {
        return TransportOrderListingRequest.builder()
                .pagination(pagination)
                .sort(DEFAULT_SORT)
                .filter(null)
                .build();
    }

    /**
     * Creates a TripListingRequest with single page pagination
     */
    public static TripListingRequest createSinglePageTripListingRequest() {
        return TripListingRequest.builder()
                .pagination(SINGLE_PAGE_PAGINATION)
                .sort(DEFAULT_SORT)
                .filter(EMPTY_FILTER)
                .build();
    }

    /**
     * Creates a TripListingRequest with custom pagination
     */
    public static TripListingRequest createTripListingRequestWithPagination(Pagination pagination) {
        return TripListingRequest.builder()
                .pagination(pagination)
                .sort(DEFAULT_SORT)
                .filter(EMPTY_FILTER)
                .build();
    }

    /**
     * Creates a TripListingRequest with custom sort
     */
    public static TripListingRequest createTripListingRequestWithSort(Sort sort) {
        return TripListingRequest.builder()
                .pagination(DEFAULT_PAGINATION)
                .sort(sort)
                .filter(EMPTY_FILTER)
                .build();
    }

    /**
     * Creates a TripListingRequest with custom filter
     */
    public static TripListingRequest createTripListingRequestWithFilter(TripListingRequest.Filter filter) {
        return TripListingRequest.builder()
                .pagination(DEFAULT_PAGINATION)
                .sort(DEFAULT_SORT)
                .filter(filter)
                .build();
    }

    /**
     * Creates a TripListingRequest with custom pagination, sort, and filter
     */
    public static TripListingRequest createCustomTripListingRequest(Pagination pagination, Sort sort, TripListingRequest.Filter filter) {
        return TripListingRequest.builder()
                .pagination(pagination)
                .sort(sort)
                .filter(filter)
                .build();
    }

    /**
     * Creates a filter with trip statuses
     */
    public static TripListingRequest.Filter createTripStatusFilter(String... statuses) {
        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setTripStatuses(Arrays.asList(statuses));
        return filter;
    }

    /**
     * Creates a filter with trip statuses using enum values
     */
    public static TripListingRequest.Filter createTripStatusFilter(TripStatus... statuses) {
        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setTripStatuses(Arrays.stream(statuses).map(Enum::name).toList());
        return filter;
    }

    /**
     * Creates a filter with trip IDs
     */
    public static TripListingRequest.Filter createTripIdsFilter(String... tripIds) {
        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setTripIds(Arrays.asList(tripIds));
        return filter;
    }

    /**
     * Creates a filter with transport order IDs
     */
    public static TripListingRequest.Filter createTransportOrderIdsFilter(String... transportOrderIds) {
        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setTransportOrderIds(Arrays.asList(transportOrderIds));
        return filter;
    }

    /**
     * Creates a filter with customer order IDs
     */
    public static TripListingRequest.Filter createCustomerOrderIdsFilter(String... customerOrderIds) {
        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setCustomerOrderIds(Arrays.asList(customerOrderIds));
        return filter;
    }

    /**
     * Creates a filter with assignment identifiers
     */
    public static TripListingRequest.Filter createAssignmentIdentifiersFilter(String... identifiers) {
        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        TripListingRequest.Assignment assignment = new TripListingRequest.Assignment();
        assignment.setIdentifiers(Arrays.asList(identifiers));
        filter.setAssignment(assignment);
        return filter;
    }

    /**
     * Creates a filter with expected pickup date range
     */
    public static TripListingRequest.Filter createExpectedStartDateRangeFilter(Long fromDate, Long toDate) {
        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setExpectedStartDateRange(DateRange.builder().from(fromDate).to(toDate).build());
        return filter;
    }

    /**
     * Creates a filter with expected delivery date range
     */
    public static TripListingRequest.Filter createExpectedEndDateRangeFilter(Long fromDate, Long toDate) {
        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setExpectedEndDateRange(DateRange.builder().from(fromDate).to(toDate).build());
        return filter;
    }

    /**
     * Creates a filter with actual pickup date range
     */
    public static TripListingRequest.Filter createActualStartDateRangeFilter(Long fromDate, Long toDate) {
        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setActualStartDateRange(DateRange.builder().from(fromDate).to(toDate).build());
        return filter;
    }

    /**
     * Creates a filter with actual delivery date range
     */
    public static TripListingRequest.Filter createActualEndDateRangeFilter(Long fromDate, Long toDate) {
        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setActualEndDateRange(DateRange.builder().from(fromDate).to(toDate).build());
        return filter;
    }

    /**
     * Creates a filter with partial expected pickup date range (only from date)
     */
    public static TripListingRequest.Filter createExpectedStartFromDateFilter(Long fromDate) {
        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setExpectedStartDateRange(DateRange.builder().from(fromDate).build());
        return filter;
    }

    /**
     * Creates a filter with partial expected pickup date range (only to date)
     */
    public static TripListingRequest.Filter createExpectedStartToDateFilter(Long toDate) {
        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setExpectedStartDateRange(DateRange.builder().to(toDate).build());
        return filter;
    }

    /**
     * Creates DateRange with both from and to dates
     */
    public static DateRange createDateRange(Long from, Long to) {
        return DateRange.builder().from(from).to(to).build();
    }

    /**
     * Creates DateRange with only from date
     */
    public static DateRange createFromDateRange(Long from) {
        return DateRange.builder().from(from).build();
    }

    /**
     * Creates DateRange with only to date
     */
    public static DateRange createToDateRange(Long to) {
        return DateRange.builder().to(to).build();
    }

    /**
     * Creates a comprehensive filter with all common fields populated
     */
    public static TripListingRequest.Filter createComplexTripFilter() {
        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setTripIds(Arrays.asList("TRIP001", "TRIP002"));
        filter.setTripStatuses(Arrays.asList(TripStatus.CREATED.name(), TripStatus.IN_PROGRESS.name()));
        filter.setTransportOrderIds(Arrays.asList("TO001"));
        filter.setCustomerOrderIds(Arrays.asList("CO001"));
        TripListingRequest.Assignment assignment = new TripListingRequest.Assignment();
        assignment.setIdentifiers(Arrays.asList("VENDOR001"));
        filter.setAssignment(assignment);
        filter.setConsignmentIds(Arrays.asList("CONSIGNMENT001"));
        filter.setTransportOrderStatuses(Arrays.asList("ASSIGNED"));
        filter.setShipmentStatuses(Arrays.asList("ALLOCATED"));
        filter.setExpectedStartDateRange(DateRange.builder().from(1000L).to(1200L).build());
        filter.setExpectedEndDateRange(DateRange.builder().from(2000L).to(2200L).build());
        filter.setActualStartDateRange(DateRange.builder().from(1500L).to(1700L).build());
        filter.setActualEndDateRange(DateRange.builder().from(2500L).to(2700L).build());
        return filter;
    }

    /**
     * Creates a filter with empty collections (useful for testing empty filter behavior)
     */
    public static TripListingRequest.Filter createEmptyCollectionsTripFilter() {
        TripListingRequest.Filter filter = new TripListingRequest.Filter();
        filter.setTripIds(Collections.emptyList());
        filter.setTripStatuses(Collections.emptyList());
        filter.setTransportOrderIds(Collections.emptyList());
        filter.setCustomerOrderIds(Collections.emptyList());
        filter.setAssignment(null);
        return filter;
    }

    // ===== SHIPMENT LISTING REQUEST UTILITIES =====

    /**
     * Creates a ShipmentListingRequest with custom filter
     */
    public static ShipmentListingRequest createShipmentListingRequestWithFilter(ShipmentListingRequest.Filter filter) {
        return ShipmentListingRequest.builder()
                .pagination(DEFAULT_PAGINATION)
                .sort(DEFAULT_SORT)
                .filter(filter)
                .build();
    }

    /**
     * Creates a ShipmentListingRequest with custom pagination, sort, and filter
     */
    public static ShipmentListingRequest createCustomShipmentListingRequest(Pagination pagination, Sort sort, ShipmentListingRequest.Filter filter) {
        return ShipmentListingRequest.builder()
                .pagination(pagination)
                .sort(sort)
                .filter(filter)
                .build();
    }

    /**
     * Creates a comprehensive filter with multiple criteria for maximum coverage
     */
    public static ShipmentListingRequest.Filter createComprehensiveShipmentFilter(String uniqueId) {
        ShipmentListingRequest.Filter filter = new ShipmentListingRequest.Filter();
        filter.setShipmentIds(Arrays.asList("SHIPMENT_BASIC_1_" + uniqueId, "SHIPMENT_COMPLEX_1_" + uniqueId));
        filter.setTripIds(Arrays.asList("TRIP_BASIC_1_" + uniqueId));
        filter.setConsignmentIds(Arrays.asList("CONSIGNMENT_BASIC", "CONSIGNMENT_COMPLEX_1"));
        filter.setShipmentStatuses(Arrays.asList("ASSIGNED", "ALLOCATED"));
        filter.setTransportOrderIds(Arrays.asList("TO_BASIC_" + uniqueId));
        filter.setCustomerOrderIds(Arrays.asList("CO_BASIC", "CO_COMPLEX_1"));
        filter.setOriginLocationId("ORIGIN_BASIC");
        filter.setDestinationLocationId("DEST_BASIC");

        long currentTime = System.currentTimeMillis();
        DateRange pickupRange = new DateRange();
        pickupRange.setFrom(currentTime - 86400000L);
        pickupRange.setTo(currentTime + 86400000L);
        filter.setExpectedPickupDateRange(pickupRange);

        DateRange deliveryRange = new DateRange();
        deliveryRange.setFrom(currentTime - 43200000L);
        deliveryRange.setTo(currentTime + 43200000L);
        filter.setExpectedDeliveryDateRange(deliveryRange);

        return filter;
    }

    /**
     * Creates a filter with invalid data to test error handling
     */
    public static ShipmentListingRequest.Filter createInvalidDataShipmentFilter() {
        ShipmentListingRequest.Filter filter = new ShipmentListingRequest.Filter();
        filter.setShipmentIds(Arrays.asList("", null, "   ", "VALID_ID"));
        filter.setTripIds(Arrays.asList("", "VALID_TRIP"));
        filter.setConsignmentIds(Arrays.asList(null, "VALID_CONSIGNMENT"));
        filter.setShipmentStatuses(Arrays.asList("INVALID_STATUS", "ASSIGNED"));
        filter.setTransportOrderIds(Arrays.asList("   ", "VALID_TO"));
        filter.setCustomerOrderIds(Arrays.asList("", "VALID_CO"));
        filter.setOriginLocationId("VALID_ORIGIN");
        filter.setDestinationLocationId("VALID_DEST");
        return filter;
    }

    /**
     * Creates empty collections filter for testing empty filter behavior
     */
    public static ShipmentListingRequest.Filter createEmptyCollectionsShipmentFilter() {
        ShipmentListingRequest.Filter filter = new ShipmentListingRequest.Filter();
        filter.setShipmentIds(Collections.emptyList());
        filter.setTripIds(Collections.emptyList());
        filter.setConsignmentIds(Collections.emptyList());
        filter.setShipmentStatuses(Collections.emptyList());
        filter.setTransportOrderIds(Collections.emptyList());
        filter.setCustomerOrderIds(Collections.emptyList());
        return filter;
    }

    /**
     * Creates pagination with negative page number (for testing validation)
     */
    public static Pagination createNegativePagePagination() {
        return new Pagination(-1, 10);
    }

    /**
     * Creates pagination with negative page size (for testing validation)
     */
    public static Pagination createNegativePageSizePagination() {
        return new Pagination(0, -1);
    }

    /**
     * Creates pagination with excessive page size (for testing limits)
     */
    public static Pagination createExcessivePageSizePagination() {
        return new Pagination(0, 2000);
    }

    /**
     * Creates sort with invalid field (for testing validation)
     */
    public static Sort createInvalidFieldSort() {
        return new Sort("invalidField", "ASC");
    }

    /**
     * Creates sort with invalid order (for testing validation)
     */
    public static Sort createInvalidOrderSort() {
        return new Sort("code", "INVALID_ORDER");
    }


    public static StaticDataRequest createBasicStaticDataRequest() {
        return StaticDataRequest.builder()
                .entityTypes(Arrays.asList("TRIP_STATUS", "TASK_STATUS"))
                .build();
    }

    public static StaticDataRequest createSingleEntityTypeRequest(String entityType) {
        return StaticDataRequest.builder()
                .entityTypes(Collections.singletonList(entityType))
                .build();
    }

    public static StaticDataRequest createMultipleValidEntityTypesRequest() {
        return StaticDataRequest.builder()
                .entityTypes(Arrays.asList(
                        "TRIP_STATUS", "ASSIGNMENT_STATUS", "ASSIGNMENT_TYPE",
                        "TASK_STATUS", "SHIPMENT_STATUS"
                ))
                .build();
    }

    public static StaticDataRequest createAllSupportedEntityTypesRequest() {
        return StaticDataRequest.builder()
                .entityTypes(Arrays.asList(
                        "TRIP_STATUS", "ASSIGNMENT_STATUS", "ASSIGNMENT_TYPE",
                        "TASK_STATUS", "SHIPMENT_STATUS",
                        "TRANSPORT_ORDER_STATUS", "RESOURCE_ASSIGNMENT_TYPE",
                        "SHIPMENT_TASK_EVENT", "STOP_TASK_EVENT",
                        "EVENT_TYPE", "ATTACHMENT_TYPE", "KAFKA_INTEGRATOR_MESSAGE_EVENT_ACTION"
                ))
                .build();
    }

    public static StaticDataRequest createMixedValidInvalidEntityTypesRequest() {
        return StaticDataRequest.builder()
                .entityTypes(Arrays.asList(
                        "TRIP_STATUS", "INVALID_TYPE_1", "ASSIGNMENT_STATUS",
                        "INVALID_TYPE_2"
                ))
                .build();
    }

    public static StaticDataRequest createInvalidEntityTypesRequest() {
        return StaticDataRequest.builder()
                .entityTypes(Arrays.asList("INVALID_TYPE_1", "INVALID_TYPE_2", "UNKNOWN_TYPE"))
                .build();
    }

    public static StaticDataRequest createEmptyEntityTypesRequest() {
        return StaticDataRequest.builder()
                .entityTypes(Collections.emptyList())
                .build();
    }


    public static StaticDataRequest createDuplicateEntityTypesRequest() {
        return StaticDataRequest.builder()
                .entityTypes(Arrays.asList(
                        "TRIP_STATUS", "TRIP_STATUS", "ASSIGNMENT_STATUS",
                        "ASSIGNMENT_STATUS", "SHIPMENT_STATUS"
                ))
                .build();
    }

    public static StaticDataRequest createCaseInsensitiveEntityTypesRequest() {
        return StaticDataRequest.builder()
                .entityTypes(Arrays.asList(
                        "trip_status", "ASSIGNMENT_STATUS",
                        "task_STATUS", "SHIPMENT_status"
                ))
                .build();
    }

    public static StaticDataRequest createLargeEntityTypesRequest() {
        List<String> entityTypes = new ArrayList<>();
        // Add all supported types multiple times to create a larger request
        List<String> supportedTypes = Arrays.asList(
                "TRIP_STATUS", "ASSIGNMENT_STATUS", "ASSIGNMENT_TYPE",
                "TASK_STATUS", "SHIPMENT_STATUS",
                "TRANSPORT_ORDER_STATUS", "RESOURCE_ASSIGNMENT_TYPE"
        );

        for (int i = 0; i < 3; i++) {
            entityTypes.addAll(supportedTypes);
        }

        return StaticDataRequest.builder()
                .entityTypes(entityTypes)
                .build();
    }

    public static StaticDataRequest createSpecialCharacterEntityTypesRequest() {
        return StaticDataRequest.builder()
                .entityTypes(Arrays.asList(
                        "TRIP_STATUS", "ASSIGNMENT-STATUS", "STOP.STATUS",
                        "TASK@STATUS", "SHIPMENT#STATUS"
                ))
                .build();
    }

    public static StaticDataRequest createWhitespaceEntityTypesRequest() {
        return StaticDataRequest.builder()
                .entityTypes(Arrays.asList(
                        " TRIP_STATUS ", "ASSIGNMENT_STATUS",
                        "\tTASK_STATUS\t", "\nSHIPMENT_STATUS\n"
                ))
                .build();
    }

    public static StaticDataRequest createMinimalValidRequest() {
        return StaticDataRequest.builder()
                .entityTypes(Collections.singletonList("TRIP_STATUS"))
                .build();
    }

    public static StaticDataRequest createMaximalValidRequest() {
        return createAllSupportedEntityTypesRequest();
    }

    public static TaskInstanceRegistrationResponse createDummyTaskInstanceRegistrationResponse() {
        TaskInstanceRegistrationResponse response = new TaskInstanceRegistrationResponse();
        response.setTaskRegistrationCode("TRC" + faker.regexify("[0-9]{3}"));
        response.setExtTaskTransactionCode("ETC" + faker.regexify("[0-9]{3}"));
        response.setMessage("Mock registration successful");
        return response;
    }
    public static TaskInstanceDeRegistrationResponse createDummyTaskInstanceDeRegistrationResponse() {
        TaskInstanceDeRegistrationResponse response = new TaskInstanceDeRegistrationResponse();
        response.setTaskRegistrationCode("TRC" + faker.regexify("[0-9]{3}"));
        response.setExtTaskTransactionCode("ETC" + faker.regexify("[0-9]{3}"));
        response.setMessage("Mock registration successful");
        response.setMessage("Mock de-registration successful");
        return response;
    }
}
