package com.dpw.ctms.move.integration.adapter;

import com.dpw.ctms.move.BaseTest;
import com.dpw.ctms.move.constants.ResourceServiceConstants;
import com.dpw.ctms.move.dto.PaginationDTO;
import com.dpw.ctms.move.fakers.Fakers;
import com.dpw.ctms.move.integration.dto.resource.FacilityListRequestDTO;
import com.dpw.ctms.move.integration.dto.resource.ProductListRequestDTO;
import com.dpw.ctms.move.integration.dto.resource.UomListRequestDTO;
import com.dpw.ctms.move.integration.feignClient.ResourceClient;
import com.dpw.ctms.move.integration.request.resource.GetOperatorListRequest;
import com.dpw.ctms.move.integration.request.resource.ResourceListRequest;
import com.dpw.ctms.move.integration.response.ListResponse;
import com.dpw.ctms.move.integration.response.resource.ResourceApiResponse;
import com.dpw.ctms.move.integration.response.resource.facility.FacilityRecord;
import com.dpw.ctms.move.integration.response.resource.operator.GetOperatorDetailsListResponse;
import com.dpw.ctms.move.integration.response.resource.product.ProductRecord;
import com.dpw.ctms.move.integration.response.resource.uom.UomRecord;
import com.dpw.ctms.move.integration.response.resource.ResourceListResponseData;
import com.dpw.tmsutils.exception.GenericException;
import com.dpw.tmsutils.exception.TMSException;
import com.dpw.tmsutils.exception.TMSFeignException;
import org.springframework.http.HttpHeaders;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.ResponseEntity;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class ResourceServiceAdapterTest extends BaseTest {

    @Autowired
    private ResourceServiceAdapter resourceServiceAdapter;

    @MockBean
    private ResourceClient resourceClient;

    private FacilityListRequestDTO facilityRequestDTO;
    private UomListRequestDTO uomRequestDTO;
    private ProductListRequestDTO productRequestDTO;
    private GetOperatorListRequest operatorRequestDTO;

    @BeforeEach
    void setUp() {
        facilityRequestDTO = FacilityListRequestDTO.builder()
                .facilityCodes(Arrays.asList("FAC001", "FAC002"))
                .build();

        uomRequestDTO = UomListRequestDTO.builder()
                .ids(Arrays.asList("1", "2"))
                .build();
        
        productRequestDTO = ProductListRequestDTO.builder()
                .ids(Arrays.asList("101", "102"))
                .build();
        
        operatorRequestDTO = GetOperatorListRequest.builder()
                .filter(GetOperatorListRequest.GetOperatorListFilter.builder()
                        .ids(Arrays.asList(1001L, 1002L))
                        .build())
                .build();
    }

    @Test
    void shouldGetFacilityListSuccessfully() {
        
        FacilityRecord record1 = FacilityRecord.builder()
                .code("FAC001")
                .name("Facility 1")
                .build();

        FacilityRecord record2 = FacilityRecord.builder()
                .code("FAC002")
                .name("Facility 2")
                .build();

        ListResponse<FacilityRecord> listResponse = new ListResponse<>();
        listResponse.setRecords(Arrays.asList(record1, record2));
        listResponse.setTotalElements(2L);

        ResourceApiResponse<ListResponse<FacilityRecord>> apiResponse = new ResourceApiResponse<>();
        apiResponse.setData(listResponse);
       

        ResponseEntity<ResourceApiResponse<ListResponse<FacilityRecord>>> responseEntity = 
                ResponseEntity.ok(apiResponse);

        when(resourceClient.getFacilityList(any(ResourceListRequest.class)))
                .thenReturn(responseEntity);

        // When
        ListResponse<FacilityRecord> result = resourceServiceAdapter.getFacilityList(facilityRequestDTO, new PaginationDTO(0,10));

        // Then
        assertNotNull(result);
        assertEquals(2, result.getRecords().size());
        assertEquals("FAC001", result.getRecords().get(0).getCode());
        assertEquals("FAC002", result.getRecords().get(1).getCode());
        assertEquals(2L, result.getTotalElements());

        verify(resourceClient).getFacilityList(any(ResourceListRequest.class));
    }

    @Test
    void shouldGetUomListSuccessfully() {
        
        UomRecord record1 = UomRecord.builder()
                .id(1L)
                .name("Kilogram")
                .code("KG")
                .build();

        UomRecord record2 = UomRecord.builder()
                .id(2L)
                .name("Liter")
                .code("L")
                .build();

        ListResponse<UomRecord> listResponse = new ListResponse<>();
        listResponse.setRecords(Arrays.asList(record1, record2));
        listResponse.setTotalElements(2L);

        ResourceApiResponse<ListResponse<UomRecord>> apiResponse = new ResourceApiResponse<>();
        apiResponse.setData(listResponse);
       

        ResponseEntity<ResourceApiResponse<ListResponse<UomRecord>>> responseEntity = 
                ResponseEntity.ok(apiResponse);

        when(resourceClient.getUomList(any(ResourceListRequest.class)))
                .thenReturn(responseEntity);

        // When
        ListResponse<UomRecord> result = resourceServiceAdapter.getUomList(uomRequestDTO,  new PaginationDTO(0,10));

        // Then
        assertNotNull(result);
        assertEquals(2, result.getRecords().size());
        assertEquals("Kilogram", result.getRecords().get(0).getName());
        assertEquals("Liter", result.getRecords().get(1).getName());
        assertEquals(2L, result.getTotalElements());

        verify(resourceClient).getUomList(any(ResourceListRequest.class));
    }

    @Test
    void shouldHandleEmptyFacilityList() {
        
        ListResponse<FacilityRecord> emptyListResponse = new ListResponse<>();
        emptyListResponse.setRecords(Collections.emptyList());
        emptyListResponse.setTotalElements(0L);

        ResourceApiResponse<ListResponse<FacilityRecord>> apiResponse = new ResourceApiResponse<>();
        apiResponse.setData(emptyListResponse);
       

        ResponseEntity<ResourceApiResponse<ListResponse<FacilityRecord>>> responseEntity = 
                ResponseEntity.ok(apiResponse);

        when(resourceClient.getFacilityList(any(ResourceListRequest.class)))
                .thenReturn(responseEntity);

        // When
        ListResponse<FacilityRecord> result = resourceServiceAdapter.getFacilityList(facilityRequestDTO,  new PaginationDTO(0,10));

        // Then
        assertNotNull(result);
        assertTrue(result.getRecords().isEmpty());
        assertEquals(0L, result.getTotalElements());
    }

    @Test
    void shouldHandleEmptyUomList() {
        
        ListResponse<UomRecord> emptyListResponse = new ListResponse<>();
        emptyListResponse.setRecords(Collections.emptyList());
        emptyListResponse.setTotalElements(0L);

        ResourceApiResponse<ListResponse<UomRecord>> apiResponse = new ResourceApiResponse<>();
        apiResponse.setData(emptyListResponse);
       

        ResponseEntity<ResourceApiResponse<ListResponse<UomRecord>>> responseEntity = 
                ResponseEntity.ok(apiResponse);

        when(resourceClient.getUomList(any(ResourceListRequest.class)))
                .thenReturn(responseEntity);

        // When
        ListResponse<UomRecord> result = resourceServiceAdapter.getUomList(uomRequestDTO,  new PaginationDTO(0,10));

        // Then
        assertNotNull(result);
        assertTrue(result.getRecords().isEmpty());
        assertEquals(0L, result.getTotalElements());
    }

    @Test
    void shouldHandleNullResponseEntity() {
        
        when(resourceClient.getFacilityList(any(ResourceListRequest.class)))
                .thenReturn(null);
        
        ListResponse<FacilityRecord> result = resourceServiceAdapter.getFacilityList(facilityRequestDTO,  new PaginationDTO(0,10));
        
        assertNotNull(result);
        assertNull(result.getRecords());
    }
    
    @Test
    void shouldPropagateNotFoundExceptionCorrectly() {
        TMSFeignException notFoundException = new TMSFeignException(404, new HttpHeaders(), "Facility not found");

        when(resourceClient.getFacilityList(any(ResourceListRequest.class)))
                .thenThrow(notFoundException);

        
        TMSException exception = assertThrows(TMSException.class, () -> 
                resourceServiceAdapter.getFacilityList(facilityRequestDTO,  new PaginationDTO(0,10)));
        assertEquals(EXTERNAL_INVOCATION_EXCEPTION.name(), exception.getErrorCode());
    }

    @Test
    void shouldPropagateBadRequestExceptionCorrectly() {
        
        TMSFeignException badRequestException = new TMSFeignException(400, new HttpHeaders(), "Invalid facility request");

        when(resourceClient.getFacilityList(any(ResourceListRequest.class)))
                .thenThrow(badRequestException);

        
        TMSException exception = assertThrows(TMSException.class, () ->
                resourceServiceAdapter.getFacilityList(facilityRequestDTO, new PaginationDTO(0,10)));
        assertEquals(INVALID_REQUEST.name(), exception.getErrorCode());
    }

    @Test
    void shouldPropagateForbiddenExceptionCorrectly() {
        
        TMSFeignException forbiddenException = new TMSFeignException(403, new HttpHeaders(), "Access denied");

        when(resourceClient.getFacilityList(any(ResourceListRequest.class)))
                .thenThrow(forbiddenException);

        
        TMSException exception = assertThrows(TMSException.class, () -> 
                resourceServiceAdapter.getFacilityList(facilityRequestDTO,  new PaginationDTO(0,10)));
        assertEquals(INTEGRATION_ERROR.name(), exception.getErrorCode());
    }

    @Test
    void shouldPropagateServiceUnavailableExceptionCorrectly() {
        
        TMSFeignException serviceUnavailableException = new TMSFeignException(503, new HttpHeaders(), "Resource Service Unavailable");

        when(resourceClient.getFacilityList(any(ResourceListRequest.class)))
                .thenThrow(serviceUnavailableException);

        
        TMSException exception = assertThrows(TMSException.class, () -> 
                resourceServiceAdapter.getFacilityList(facilityRequestDTO,  new PaginationDTO(0,10)));
        assertEquals(EXTERNAL_INVOCATION_EXCEPTION.name(), exception.getErrorCode());
    }

    @Test
    void shouldPropagateInternalServerErrorExceptionCorrectly() {
        
        TMSFeignException internalServerErrorException = new TMSFeignException(500, new HttpHeaders(), "Internal Server Error");

        when(resourceClient.getUomList(any(ResourceListRequest.class)))
                .thenThrow(internalServerErrorException);

        
        TMSException exception = assertThrows(TMSException.class, () -> 
                resourceServiceAdapter.getUomList(uomRequestDTO,  new PaginationDTO(0,10)));
        assertEquals(INTERNAL_ERROR.name(), exception.getErrorCode());
    }

    @Test
    void shouldHandleUnexpectedExceptionCorrectly() {
        
        RuntimeException unexpectedException = new RuntimeException("Unexpected error");

        when(resourceClient.getFacilityList(any(ResourceListRequest.class)))
                .thenThrow(unexpectedException);

        
        assertThrows(GenericException.class, () ->
                resourceServiceAdapter.getFacilityList(facilityRequestDTO,  new PaginationDTO(0,10)));
    }

    @Test
    void shouldReturnCorrectServiceName() {
        // When
        String serviceName = resourceServiceAdapter.getServiceName();

        // Then
        assertEquals(ResourceServiceConstants.RESOURCE_SERVICE, serviceName);
    }

    @Test
    void shouldHandlePaginationCorrectlyForFacilities() {
        
        ListResponse<FacilityRecord> paginatedResponse = new ListResponse<>();
        paginatedResponse.setRecords(Collections.emptyList());
        paginatedResponse.setTotalElements(100L);

        ResourceApiResponse<ListResponse<FacilityRecord>> apiResponse = new ResourceApiResponse<>();
        apiResponse.setData(paginatedResponse);
       

        ResponseEntity<ResourceApiResponse<ListResponse<FacilityRecord>>> responseEntity = 
                ResponseEntity.ok(apiResponse);

        when(resourceClient.getFacilityList(any(ResourceListRequest.class)))
                .thenReturn(responseEntity);

        // When
        ListResponse<FacilityRecord> result = 
                resourceServiceAdapter.getFacilityList(facilityRequestDTO,  new PaginationDTO(3,25));

        // Then
        assertNotNull(result);
        assertEquals(100L, result.getTotalElements());
        verify(resourceClient).getFacilityList(argThat(request -> {
            // Verify pagination parameters are correctly passed to the request
            return request.getPagination().getPageNo() == 3 &&
                   request.getPagination().getPageSize() == 25;
        }));
    }

    @Test
    void shouldHandlePaginationCorrectlyForUoms() {
        
        ListResponse<UomRecord> paginatedResponse = new ListResponse<>();
        paginatedResponse.setRecords(Collections.emptyList());
        paginatedResponse.setTotalElements(50L);

        ResourceApiResponse<ListResponse<UomRecord>> apiResponse = new ResourceApiResponse<>();
        apiResponse.setData(paginatedResponse);
       

        ResponseEntity<ResourceApiResponse<ListResponse<UomRecord>>> responseEntity = 
                ResponseEntity.ok(apiResponse);

        when(resourceClient.getUomList(any(ResourceListRequest.class)))
                .thenReturn(responseEntity);

        ListResponse<UomRecord> result = 
                resourceServiceAdapter.getUomList(uomRequestDTO,  new PaginationDTO(1,15));
        assertNotNull(result);
        assertEquals(50L, result.getTotalElements());
        verify(resourceClient).getUomList(argThat(request -> {
            return request.getPagination().getPageNo() == 1 &&
                   request.getPagination().getPageSize() == 15;
        }));
    }

    @Test
    void shouldCreateCorrectFacilityRequestStructure() {
        
        ListResponse<FacilityRecord> response = Fakers.createFacilityResponse();
        
        ResourceApiResponse<ListResponse<FacilityRecord>> apiResponse = new ResourceApiResponse<>();
        apiResponse.setData(response);
       

        ResponseEntity<ResourceApiResponse<ListResponse<FacilityRecord>>> responseEntity = 
                ResponseEntity.ok(apiResponse);

        when(resourceClient.getFacilityList(any(ResourceListRequest.class)))
                .thenReturn(responseEntity);

        resourceServiceAdapter.getFacilityList(facilityRequestDTO,  new PaginationDTO(0,10));

        verify(resourceClient).getFacilityList(argThat(request -> {
            assertNotNull(request.getFilter());
            assertNotNull(request.getPagination());
            assertTrue(request.getFilter().getIds().containsAll(facilityRequestDTO.getFacilityCodes()));
            return true;
        }));
    }

    @Test
    void shouldCreateCorrectUomRequestStructure() {
        
        ListResponse<UomRecord> response = Fakers.createUomResponse();
        
        ResourceApiResponse<ListResponse<UomRecord>> apiResponse = new ResourceApiResponse<>();
        apiResponse.setData(response);
       

        ResponseEntity<ResourceApiResponse<ListResponse<UomRecord>>> responseEntity = 
                ResponseEntity.ok(apiResponse);

        when(resourceClient.getUomList(any(ResourceListRequest.class)))
                .thenReturn(responseEntity);

        // When
        resourceServiceAdapter.getUomList(uomRequestDTO,  new PaginationDTO(0,10));

        // Then
        verify(resourceClient).getUomList(argThat(request -> {
            // Verify the request structure
            assertNotNull(request.getFilter());
            assertNotNull(request.getPagination());
            assertTrue(request.getFilter().getIds().containsAll(uomRequestDTO.getIds()));
            return true;
        }));
    }

    @Test
    void shouldHandleEmptyFacilityCodesList() {
        
        FacilityListRequestDTO emptyRequest = FacilityListRequestDTO.builder()
                .facilityCodes(Collections.emptyList())
                .build();

        ListResponse<FacilityRecord> emptyResponse = Fakers.createEmptyFacilityResponse();
        
        ResourceApiResponse<ListResponse<FacilityRecord>> apiResponse = new ResourceApiResponse<>();
        apiResponse.setData(emptyResponse);
       

        ResponseEntity<ResourceApiResponse<ListResponse<FacilityRecord>>> responseEntity = 
                ResponseEntity.ok(apiResponse);

        when(resourceClient.getFacilityList(any(ResourceListRequest.class)))
                .thenReturn(responseEntity);

        // When
        ListResponse<FacilityRecord> result = 
                resourceServiceAdapter.getFacilityList(emptyRequest,  new PaginationDTO(0,10));

        // Then
        assertNotNull(result);
        assertTrue(result.getRecords().isEmpty());
    }

    @Test
    void shouldHandleEmptyUomIdsList() {
        
        UomListRequestDTO emptyRequest = UomListRequestDTO.builder()
                .ids(Collections.emptyList())
                .build();

        ListResponse<UomRecord> emptyResponse = Fakers.createUomResponse();
        
        ResourceApiResponse<ListResponse<UomRecord>> apiResponse = new ResourceApiResponse<>();
        apiResponse.setData(emptyResponse);
       

        ResponseEntity<ResourceApiResponse<ListResponse<UomRecord>>> responseEntity = 
                ResponseEntity.ok(apiResponse);

        when(resourceClient.getUomList(any(ResourceListRequest.class)))
                .thenReturn(responseEntity);

        // When
        ListResponse<UomRecord> result = 
                resourceServiceAdapter.getUomList(emptyRequest,  new PaginationDTO(0,10));

        // Then
        assertNotNull(result);
        assertNotNull(result.getRecords());
    }

    @Test
    void shouldHandleNullFacilityCodesList() {
        
        FacilityListRequestDTO nullCodesRequest = FacilityListRequestDTO.builder()
                .facilityCodes(null)
                .build();

        ListResponse<FacilityRecord> emptyResponse = Fakers.createEmptyFacilityResponse();
        
        ResourceApiResponse<ListResponse<FacilityRecord>> apiResponse = new ResourceApiResponse<>();
        apiResponse.setData(emptyResponse);
       

        ResponseEntity<ResourceApiResponse<ListResponse<FacilityRecord>>> responseEntity = 
                ResponseEntity.ok(apiResponse);

        when(resourceClient.getFacilityList(any(ResourceListRequest.class)))
                .thenReturn(responseEntity);

        // When
        ListResponse<FacilityRecord> result = 
                resourceServiceAdapter.getFacilityList(nullCodesRequest,  new PaginationDTO(0,10));

        // Then
        assertNotNull(result);
        assertTrue(result.getRecords().isEmpty());
    }

    @Test
    void shouldHandleNullUomIdsList() {
        
        UomListRequestDTO nullIdsRequest = UomListRequestDTO.builder()
                .ids(null)
                .build();

        ListResponse<UomRecord> emptyResponse = Fakers.createUomResponse();
        
        ResourceApiResponse<ListResponse<UomRecord>> apiResponse = new ResourceApiResponse<>();
        apiResponse.setData(emptyResponse);
       

        ResponseEntity<ResourceApiResponse<ListResponse<UomRecord>>> responseEntity = 
                ResponseEntity.ok(apiResponse);

        when(resourceClient.getUomList(any(ResourceListRequest.class)))
                .thenReturn(responseEntity);

        // When
        ListResponse<UomRecord> result = 
                resourceServiceAdapter.getUomList(nullIdsRequest,  new PaginationDTO(0,10));

        // Then
        assertNotNull(result);
        assertNotNull(result.getRecords());
    }

    @Test
    void shouldHandleLargeFacilityListRequest() {
        
        FacilityListRequestDTO largeRequest = FacilityListRequestDTO.builder()
                .facilityCodes(Collections.nCopies(100, "FAC"))
                .build();

        ListResponse<FacilityRecord> largeResponse = new ListResponse<>();
        largeResponse.setRecords(Collections.emptyList());
        largeResponse.setTotalElements(100L);

        ResourceApiResponse<ListResponse<FacilityRecord>> apiResponse = new ResourceApiResponse<>();
        apiResponse.setData(largeResponse);
       

        ResponseEntity<ResourceApiResponse<ListResponse<FacilityRecord>>> responseEntity = 
                ResponseEntity.ok(apiResponse);

        when(resourceClient.getFacilityList(any(ResourceListRequest.class)))
                .thenReturn(responseEntity);

        // When
        ListResponse<FacilityRecord> result = 
                resourceServiceAdapter.getFacilityList(largeRequest,  new PaginationDTO(0,10));

        // Then
        assertNotNull(result);
        assertEquals(100L, result.getTotalElements());
        verify(resourceClient).getFacilityList(any(ResourceListRequest.class));
    }

    @Test
    void shouldGetProductListSuccessfully() {
        
        ProductRecord record1 = ProductRecord.builder()
                .id(101L)
                .name("Product 1")
                .code("P001")
                .build();

        ProductRecord record2 = ProductRecord.builder()
                .id(102L)
                .name("Product 2")
                .code("P002")
                .build();

        ListResponse<ProductRecord> listResponse = new ListResponse<>();
        listResponse.setRecords(Arrays.asList(record1, record2));
        listResponse.setTotalElements(2L);

        ResourceApiResponse<ListResponse<ProductRecord>> apiResponse = new ResourceApiResponse<>();
        apiResponse.setData(listResponse);

        ResponseEntity<ResourceApiResponse<ListResponse<ProductRecord>>> responseEntity = 
                ResponseEntity.ok(apiResponse);

        when(resourceClient.getProductList(any(ResourceListRequest.class)))
                .thenReturn(responseEntity);

        // When
        ListResponse<ProductRecord> result = resourceServiceAdapter.getProductList(productRequestDTO, new PaginationDTO(0,10));

        // Then
        assertNotNull(result);
        assertEquals(2, result.getRecords().size());
        assertEquals("Product 1", result.getRecords().get(0).getName());
        assertEquals("Product 2", result.getRecords().get(1).getName());
        assertEquals(2L, result.getTotalElements());

        verify(resourceClient).getProductList(any(ResourceListRequest.class));
    }

    @Test
    void shouldHandleEmptyProductList() {
        
        ListResponse<ProductRecord> emptyListResponse = new ListResponse<>();
        emptyListResponse.setRecords(Collections.emptyList());
        emptyListResponse.setTotalElements(0L);

        ResourceApiResponse<ListResponse<ProductRecord>> apiResponse = new ResourceApiResponse<>();
        apiResponse.setData(emptyListResponse);

        ResponseEntity<ResourceApiResponse<ListResponse<ProductRecord>>> responseEntity = 
                ResponseEntity.ok(apiResponse);

        when(resourceClient.getProductList(any(ResourceListRequest.class)))
                .thenReturn(responseEntity);

        // When
        ListResponse<ProductRecord> result = resourceServiceAdapter.getProductList(productRequestDTO, new PaginationDTO(0,10));

        // Then
        assertNotNull(result);
        assertTrue(result.getRecords().isEmpty());
        assertEquals(0L, result.getTotalElements());
    }

    @Test
    void shouldGetOperatorDetailsListSuccessfully() {
        
        GetOperatorDetailsListResponse operator1 = GetOperatorDetailsListResponse.builder()
                .id(1001L)
                .code("OP001")
                .firstName("John")
                .lastName("Doe")
                .employeeNumber("EMP001")
                .build();

        GetOperatorDetailsListResponse operator2 = GetOperatorDetailsListResponse.builder()
                .id(1002L)
                .code("OP002")
                .firstName("Jane")
                .lastName("Smith")
                .employeeNumber("EMP002")
                .build();

        ResourceListResponseData<GetOperatorDetailsListResponse> listResponse = new ResourceListResponseData<>();
        listResponse.setRecords(Arrays.asList(operator1, operator2));
        listResponse.setTotalElements(2L);

        ResourceApiResponse<ResourceListResponseData<GetOperatorDetailsListResponse>> apiResponse = new ResourceApiResponse<>();
        apiResponse.setData(listResponse);

        ResponseEntity<ResourceApiResponse<ResourceListResponseData<GetOperatorDetailsListResponse>>> responseEntity = 
                ResponseEntity.ok(apiResponse);

        when(resourceClient.getOperatorDetailsList(any(GetOperatorListRequest.class)))
                .thenReturn(responseEntity);

        // When
        List<GetOperatorDetailsListResponse> result = resourceServiceAdapter.getOperatorDetailsList(operatorRequestDTO);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("John", result.get(0).getFirstName());
        assertEquals("Jane", result.get(1).getFirstName());
        assertEquals("OP001", result.get(0).getCode());
        assertEquals("OP002", result.get(1).getCode());

        verify(resourceClient).getOperatorDetailsList(any(GetOperatorListRequest.class));
    }

    @Test
    void shouldHandleEmptyOperatorDetailsList() {
        
        ResourceListResponseData<GetOperatorDetailsListResponse> emptyListResponse = new ResourceListResponseData<>();
        emptyListResponse.setRecords(Collections.emptyList());
        emptyListResponse.setTotalElements(0L);

        ResourceApiResponse<ResourceListResponseData<GetOperatorDetailsListResponse>> apiResponse = new ResourceApiResponse<>();
        apiResponse.setData(emptyListResponse);

        ResponseEntity<ResourceApiResponse<ResourceListResponseData<GetOperatorDetailsListResponse>>> responseEntity = 
                ResponseEntity.ok(apiResponse);

        when(resourceClient.getOperatorDetailsList(any(GetOperatorListRequest.class)))
                .thenReturn(responseEntity);

        // When
        List<GetOperatorDetailsListResponse> result = resourceServiceAdapter.getOperatorDetailsList(operatorRequestDTO);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void shouldGetOperatorDetailsMapSuccessfully() {
        
        GetOperatorDetailsListResponse operator1 = GetOperatorDetailsListResponse.builder()
                .id(1001L)
                .code("OP001")
                .firstName("John")
                .lastName("Doe")
                .build();

        GetOperatorDetailsListResponse operator2 = GetOperatorDetailsListResponse.builder()
                .id(1002L)
                .code("OP002")
                .firstName("Jane")
                .lastName("Smith")
                .build();

        ResourceListResponseData<GetOperatorDetailsListResponse> listResponse = new ResourceListResponseData<>();
        listResponse.setRecords(Arrays.asList(operator1, operator2));
        listResponse.setTotalElements(2L);

        ResourceApiResponse<ResourceListResponseData<GetOperatorDetailsListResponse>> apiResponse = new ResourceApiResponse<>();
        apiResponse.setData(listResponse);

        ResponseEntity<ResourceApiResponse<ResourceListResponseData<GetOperatorDetailsListResponse>>> responseEntity = 
                ResponseEntity.ok(apiResponse);

        when(resourceClient.getOperatorDetailsList(any(GetOperatorListRequest.class)))
                .thenReturn(responseEntity);

        // When
        Map<String, GetOperatorDetailsListResponse> result = resourceServiceAdapter.getOperatorDetailsMap(operatorRequestDTO);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.containsKey("OP001"));
        assertTrue(result.containsKey("OP002"));
        assertEquals("John", result.get("OP001").getFirstName());
        assertEquals("Jane", result.get("OP002").getFirstName());

        verify(resourceClient).getOperatorDetailsList(any(GetOperatorListRequest.class));
    }

    @Test
    void shouldHandleEmptyOperatorDetailsMap() {
        
        ResourceListResponseData<GetOperatorDetailsListResponse> emptyListResponse = new ResourceListResponseData<>();
        emptyListResponse.setRecords(Collections.emptyList());
        emptyListResponse.setTotalElements(0L);

        ResourceApiResponse<ResourceListResponseData<GetOperatorDetailsListResponse>> apiResponse = new ResourceApiResponse<>();
        apiResponse.setData(emptyListResponse);

        ResponseEntity<ResourceApiResponse<ResourceListResponseData<GetOperatorDetailsListResponse>>> responseEntity = 
                ResponseEntity.ok(apiResponse);

        when(resourceClient.getOperatorDetailsList(any(GetOperatorListRequest.class)))
                .thenReturn(responseEntity);

        // When
        Map<String, GetOperatorDetailsListResponse> result = resourceServiceAdapter.getOperatorDetailsMap(operatorRequestDTO);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void shouldPropagateProductListExceptionCorrectly() {
        
        TMSFeignException feignException = new TMSFeignException(500, new HttpHeaders(), "Product service error");

        when(resourceClient.getProductList(any(ResourceListRequest.class)))
                .thenThrow(feignException);

        // When/Then
        TMSException exception = assertThrows(TMSException.class, () ->
                resourceServiceAdapter.getProductList(productRequestDTO, new PaginationDTO(0,10)));
        assertEquals(INTERNAL_ERROR.name(), exception.getErrorCode());
    }

    @Test
    void shouldPropagateOperatorDetailsListExceptionCorrectly() {
        
        TMSFeignException feignException = new TMSFeignException(404, new HttpHeaders(), "Operator not found");

        when(resourceClient.getOperatorDetailsList(any(GetOperatorListRequest.class)))
                .thenThrow(feignException);

        // When/Then
        TMSException exception = assertThrows(TMSException.class, () ->
                resourceServiceAdapter.getOperatorDetailsList(operatorRequestDTO));
        assertEquals(EXTERNAL_INVOCATION_EXCEPTION.name(), exception.getErrorCode());
    }
}