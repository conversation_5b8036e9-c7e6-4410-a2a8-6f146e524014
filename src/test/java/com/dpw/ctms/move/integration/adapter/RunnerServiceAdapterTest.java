package com.dpw.ctms.move.integration.adapter;

import com.dpw.ctms.move.constants.RunnerServiceConstants;
import com.dpw.ctms.move.integration.feignClient.RunnerClient;
import com.dpw.ctms.move.integration.request.runner.RunnerDocumentRequest;
import com.dpw.ctms.move.integration.response.runner.RunnerDocumentResponse;
import com.dpw.tmsutils.exception.GenericException;
import com.dpw.tmsutils.exception.TMSException;
import com.dpw.tmsutils.exception.TMSFeignException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class RunnerServiceAdapterTest {

    @InjectMocks
    private RunnerServiceAdapter runnerServiceAdapter;

    @Mock
    private RunnerClient runnerClient;

    @Test
    void sendPodDocument_success() {
        RunnerDocumentRequest request = Mockito.mock(RunnerDocumentRequest.class);
        RunnerDocumentResponse response = Mockito.mock(RunnerDocumentResponse.class);
        when(runnerClient.sendPodDocument(any())).thenReturn(response);

        RunnerDocumentResponse result = runnerServiceAdapter.sendPodDocument(request);
        
        assertSame(response, result);
        verify(runnerClient).sendPodDocument(eq(request));
    }

    @Test
    void sendPodDocument_notFound() {
        RunnerDocumentRequest request = Mockito.mock(RunnerDocumentRequest.class);
        TMSFeignException ex = Mockito.mock(TMSFeignException.class);
        when(ex.getStatus()).thenReturn(HttpStatus.NOT_FOUND.value());
        when(runnerClient.sendPodDocument(any())).thenThrow(ex);
        
        assertThrows(TMSException.class, () -> runnerServiceAdapter.sendPodDocument(request));
    }

    @Test
    void sendPodDocument_badRequest() {
        RunnerDocumentRequest request = Mockito.mock(RunnerDocumentRequest.class);
        TMSFeignException ex = Mockito.mock(TMSFeignException.class);
        when(ex.getStatus()).thenReturn(HttpStatus.BAD_REQUEST.value());
        when(runnerClient.sendPodDocument(any())).thenThrow(ex);
        
        assertThrows(TMSException.class, () -> runnerServiceAdapter.sendPodDocument(request));
    }

    @Test
    void sendPodDocument_forbidden() {
        RunnerDocumentRequest request = Mockito.mock(RunnerDocumentRequest.class);
        TMSFeignException ex = Mockito.mock(TMSFeignException.class);
        when(ex.getStatus()).thenReturn(HttpStatus.FORBIDDEN.value());
        when(runnerClient.sendPodDocument(any())).thenThrow(ex);
        
        assertThrows(TMSException.class, () -> runnerServiceAdapter.sendPodDocument(request));
    }

    @Test
    void sendPodDocument_serviceUnavailable() {
        RunnerDocumentRequest request = Mockito.mock(RunnerDocumentRequest.class);
        TMSFeignException ex = Mockito.mock(TMSFeignException.class);
        when(ex.getStatus()).thenReturn(HttpStatus.SERVICE_UNAVAILABLE.value());
        when(runnerClient.sendPodDocument(any())).thenThrow(ex);
        
        assertThrows(TMSException.class, () -> runnerServiceAdapter.sendPodDocument(request));
    }

    @Test
    void sendPodDocument_serverError() {
        RunnerDocumentRequest request = Mockito.mock(RunnerDocumentRequest.class);
        TMSFeignException ex = Mockito.mock(TMSFeignException.class);
        when(ex.getStatus()).thenReturn(HttpStatus.INTERNAL_SERVER_ERROR.value());
        when(runnerClient.sendPodDocument(any())).thenThrow(ex);
        
        assertThrows(TMSException.class, () -> runnerServiceAdapter.sendPodDocument(request));
    }

    @Test
    void sendPodDocument_unexpectedException() {
        RunnerDocumentRequest request = Mockito.mock(RunnerDocumentRequest.class);
        RuntimeException ex = new RuntimeException("Unexpected error");
        when(runnerClient.sendPodDocument(any())).thenThrow(ex);
        
        assertThrows(GenericException.class, () -> runnerServiceAdapter.sendPodDocument(request));
    }

    @Test
    void getServiceName_returnsCorrectName() {
        String serviceName = runnerServiceAdapter.getServiceName();
        assertEquals(RunnerServiceConstants.RUNNER_SERVICE, serviceName);
    }

}