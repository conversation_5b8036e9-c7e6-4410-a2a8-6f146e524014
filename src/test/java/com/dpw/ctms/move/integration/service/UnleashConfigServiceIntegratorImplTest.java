package com.dpw.ctms.move.integration.service;

import com.dpw.ctms.move.config.ConfigService;
import com.dpw.ctms.move.enums.Tenant;
import com.dpw.ctms.move.integration.service.impl.UnleashConfigServiceIntegratorImpl;
import com.dpw.tmsutils.threadlocal.TenantContext;
import com.dpw.tmsutils.utils.ObjectMapperUtil;
import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class UnleashConfigServiceIntegratorImplTest {

    @Mock
    private ConfigService configService;

    private UnleashConfigServiceIntegratorImpl integrator;

    @BeforeEach
    void setUp() {
        integrator = new UnleashConfigServiceIntegratorImpl(configService);
    }

    @Test
    void shouldReturnConfigJsonNode_WhenFetchConfigIsCalled() {
        // given
        String configKey = "vehicle_operator_role";
        String tenant = "CFR";
        Tenant expectedTenant = Tenant.valueOf(tenant);

        JsonNode expectedNode = ObjectMapperUtil.getObjectMapper().createObjectNode().put("key", "value");

        try (MockedStatic<TenantContext> mockedTenantContext = mockStatic(TenantContext.class)) {
            mockedTenantContext.when(TenantContext::getCurrentTenant).thenReturn(tenant);
            when(configService.getConfig(configKey, expectedTenant)).thenReturn(expectedNode);

            // when
            JsonNode result = integrator.fetchConfig(configKey);

            // then
            Assertions.assertNotNull(result);
            Assertions.assertEquals("value", result.get("key").asText());
            verify(configService).getConfig(configKey, expectedTenant);
        }
    }
}

