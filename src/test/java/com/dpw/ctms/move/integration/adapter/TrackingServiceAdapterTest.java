package com.dpw.ctms.move.integration.adapter;


import com.dpw.ctms.move.integration.feignClient.TrackingServiceClient;
import com.dpw.ctms.move.integration.request.trackingservice.TripTrackingRequest;
import com.dpw.ctms.move.integration.response.trackingservice.TripTrackingResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;

import org.springframework.test.util.ReflectionTestUtils;

class TrackingServiceAdapterTest {

    private TrackingServiceClient trackingServiceClient;
    private TrackingServiceAdapter trackingServiceAdapter;

    @BeforeEach
    void setUp() {
        trackingServiceClient = mock(TrackingServiceClient.class);
        trackingServiceAdapter = new TrackingServiceAdapter(trackingServiceClient);
        ReflectionTestUtils.setField(trackingServiceAdapter, "trackingServicePathVariable", "mock-path-var");
    }

    @Test
    void testTrackTrip_success() throws Exception {
        // Given
        TripTrackingRequest request = new TripTrackingRequest();
        TripTrackingResponse expectedResponse = new TripTrackingResponse();
        expectedResponse.setCode("200");
        expectedResponse.setStatus("success");

        String jsonResponse = new ObjectMapper().writeValueAsString(expectedResponse);

        // Mock the client call
        when(trackingServiceClient.trackTrip(eq("mock-path-var"), eq(request)))
                .thenReturn(jsonResponse);

        // When
        TripTrackingResponse actualResponse = trackingServiceAdapter.trackTrip(request);

        // Then
        assertNotNull(actualResponse);
        assertEquals("200", actualResponse.getCode());
        assertEquals("success", actualResponse.getStatus());
        verify(trackingServiceClient, times(1)).trackTrip("mock-path-var", request);
    }
}

