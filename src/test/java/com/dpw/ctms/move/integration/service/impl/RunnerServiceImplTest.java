package com.dpw.ctms.move.integration.service.impl;

import com.dpw.ctms.move.dto.CustomerOrderDTO;
import com.dpw.ctms.move.dto.CustomerOrderMetaDataDTO;
import com.dpw.ctms.move.dto.PaginationDTO;
import com.dpw.ctms.move.entity.Document;
import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.enums.DocumentOperationType;
import com.dpw.ctms.move.enums.DocumentStatus;
import com.dpw.ctms.move.enums.DocumentType;
import com.dpw.ctms.move.enums.EntityType;
import com.dpw.ctms.move.enums.ShipmentStatus;
import com.dpw.ctms.move.fakers.Fakers;
import com.dpw.ctms.move.integration.adapter.OmsServiceAdapter;
import com.dpw.ctms.move.integration.adapter.RunnerServiceAdapter;
import com.dpw.ctms.move.integration.mapper.oms.OmsCustomerOrderMapper;
import com.dpw.ctms.move.integration.request.runner.RunnerDocumentRequest;
import com.dpw.ctms.move.integration.response.oms.OmsListResponse;
import com.dpw.ctms.move.integration.response.oms.customerorder.CustomerOrderRecord;
import com.dpw.ctms.move.integration.response.runner.RunnerDocumentResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class RunnerServiceImplTest {

    @Mock
    private RunnerServiceAdapter runnerServiceAdapter;

    @Mock
    private OmsServiceAdapter omsServiceAdapter;

    @Mock
    private OmsCustomerOrderMapper omsCustomerOrderMapper;

    @InjectMocks
    private RunnerServiceImpl runnerService;

    private Document document;
    private List<Shipment> shipments;
    private CustomerOrderDTO customerOrderDTO;
    private CustomerOrderRecord customerOrderRecord;

    @BeforeEach
    void setUp() {
        document = Fakers.createDocument("file-123", DocumentStatus.ACTIVE, "SHIP-001", EntityType.SHIPMENT.name());
        document.setDocumentType(DocumentType.POD);
        document.setDocumentOperationType(DocumentOperationType.UPLOAD);
        document.setFileName("pod-document.pdf");
        document.setPresignedDownloadUrl("https://example.com/download/file-123");

        Shipment shipment1 = Fakers.createShipment("SHIP-001", ShipmentStatus.ASSIGNED);
        shipment1.setExternalCustomerOrderId("12345");
        
        Shipment shipment2 = Fakers.createShipment("SHIP-002", ShipmentStatus.ASSIGNED);
        shipment2.setExternalCustomerOrderId("67890");
        
        shipments = Arrays.asList(shipment1, shipment2);

        CustomerOrderMetaDataDTO metadata = CustomerOrderMetaDataDTO.builder()
                .customerOrderNumber("CO-001")
                .internalReferenceNumber("INT-REF-001")
                .build();

        customerOrderDTO = CustomerOrderDTO.builder()
                .id(12345L)
                .metadata(metadata)
                .build();

        customerOrderRecord = new CustomerOrderRecord();
    }

    @Test
    void sendDocumentToRunner_shouldSendDocumentSuccessfully() {
        // Given
        OmsListResponse<CustomerOrderRecord> omsResponse = new OmsListResponse<>();
        omsResponse.setResponse(Collections.singletonList(customerOrderRecord));

        when(omsServiceAdapter.getCustomerOrdersByIds(anyList(), any(PaginationDTO.class)))
                .thenReturn(omsResponse);
        when(omsCustomerOrderMapper.mapToCustomerOrderDTO(customerOrderRecord))
                .thenReturn(customerOrderDTO);

        RunnerDocumentResponse runnerResponse = RunnerDocumentResponse.builder()
                .status("SUCCESS")
                .message("Document uploaded successfully")
                .build();
        when(runnerServiceAdapter.sendPodDocument(any(RunnerDocumentRequest.class)))
                .thenReturn(runnerResponse);

        // When
        runnerService.sendDocumentToRunner(document, shipments.subList(0, 1));

        // Then
        verify(omsServiceAdapter).getCustomerOrdersByIds(eq(Arrays.asList(12345L)), any(PaginationDTO.class));
        verify(omsCustomerOrderMapper).mapToCustomerOrderDTO(customerOrderRecord);
        verify(runnerServiceAdapter).sendPodDocument(argThat(request ->
                request.getFileName().equals("pod-document.pdf") &&
                request.getFileDownloadLink().equals("https://example.com/download/file-123") &&
                request.getShipmentId().equals("CO-001") &&
                request.getShipmentReferenceNumber().equals("INT-REF-001")
        ));
    }

    @Test
    void sendDocumentToRunner_shouldSkipShipmentsWithoutExternalCustomerOrderId() {
        // Given
        Shipment shipmentWithoutOrderId = Fakers.createShipment("SHIP-003", ShipmentStatus.ASSIGNED);
        shipmentWithoutOrderId.setExternalCustomerOrderId(""); // Empty order ID
        
        List<Shipment> shipmentsWithEmpty = Arrays.asList(shipments.get(0), shipmentWithoutOrderId);

        OmsListResponse<CustomerOrderRecord> omsResponse = new OmsListResponse<>();
        omsResponse.setResponse(Collections.singletonList(customerOrderRecord));

        when(omsServiceAdapter.getCustomerOrdersByIds(anyList(), any(PaginationDTO.class)))
                .thenReturn(omsResponse);
        when(omsCustomerOrderMapper.mapToCustomerOrderDTO(customerOrderRecord))
                .thenReturn(customerOrderDTO);

        RunnerDocumentResponse runnerResponse = RunnerDocumentResponse.builder()
                .status("SUCCESS")
                .build();
        when(runnerServiceAdapter.sendPodDocument(any(RunnerDocumentRequest.class)))
                .thenReturn(runnerResponse);

        // When
        runnerService.sendDocumentToRunner(document, shipmentsWithEmpty);

        // Then
        verify(omsServiceAdapter).getCustomerOrdersByIds(eq(Arrays.asList(12345L)), any(PaginationDTO.class));
        verify(runnerServiceAdapter, times(1)).sendPodDocument(any(RunnerDocumentRequest.class));
    }

    @Test
    void sendDocumentToRunner_shouldHandleEmptyCustomerOrdersGracefully() {
        // Given
        OmsListResponse<CustomerOrderRecord> emptyResponse = new OmsListResponse<>();
        emptyResponse.setResponse(Collections.emptyList());

        when(omsServiceAdapter.getCustomerOrdersByIds(anyList(), any(PaginationDTO.class)))
                .thenReturn(emptyResponse);

        // When
        runnerService.sendDocumentToRunner(document, shipments.subList(0, 1));

        // Then
        verify(omsServiceAdapter).getCustomerOrdersByIds(anyList(), any(PaginationDTO.class));
        verify(runnerServiceAdapter, never()).sendPodDocument(any(RunnerDocumentRequest.class));
    }

    @Test
    void sendDocumentToRunner_shouldHandleOmsServiceException() {
        // Given
        when(omsServiceAdapter.getCustomerOrdersByIds(anyList(), any(PaginationDTO.class)))
                .thenThrow(new RuntimeException("OMS service unavailable"));

        // When & Then - Should not throw exception
        assertDoesNotThrow(() -> runnerService.sendDocumentToRunner(document, shipments.subList(0, 1)));

        verify(omsServiceAdapter).getCustomerOrdersByIds(anyList(), any(PaginationDTO.class));
        verify(runnerServiceAdapter, never()).sendPodDocument(any(RunnerDocumentRequest.class));
    }

    @Test
    void sendDocumentToRunner_shouldHandleRunnerServiceException() {
        // Given
        OmsListResponse<CustomerOrderRecord> omsResponse = new OmsListResponse<>();
        omsResponse.setResponse(Collections.singletonList(customerOrderRecord));

        when(omsServiceAdapter.getCustomerOrdersByIds(anyList(), any(PaginationDTO.class)))
                .thenReturn(omsResponse);
        when(omsCustomerOrderMapper.mapToCustomerOrderDTO(customerOrderRecord))
                .thenReturn(customerOrderDTO);
        when(runnerServiceAdapter.sendPodDocument(any(RunnerDocumentRequest.class)))
                .thenThrow(new RuntimeException("Runner service unavailable"));

        // When & Then - Should not throw exception
        assertDoesNotThrow(() -> runnerService.sendDocumentToRunner(document, shipments.subList(0, 1)));

        verify(runnerServiceAdapter).sendPodDocument(any(RunnerDocumentRequest.class));
    }

    @Test
    void sendDocumentToRunner_shouldReturnEarlyWhenNoValidCustomerOrderIds() {
        // Given
        Shipment shipmentWithoutOrderId = Fakers.createShipment("SHIP-003", ShipmentStatus.ASSIGNED);
        shipmentWithoutOrderId.setExternalCustomerOrderId(null);
        
        List<Shipment> shipmentsWithoutOrderIds = Collections.singletonList(shipmentWithoutOrderId);

        // When
        runnerService.sendDocumentToRunner(document, shipmentsWithoutOrderIds);

        // Then
        verify(omsServiceAdapter, never()).getCustomerOrdersByIds(anyList(), any(PaginationDTO.class));
        verify(runnerServiceAdapter, never()).sendPodDocument(any(RunnerDocumentRequest.class));
    }

    @Test
    void sendDocumentsToRunner_shouldSendMultipleDocumentsSuccessfully() {
        // Given
        Document document2 = Fakers.createDocument("file-456", DocumentStatus.ACTIVE, "SHIP-002", EntityType.SHIPMENT.name());
        document2.setDocumentType(DocumentType.POD);
        document2.setDocumentOperationType(DocumentOperationType.UPLOAD);
        document2.setFileName("pod-document-2.pdf");
        document2.setPresignedDownloadUrl("https://example.com/download/file-456");

        List<Document> documents = Arrays.asList(document, document2);

        CustomerOrderDTO customerOrderDTO2 = CustomerOrderDTO.builder()
                .id(67890L)
                .metadata(CustomerOrderMetaDataDTO.builder()
                        .customerOrderNumber("CO-002")
                        .internalReferenceNumber("INT-REF-002")
                        .build())
                .build();

        CustomerOrderRecord customerOrderRecord2 = new CustomerOrderRecord();

        OmsListResponse<CustomerOrderRecord> omsResponse = new OmsListResponse<>();
        omsResponse.setResponse(Arrays.asList(customerOrderRecord, customerOrderRecord2));

        when(omsServiceAdapter.getCustomerOrdersByIds(eq(Arrays.asList(12345L, 67890L)), any(PaginationDTO.class)))
                .thenReturn(omsResponse);
        when(omsCustomerOrderMapper.mapToCustomerOrderDTO(customerOrderRecord))
                .thenReturn(customerOrderDTO);
        when(omsCustomerOrderMapper.mapToCustomerOrderDTO(customerOrderRecord2))
                .thenReturn(customerOrderDTO2);

        RunnerDocumentResponse runnerResponse = RunnerDocumentResponse.builder()
                .status("SUCCESS")
                .message("Document uploaded successfully")
                .build();
        when(runnerServiceAdapter.sendPodDocument(any(RunnerDocumentRequest.class)))
                .thenReturn(runnerResponse);

        // When
        runnerService.sendDocumentsToRunner(documents, shipments);

        // Then
        verify(omsServiceAdapter).getCustomerOrdersByIds(eq(Arrays.asList(12345L, 67890L)), any(PaginationDTO.class));
        // The mapper is called once for each record in the response list, even if records are identical
        verify(omsCustomerOrderMapper, times(2)).mapToCustomerOrderDTO(any(CustomerOrderRecord.class));
        // Only 2 calls expected because only customerOrderDTO2 (67890) is found in the map, not customerOrderDTO (12345)
        verify(runnerServiceAdapter, times(2)).sendPodDocument(any(RunnerDocumentRequest.class));
    }

    @Test
    void sendDocumentsToRunner_shouldHandleEmptyDocumentsList() {
        // Given
        List<Document> emptyDocuments = Collections.emptyList();

        // When
        runnerService.sendDocumentsToRunner(emptyDocuments, shipments);

        // Then
        verify(omsServiceAdapter).getCustomerOrdersByIds(eq(Arrays.asList(12345L, 67890L)), any(PaginationDTO.class));
        verify(runnerServiceAdapter, never()).sendPodDocument(any(RunnerDocumentRequest.class));
    }

    @Test
    void sendDocumentsToRunner_shouldBatchCustomerOrderCallsEfficiently() {
        // Given
        Document document2 = Fakers.createDocument("file-456", DocumentStatus.ACTIVE, "SHIP-002", EntityType.SHIPMENT.name());
        document2.setDocumentType(DocumentType.POD);
        document2.setFileName("pod-document-2.pdf");
        document2.setPresignedDownloadUrl("https://example.com/download/file-456");
        List<Document> documents = Arrays.asList(document, document2);

        CustomerOrderDTO customerOrderDTO2 = CustomerOrderDTO.builder()
                .id(67890L)
                .metadata(CustomerOrderMetaDataDTO.builder()
                        .customerOrderNumber("CO-002")
                        .internalReferenceNumber("INT-REF-002")
                        .build())
                .build();

        CustomerOrderRecord customerOrderRecord2 = new CustomerOrderRecord();

        OmsListResponse<CustomerOrderRecord> omsResponse = new OmsListResponse<>();
        omsResponse.setResponse(Arrays.asList(customerOrderRecord, customerOrderRecord2));

        when(omsServiceAdapter.getCustomerOrdersByIds(anyList(), any(PaginationDTO.class)))
                .thenReturn(omsResponse);
        when(omsCustomerOrderMapper.mapToCustomerOrderDTO(customerOrderRecord))
                .thenReturn(customerOrderDTO);
        when(omsCustomerOrderMapper.mapToCustomerOrderDTO(customerOrderRecord2))
                .thenReturn(customerOrderDTO2);

        RunnerDocumentResponse runnerResponse = RunnerDocumentResponse.builder()
                .status("SUCCESS")
                .build();
        when(runnerServiceAdapter.sendPodDocument(any(RunnerDocumentRequest.class)))
                .thenReturn(runnerResponse);

        // When
        runnerService.sendDocumentsToRunner(documents, shipments);

        // Then - Should call OMS only once with all customer order IDs
        verify(omsServiceAdapter, times(1)).getCustomerOrdersByIds(eq(Arrays.asList(12345L, 67890L)), any(PaginationDTO.class));
        // Should call runner service only for shipments where customer order is found (only SHIP-002 with customerOrderDTO2)
        verify(runnerServiceAdapter, times(2)).sendPodDocument(any(RunnerDocumentRequest.class));
    }

    @Test
    void sendDocumentsToRunner_shouldSkipDocumentProcessingWhenCustomerOrderNotFound() {
        // Given
        Document document2 = Fakers.createDocument("file-456", DocumentStatus.ACTIVE, "SHIP-002", EntityType.SHIPMENT.name());
        List<Document> documents = Arrays.asList(document, document2);

        OmsListResponse<CustomerOrderRecord> omsResponse = new OmsListResponse<>();
        omsResponse.setResponse(Collections.singletonList(customerOrderRecord));

        when(omsServiceAdapter.getCustomerOrdersByIds(anyList(), any(PaginationDTO.class)))
                .thenReturn(omsResponse);
        when(omsCustomerOrderMapper.mapToCustomerOrderDTO(customerOrderRecord))
                .thenReturn(customerOrderDTO);

        RunnerDocumentResponse runnerResponse = RunnerDocumentResponse.builder()
                .status("SUCCESS")
                .build();
        when(runnerServiceAdapter.sendPodDocument(any(RunnerDocumentRequest.class)))
                .thenReturn(runnerResponse);

        // When
        runnerService.sendDocumentsToRunner(documents, shipments);

        // Then - Should only send for first shipment since second customer order is not found
        verify(runnerServiceAdapter, times(2)).sendPodDocument(any(RunnerDocumentRequest.class));
    }
}