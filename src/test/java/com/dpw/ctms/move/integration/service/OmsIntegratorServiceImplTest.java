package com.dpw.ctms.move.integration.service;


import com.dpw.ctms.move.dto.PaginationDTO;
import com.dpw.ctms.move.integration.adapter.OmsServiceAdapter;
import com.dpw.ctms.move.integration.dto.oms.ConsignmentListRequestDTO;
import com.dpw.ctms.move.integration.dto.oms.OmsConsignmentDto;
import com.dpw.ctms.move.integration.mapper.oms.OmsConsignmentMapper;
import com.dpw.ctms.move.integration.response.oms.OmsListResponse;
import com.dpw.ctms.move.integration.response.oms.consignment.ConsignmentRecord;
import com.dpw.ctms.move.integration.service.impl.OmsIntegratorServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

class OmsIntegratorServiceImplTest {

    @Mock
    private OmsServiceAdapter omsServiceAdapter;

    @Mock
    private OmsConsignmentMapper omsConsignmentMapper;

    @InjectMocks
    private OmsIntegratorServiceImpl omsIntegratorService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testGetOmsConsignmentDtos_whenValidRecords_shouldMapCorrectly() {
        // Arrange
        List<String> externalIds = List.of("EXT-1");

        ConsignmentRecord record = new ConsignmentRecord();
        OmsListResponse<ConsignmentRecord> response = new OmsListResponse<>();
        response.setResponse(List.of(record));

        OmsConsignmentDto omsConsignmentDto = OmsConsignmentDto.builder()
                .id(123L)
                .code("CONS001")
                .build();

        when(omsServiceAdapter.getConsignmentList(
                any(ConsignmentListRequestDTO.class),
                any(PaginationDTO.class))
        ).thenReturn(response);

        when(omsConsignmentMapper.mapToOmsConsignmentDto(record)).thenReturn(omsConsignmentDto);

        // Act
        List<OmsConsignmentDto> result = omsIntegratorService.getOmsConsignmentDtos(externalIds);

        // Assert
        assertThat(result).hasSize(1);
        assertThat(result.getFirst().getId()).isEqualTo(123L);
        assertThat(result.getFirst().getCode()).isEqualTo("CONS001");

        verify(omsServiceAdapter).getConsignmentList(
                argThat(req -> req.getIds().equals(externalIds)),
                argThat(p -> p.getPageNo() == 0 && p.getPageSize() == externalIds.size())
        );
        verify(omsConsignmentMapper).mapToOmsConsignmentDto(record);
    }

    @Test
    void testGetOmsConsignmentDtos_whenNullResponse_shouldReturnEmptyList() {
        // Arrange
        List<String> externalIds = List.of("EXT-1");
        OmsListResponse<ConsignmentRecord> response = new OmsListResponse<>();
        response.setResponse(null);

        when(omsServiceAdapter.getConsignmentList(any(), any())).thenReturn(response);

        // Act
        List<OmsConsignmentDto> result = omsIntegratorService.getOmsConsignmentDtos(externalIds);

        // Assert
        assertThat(result).isEmpty();
    }

    @Test
    void testGetOmsConsignmentDtos_whenMapperReturnsNull_shouldFilterItOut() {
        // Arrange
        List<String> externalIds = List.of("EXT-1");

        ConsignmentRecord record = new ConsignmentRecord();
        OmsListResponse<ConsignmentRecord> response = new OmsListResponse<>();
        response.setResponse(List.of(record));

        when(omsServiceAdapter.getConsignmentList(any(), any())).thenReturn(response);
        when(omsConsignmentMapper.mapToOmsConsignmentDto(record)).thenReturn(null); // simulate bad mapping

        // Act
        List<OmsConsignmentDto> result = omsIntegratorService.getOmsConsignmentDtos(externalIds);

        // Assert
        assertThat(result).isEmpty();
    }
}

