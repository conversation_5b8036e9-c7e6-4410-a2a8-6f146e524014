package com.dpw.ctms.move.integration.service;

import com.dpw.ctms.move.integration.adapter.ResourceServiceAdapter;
import com.dpw.ctms.move.integration.dto.resource.ResourceFacilitiesDto;
import com.dpw.ctms.move.integration.dto.resource.ResourceProductDetailsDto;
import com.dpw.ctms.move.integration.dto.resource.ResourceUomDto;
import com.dpw.ctms.move.integration.mapper.resource.ResourceFacilitiesMapper;
import com.dpw.ctms.move.integration.mapper.resource.ResourceProductMapper;
import com.dpw.ctms.move.integration.mapper.resource.ResourceUomMapper;
import com.dpw.ctms.move.integration.response.ListResponse;
import com.dpw.ctms.move.integration.response.resource.facility.FacilityRecord;
import com.dpw.ctms.move.integration.response.resource.product.ProductRecord;
import com.dpw.ctms.move.integration.response.resource.uom.UomRecord;
import com.dpw.ctms.move.integration.service.impl.ResourceIntegratorServiceImpl;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ResourceIntegratorServiceImplTest {

    @Mock
    private ResourceServiceAdapter resourceServiceAdapter;

    @Mock
    private ResourceFacilitiesMapper facilityMapper;

    @Mock
    private ResourceProductMapper productMapper;

    @Mock
    private ResourceUomMapper uomMapper;

    @InjectMocks
    private ResourceIntegratorServiceImpl resourceIntegratorService;

    @Test
    void testGetFacilityDTOs_success() {
        // Arrange
        List<String> externalFacilityCodes = List.of("FAC-001", "FAC-002");

        FacilityRecord record1 = new FacilityRecord(); // create a stubbed record
        FacilityRecord record2 = new FacilityRecord(); // another one

        List<FacilityRecord> facilityRecords = List.of(record1, record2);

        ListResponse<FacilityRecord> response = new ListResponse<>();
        response.setRecords(facilityRecords);

        ResourceFacilitiesDto dto1 = new ResourceFacilitiesDto(); // mapped result
        ResourceFacilitiesDto dto2 = new ResourceFacilitiesDto();

        when(resourceServiceAdapter.getFacilityList(any(), any())).thenReturn(response);
        when(facilityMapper.mapToResourceFacilitiesDto(record1)).thenReturn(dto1);
        when(facilityMapper.mapToResourceFacilitiesDto(record2)).thenReturn(dto2);

        // Act
        List<ResourceFacilitiesDto> result = resourceIntegratorService.getFacilitiesDTOs(externalFacilityCodes);

        // Assert
        assertThat(result).containsExactly(dto1, dto2);
        verify(resourceServiceAdapter).getFacilityList(any(), any());
    }

    @Test
    void testGetFacilityDTOs_nullRecords_shouldReturnEmptyList() {
        // Arrange
        List<String> externalFacilityCodes = List.of("FAC-003");
        ListResponse<FacilityRecord> response = new ListResponse<>();
        response.setRecords(null);

        when(resourceServiceAdapter.getFacilityList(any(), any())).thenReturn(response);

        // Act
        List<ResourceFacilitiesDto> result = resourceIntegratorService.getFacilitiesDTOs(externalFacilityCodes);

        // Assert
        assertThat(result).isEmpty();
    }

    @Test
    void testGetFacilityDTOs_mapperReturnsNull_filteredOut() {
        // Arrange
        List<String> externalFacilityCodes = List.of("FAC-004");
        FacilityRecord record = new FacilityRecord();

        ListResponse<FacilityRecord> response = new ListResponse<>();
        response.setRecords(List.of(record));

        when(resourceServiceAdapter.getFacilityList(any(), any())).thenReturn(response);
        when(facilityMapper.mapToResourceFacilitiesDto(record)).thenReturn(null);

        // Act
        List<ResourceFacilitiesDto> result = resourceIntegratorService.getFacilitiesDTOs(externalFacilityCodes);

        // Assert
        assertThat(result).isEmpty();
    }

    @Test
    void testGetProductDetailsDTOs_success() {
        // Arrange
        List<String> productIds = List.of("101", "102");

        ProductRecord record1 = new ProductRecord();
        ProductRecord record2 = new ProductRecord();
        List<ProductRecord> productRecords = List.of(record1, record2);

        ListResponse<ProductRecord> response = new ListResponse<>();
        response.setRecords(productRecords);

        ResourceProductDetailsDto dto1 = new ResourceProductDetailsDto();
        ResourceProductDetailsDto dto2 = new ResourceProductDetailsDto();

        when(resourceServiceAdapter.getProductList(any(), any())).thenReturn(response);
        when(productMapper.mapToResourceProductDetailsDto(record1)).thenReturn(dto1);
        when(productMapper.mapToResourceProductDetailsDto(record2)).thenReturn(dto2);

        // Act
        List<ResourceProductDetailsDto> result = resourceIntegratorService.getProductDetailsDTOs(productIds);

        // Assert
        assertThat(result).containsExactly(dto1, dto2);
        verify(resourceServiceAdapter).getProductList(any(), any());
    }

    @Test
    void testGetProductDetailsDTOs_nullRecords_shouldReturnEmptyList() {
        // Arrange
        List<String> productIds = List.of("103");
        ListResponse<ProductRecord> response = new ListResponse<>();
        response.setRecords(null);

        when(resourceServiceAdapter.getProductList(any(), any())).thenReturn(response);

        // Act
        List<ResourceProductDetailsDto> result = resourceIntegratorService.getProductDetailsDTOs(productIds);

        // Assert
        assertThat(result).isEmpty();
    }

    @Test
    void testGetProductDetailsDTOs_mapperReturnsNull_filteredOut() {
        // Arrange
        List<String> productIds = List.of("104");
        ProductRecord record = new ProductRecord();

        ListResponse<ProductRecord> response = new ListResponse<>();
        response.setRecords(List.of(record));

        when(resourceServiceAdapter.getProductList(any(), any())).thenReturn(response);
        when(productMapper.mapToResourceProductDetailsDto(record)).thenReturn(null);

        // Act
        List<ResourceProductDetailsDto> result = resourceIntegratorService.getProductDetailsDTOs(productIds);

        // Assert
        assertThat(result).isEmpty();
    }

    @Test
    void testGetUomDTOs_success() {
        // Arrange
        List<String> uomIds = List.of("1", "2");

        UomRecord record1 = new UomRecord();
        UomRecord record2 = new UomRecord();
        List<UomRecord> uomRecords = List.of(record1, record2);

        ListResponse<UomRecord> response = new ListResponse<>();
        response.setRecords(uomRecords);

        ResourceUomDto dto1 = new ResourceUomDto();
        ResourceUomDto dto2 = new ResourceUomDto();

        when(resourceServiceAdapter.getUomList(any(), any())).thenReturn(response);
        when(uomMapper.mapToResourceUomDto(record1)).thenReturn(dto1);
        when(uomMapper.mapToResourceUomDto(record2)).thenReturn(dto2);

        // Act
        List<ResourceUomDto> result = resourceIntegratorService.getUomDTOs(uomIds);

        // Assert
        assertThat(result).containsExactly(dto1, dto2);
        verify(resourceServiceAdapter).getUomList(any(), any());
    }

    @Test
    void testGetUomDTOs_nullRecords_shouldReturnEmptyList() {
        // Arrange
        List<String> uomIds = List.of("3");
        ListResponse<UomRecord> response = new ListResponse<>();
        response.setRecords(null);

        when(resourceServiceAdapter.getUomList(any(), any())).thenReturn(response);

        // Act
        List<ResourceUomDto> result = resourceIntegratorService.getUomDTOs(uomIds);

        // Assert
        assertThat(result).isEmpty();
    }

    @Test
    void testGetUomDTOs_mapperReturnsNull_filteredOut() {
        // Arrange
        List<String> uomIds = List.of("4");
        UomRecord record = new UomRecord();

        ListResponse<UomRecord> response = new ListResponse<>();
        response.setRecords(List.of(record));

        when(resourceServiceAdapter.getUomList(any(), any())).thenReturn(response);
        when(uomMapper.mapToResourceUomDto(record)).thenReturn(null);

        // Act
        List<ResourceUomDto> result = resourceIntegratorService.getUomDTOs(uomIds);

        // Assert
        assertThat(result).isEmpty();
    }

    @Test
    void testGetFacilityDTOs_emptyList_shouldReturnEmptyList() {
        // Arrange
        List<String> emptyFacilityCodes = Collections.emptyList();

        ListResponse<FacilityRecord> response = new ListResponse<>();
        response.setRecords(Collections.emptyList());

        when(resourceServiceAdapter.getFacilityList(any(), any())).thenReturn(response);

        // Act
        List<ResourceFacilitiesDto> result = resourceIntegratorService.getFacilitiesDTOs(emptyFacilityCodes);

        // Assert
        assertThat(result).isEmpty();
        verify(resourceServiceAdapter).getFacilityList(any(), any());
    }

    @Test
    void testGetProductDetailsDTOs_emptyList_shouldReturnEmptyList() {
        // Arrange
        List<String> emptyProductIds = Collections.emptyList();

        ListResponse<ProductRecord> response = new ListResponse<>();
        response.setRecords(Collections.emptyList());

        when(resourceServiceAdapter.getProductList(any(), any())).thenReturn(response);

        // Act
        List<ResourceProductDetailsDto> result = resourceIntegratorService.getProductDetailsDTOs(emptyProductIds);

        // Assert
        assertThat(result).isEmpty();
        verify(resourceServiceAdapter).getProductList(any(), any());
    }

    @Test
    void testGetUomDTOs_emptyList_shouldReturnEmptyList() {
        // Arrange
        List<String> emptyUomIds = Collections.emptyList();

        ListResponse<UomRecord> response = new ListResponse<>();
        response.setRecords(Collections.emptyList());

        when(resourceServiceAdapter.getUomList(any(), any())).thenReturn(response);

        // Act
        List<ResourceUomDto> result = resourceIntegratorService.getUomDTOs(emptyUomIds);

        // Assert
        assertThat(result).isEmpty();
        verify(resourceServiceAdapter).getUomList(any(), any());
    }
}
