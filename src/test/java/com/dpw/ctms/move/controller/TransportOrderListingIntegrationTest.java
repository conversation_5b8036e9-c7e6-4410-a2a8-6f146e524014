package com.dpw.ctms.move.controller;

import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.Time;
import com.dpw.ctms.move.entity.TrailerResource;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.entity.VehicleOperatorResource;
import com.dpw.ctms.move.entity.VehicleResource;
import com.dpw.ctms.move.entity.Time;
import com.dpw.ctms.move.enums.AssignmentType;
import com.dpw.ctms.move.enums.ShipmentStatus;
import com.dpw.ctms.move.enums.TransportOrderStatus;
import com.dpw.ctms.move.enums.TripStatus;
import com.dpw.ctms.move.repository.TransportOrderRepository;
import com.dpw.ctms.move.repository.TripRepository;
import com.dpw.ctms.move.request.DateRange;
import com.dpw.ctms.move.request.TransportOrderListingRequest;
import com.dpw.ctms.move.response.ListResponse;
import com.dpw.ctms.move.response.TransportOrderListingResponse;
import com.dpw.ctms.move.testcontainers.TestDatabaseManager;
import com.dpw.ctms.move.utils.Faker;
import com.dpw.tmsutils.threadlocal.TenantContext;
import com.fasterxml.jackson.core.type.TypeReference;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;

import java.lang.Exception;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static com.dpw.tmsutils.constant.RequestConstants.TENANT_HEADER;
import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class TransportOrderListingIntegrationTest extends IntegrationTestBase{
    @Autowired
    private TripRepository tripRepository;

    @Autowired
    private TransportOrderRepository transportOrderRepository;

    private String uniqueId;

    @BeforeEach
    void setUp() {
        TenantContext.setCurrentTenant("CFR");
        uniqueId = String.valueOf(System.currentTimeMillis());
        cleanup();
        setupBasicTestData();
    }

    @AfterEach
    void cleanup() {
        TenantContext.setCurrentTenant("CFR");
        try {
            // Use repository cleanup first (safer)
            tripRepository.deleteAll();
            transportOrderRepository.deleteAll();
        } catch (Exception e) {
            // If repository cleanup fails, use database manager cleanup
            System.err.println("Repository cleanup failed, using database cleanup: " + e.getMessage());
            TestDatabaseManager.cleanupCfrSchema();
        }
        TenantContext.clear();
    }

    @Test
    void listTOs_WithNoFilters_ShouldReturnAllTos() throws Exception {
        // Arrange
        TenantContext.setCurrentTenant("CFR");
        TransportOrderListingRequest request = Faker.createBasicTransportOrderListingRequest();

        // Act
        MvcResult result = performListTOsRequest(request);

        // Assert
        ListResponse<TransportOrderListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertEquals(2, response.getTotalRecords());
        assertEquals(2, response.getData().size());

        // Verify basic structure
        TransportOrderListingResponse firstTO = response.getData().getFirst();
        assertNotNull(firstTO.getCode());
        assertNotNull(firstTO.getStatus());
        assertNotNull(firstTO.getCode());
    }

    @Test
    void listTOs_WithAllFilters_ShouldInvokeFilters() throws Exception {
        // Arrange
        TenantContext.setCurrentTenant("CFR");
        TransportOrderListingRequest.Filter filter = new TransportOrderListingRequest.Filter();
        applyTransportOrderStatusFilter(filter);
        applyTransportOrderIdsFilter(filter);
        applyTripStatusFilter(filter);
        applyTripIdsFilter(filter);
        applyShipmentIdsFilter(filter);
        applyShipmentStatusesFilter(filter);
        applyConsignmentIdsFilter(filter);
        applyCustomerOrderIdsFilter(filter);
        applyVendorIdsFilter(filter);
        applyTripVehicleFilters(filter);
        applyTripVehicleOperatorFilters(filter);
        applyTripTrailerFilters(filter);
        applyTripVehicleTypesFilter(filter);
        applyOptionalDateRangeFilter(filter);
        applyShipmentLocationFilters(filter);
        TransportOrderListingRequest request = Faker.createBasicTransportOrderListingRequestWithFilters(filter);

        // Act
        MvcResult result = performListTOsRequest(request);

        // Assert
        ListResponse<TransportOrderListingResponse> response = parseResponse(result);
        assertNotNull(response);
    }

    @Test
    void listTOs_WithPagination_ShouldReturnPagedResults() throws Exception {
        // Arrange - Request page 0 with size 1
        TenantContext.setCurrentTenant("CFR");
        TransportOrderListingRequest request = Faker.createTransportOrderListingRequestWithPagination(Faker.SINGLE_PAGE_PAGINATION);

        // Act
        MvcResult result = performListTOsRequest(request);

        // Assert
        ListResponse<TransportOrderListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertEquals(2, response.getTotalRecords()); // Total records (updated to 4)
        assertEquals(1, response.getData().size()); // Only 1 record on this page
    }

    @Test
    void listTOs_WithPodAttachedTrue_ShouldReturnTransportOrdersWithPodAttached() throws Exception {
        // Arrange
        TenantContext.setCurrentTenant("CFR");
        TransportOrderListingRequest.Filter filter = new TransportOrderListingRequest.Filter();
        filter.setIsPodAttached(true);
        TransportOrderListingRequest request = Faker.createBasicTransportOrderListingRequestWithFilters(filter);

        // Act
        MvcResult result = performListTOsRequest(request);

        // Assert
        ListResponse<TransportOrderListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0);
        // Should return transport orders where at least one shipment has isDocumentAttached = true
    }

    @Test
    void listTOs_WithPodAttachedFalse_ShouldReturnTransportOrdersWithoutPodAttached() throws Exception {
        // Arrange
        TenantContext.setCurrentTenant("CFR");
        TransportOrderListingRequest.Filter filter = new TransportOrderListingRequest.Filter();
        filter.setIsPodAttached(false);
        TransportOrderListingRequest request = Faker.createBasicTransportOrderListingRequestWithFilters(filter);

        // Act
        MvcResult result = performListTOsRequest(request);

        // Assert
        ListResponse<TransportOrderListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0);
        // Should return transport orders where at least one shipment has isDocumentAttached = false
    }

    @Test
    void listTOs_WithNullPodAttached_ShouldReturnAllTransportOrders() throws Exception {
        // Arrange
        TenantContext.setCurrentTenant("CFR");
        TransportOrderListingRequest.Filter filter = new TransportOrderListingRequest.Filter();
        filter.setIsPodAttached(null);
        TransportOrderListingRequest request = Faker.createBasicTransportOrderListingRequestWithFilters(filter);

        // Act
        MvcResult result = performListTOsRequest(request);

        // Assert
        ListResponse<TransportOrderListingResponse> response = parseResponse(result);
        assertNotNull(response);
        // Should return all transport orders when POD filter is not applied
        assertEquals(2, response.getTotalRecords(), "Should return all transport orders when POD filter is null");
    }

    private MvcResult performListTOsRequest(TransportOrderListingRequest request) throws Exception {
        String requestBody = objectMapper.writeValueAsString(request);

        return mockMvc.perform(post("/v1/transport-orders/list")
                        .contentType(MediaType.APPLICATION_JSON)
                        .header(TENANT_HEADER, "CFR")
                        .content(requestBody))
                .andExpect(status().isOk())
                .andReturn();
    }

    private ListResponse<TransportOrderListingResponse> parseResponse(MvcResult result) throws Exception {
        String responseJson = result.getResponse().getContentAsString();
        return objectMapper.readValue(responseJson, new TypeReference<>() {
        });
    }

    private void setupBasicTestData() {
        // Ensure tenant context is set for data operations
        TenantContext.setCurrentTenant("CFR");

        // Create basic Transport Order
        TransportOrder basicTransportOrder = new TransportOrder();
        basicTransportOrder.setCode("TO_BASIC_" + uniqueId);
        basicTransportOrder.setStatus(TransportOrderStatus.ASSIGNED);
        basicTransportOrder.setAssignmentType(AssignmentType.EXTERNAL);
        basicTransportOrder.setAssigneeIdentifier("VENDOR_BASIC");
        Set<Shipment> shipments = getShipments();
        shipments.forEach(shipment -> shipment.setTransportOrder(basicTransportOrder));
        basicTransportOrder.setShipments(new HashSet<>(shipments));


        // Create complex Transport Order for comprehensive testing
        TransportOrder complexTransportOrder = new TransportOrder();
        complexTransportOrder.setCode("TO_COMPLEX_" + uniqueId);
        complexTransportOrder.setStatus(TransportOrderStatus.ASSIGNED);
        complexTransportOrder.setAssignmentType(AssignmentType.EXTERNAL);
        complexTransportOrder.setAssigneeIdentifier("VENDOR_COMPLEX");
        Set<Shipment> complexShipments = getShipments();
        shipments.forEach(shipment -> shipment.setTransportOrder(complexTransportOrder));
        complexTransportOrder.setShipments(new HashSet<>(complexShipments));


        // Create comprehensive test trips
        List<Trip> trips = new ArrayList<>();

        // Basic Trip 1
        Trip trip1 = createBasicTrip("TRIP_BASIC_1_" + uniqueId, TripStatus.CREATED, basicTransportOrder,
                "ORIGIN_BASIC", "DEST_BASIC");
        trips.add(trip1);

        // Basic Trip 2
        Trip trip2 = createBasicTrip("TRIP_BASIC_2_" + uniqueId, TripStatus.IN_PROGRESS, basicTransportOrder,
                "ORIGIN_BASIC_2", "DEST_BASIC_2");
        trips.add(trip2);

        // Complex Trip 1 - with comprehensive data for filter testing
        Trip complexTrip1 = createComplexTrip("TRIP_COMPLEX_1_" + uniqueId, TripStatus.CREATED, complexTransportOrder,
                "ORIGIN_COMPLEX", "DEST_COMPLEX", 1);
        trips.add(complexTrip1);

        // Complex Trip 2 - with comprehensive data for filter testing
        Trip complexTrip2 = createComplexTrip("TRIP_COMPLEX_2_" + uniqueId, TripStatus.IN_PROGRESS, complexTransportOrder,
                "ORIGIN_COMPLEX_2", "DEST_COMPLEX_2", 2);
        trips.add(complexTrip2);

        basicTransportOrder.setTrips(new HashSet<>(trips));
        complexTransportOrder.setTrips(new HashSet<>(trips));

        transportOrderRepository.saveAll(List.of(basicTransportOrder, complexTransportOrder));

    }

    private Trip createBasicTrip(String code, TripStatus status, TransportOrder transportOrder,
                                 String originCode, String destCode) {
        Trip trip = new Trip();
        trip.setCode(code);
        trip.setStatus(status);
        trip.setTransportOrder(transportOrder);
        trip.setExternalOriginLocationCode(originCode);
        trip.setExternalDestinationLocationCode(destCode);

        // Set timestamp fields for date range testing
        long currentTime = System.currentTimeMillis();
        trip.setExpectedStartAt(new Time(currentTime, "UTC"));
        trip.setExpectedEndAt(new Time(currentTime + 3600000L, "UTC"));
        trip.setActualStartAt(new Time(currentTime + 1800000L, "UTC"));
        trip.setActualEndAt(new Time(currentTime + 5400000L, "UTC"));

        return trip;
    }

    private Trip createComplexTrip(String code, TripStatus status, TransportOrder transportOrder,
                                   String originCode, String destCode, int index) {
        Trip trip = createBasicTrip(code, status, transportOrder, originCode, destCode);

        // Add Vehicle Resource with proper data for filtering tests
        VehicleResource vehicleResource = new VehicleResource();
        vehicleResource.setExternalResourceId("VEHICLE_COMPLEX_" + index);
        vehicleResource.setExternalVehicleTypeId("TRUCK");
        vehicleResource.setRegistrationNumber("REG_COMPLEX_" + index);
        vehicleResource.setTrip(trip);
        trip.setVehicleResource(vehicleResource);

        // Add Trailer Resources
        Set<TrailerResource> trailerResources = new HashSet<>();
        TrailerResource trailerResource = new TrailerResource();
        trailerResource.setExternalResourceId("TRAILER_COMPLEX_" + index);
        trailerResource.setTrip(trip);
        trailerResources.add(trailerResource);
        trip.setTrailerResources(trailerResources);

        // Add Vehicle Operator Resources
        Set<VehicleOperatorResource> operatorResources = new HashSet<>();
        VehicleOperatorResource operatorResource = new VehicleOperatorResource();
        operatorResource.setExternalResourceId("DRIVER_COMPLEX_" + index);
        operatorResource.setTrip(trip);
        operatorResources.add(operatorResource);
        trip.setVehicleOperatorResources(operatorResources);

        // Add Shipments
        Set<Shipment> shipments = getShipments();
        shipments.forEach(shipment -> shipment.setTrip(trip));
        trip.setShipments(shipments);
        return trip;
    }

    private Set<Shipment> getShipments() {
        Set<Shipment> shipments = new HashSet<>();
        Shipment shipment = new Shipment();
        shipment.setCode("SHIPMENT_BASIC_" + System.currentTimeMillis() + Math.random()*100);
        shipment.setExternalCustomerOrderId("CO_BASIC");
        shipment.setExternalConsignmentId("CONSIGNMENT_BASIC");
        shipment.setStatus(ShipmentStatus.ALLOCATED);
        shipments.add(shipment);
        return shipments;
    }

    private void applyTransportOrderStatusFilter(TransportOrderListingRequest.Filter filter) {
        filter.setTransportOrderStatuses(List.of("CONFIRMED", "CANCELLED"));
    }

    private void applyTransportOrderIdsFilter(TransportOrderListingRequest.Filter filter) {
        filter.setTransportOrderIds(List.of("TO_BASIC_" + uniqueId, "TO_COMPLEX_" + uniqueId, "TO003"));
    }

    private void applyTripStatusFilter(TransportOrderListingRequest.Filter filter) {
        filter.setTripStatuses(List.of("COMPLETED", "IN_TRANSIT"));
    }

    private void applyTripIdsFilter(TransportOrderListingRequest.Filter filter) {
        filter.setTripIds(List.of("TRIP_BASIC_1_" + uniqueId, "TRIP_BASIC_2_" + uniqueId, "trip003"));
    }

    private void applyShipmentIdsFilter(TransportOrderListingRequest.Filter filter) {
        filter.setShipmentIds(List.of("shipment1", "shipment2"));
    }

    private void applyShipmentStatusesFilter(TransportOrderListingRequest.Filter filter) {
        filter.setShipmentStatuses(List.of("ALLOCATED", "INVALID_STATUS"));
    }

    private void applyConsignmentIdsFilter(TransportOrderListingRequest.Filter filter) {
        filter.setConsignmentIds(List.of("consignment1", "consignment2"));
    }

    private void applyCustomerOrderIdsFilter(TransportOrderListingRequest.Filter filter) {
        filter.setCustomerOrderIds(List.of("customer001", "customer002"));
    }

    private void applyVendorIdsFilter(TransportOrderListingRequest.Filter filter) {
        TransportOrderListingRequest.Assignment assignment = new TransportOrderListingRequest.Assignment();
        assignment.setIdentifiers(List.of("VENDOR_COMPLEX"));
        filter.setAssignment(assignment);
    }

    private void applyTripVehicleFilters(TransportOrderListingRequest.Filter filter) {
        filter.setVehicleIds(List.of("vehicleA", "vehicleB"));
    }

    private void applyTripVehicleOperatorFilters(TransportOrderListingRequest.Filter filter) {
        filter.setVehicleOperatorIds(List.of("operator1", "operator2"));
    }

    private void applyTripTrailerFilters(TransportOrderListingRequest.Filter filter) {
        filter.setTrailerIds(List.of("trailerA", "trailerB"));
    }

    private void applyTripVehicleTypesFilter(TransportOrderListingRequest.Filter filter) {
        filter.setVehicleTypes(List.of("typeA", "typeB"));
    }

    private void applyOptionalDateRangeFilter(TransportOrderListingRequest.Filter filter) {
        DateRange expectedPickupDateRange = new DateRange();
        expectedPickupDateRange.setFrom(1L);
        expectedPickupDateRange.setTo(2L);
        filter.setExpectedPickupDateRange(expectedPickupDateRange);

        DateRange expectedDeliveryDateRange = new DateRange();
        expectedDeliveryDateRange.setFrom(3L);
        expectedDeliveryDateRange.setTo(4L);
        filter.setExpectedDeliveryDateRange(expectedDeliveryDateRange);

        DateRange actualPickupDateRange = new DateRange();
        actualPickupDateRange.setFrom(5L);
        actualPickupDateRange.setTo(6L);
        filter.setActualPickupDateRange(actualPickupDateRange);

        DateRange actualDeliveryDateRange = new DateRange();
        actualDeliveryDateRange.setFrom(7L);
        actualDeliveryDateRange.setTo(8L);
        filter.setActualDeliveryDateRange(actualDeliveryDateRange);
    }

    private void applyShipmentLocationFilters(TransportOrderListingRequest.Filter filter) {
        filter.setOriginLocationId("ORIGIN_BASIC_2");
        filter.setDestinationLocationId("DESTINATION_BASIC_2");
    }
}
