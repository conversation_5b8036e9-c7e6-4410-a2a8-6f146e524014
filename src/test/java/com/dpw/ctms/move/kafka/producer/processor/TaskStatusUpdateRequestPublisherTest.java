package com.dpw.ctms.move.kafka.producer.processor;

import com.dpw.ctms.move.dto.TimeDTO;
import com.dpw.ctms.move.kafka.producer.KafkaProducer;
import com.dpw.ctms.move.kafka.producer.processor.impl.TaskStatusUpdateRequestPublisher;
import com.dpw.ctms.move.request.common.IntegratorMessageHeader;
import com.dpw.ctms.move.request.common.IntegratorMessageRequest;
import com.dpw.ctms.move.request.common.MessageRequest;
import com.dpw.ctms.move.request.message.TripStatusUpdateMessage;
import com.dpw.ctms.move.service.IEventProcessorService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static com.dpw.ctms.move.constants.MoveConstants.SOURCE_MOVE;
import static com.dpw.ctms.move.enums.MessageActionType.TASK_STATUS_UPDATE;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class
TaskStatusUpdateRequestPublisherTest {

    @Mock
    private IEventProcessorService<TripStatusUpdateMessage> eventProcessorService;

    @Mock
    private KafkaProducer kafkaProducer;

    private TaskStatusUpdateRequestPublisher publisher;

    private IntegratorMessageRequest<TripStatusUpdateMessage> validRequest;

    @BeforeEach
    void setUp() {
        publisher = new TaskStatusUpdateRequestPublisher(eventProcessorService, kafkaProducer);
        
        TripStatusUpdateMessage statusUpdateMessage = TripStatusUpdateMessage.builder()
                .currentStatus("COMPLETED")
                .previousStatus("ACTIVE")
                .tripCode("TRIP-12345")
                .extTripCode("EXT-TRIP-12345")
                .startTime(new TimeDTO(System.currentTimeMillis() - 3600000L, "UTC"))
                .endTime(new TimeDTO(System.currentTimeMillis(), "UTC"))
                .updatedBy("test-user")
                .updatedAt(System.currentTimeMillis())
                .eventType("STATUS_UPDATE")
                .comments("Trip completed successfully")
                .build();

        IntegratorMessageHeader header = IntegratorMessageHeader.builder()
                .action(TASK_STATUS_UPDATE.name())
                .dateTime(System.currentTimeMillis())
                .source(SOURCE_MOVE)
                .topic("trip-status-updates")
                .build();

        validRequest = IntegratorMessageRequest.<TripStatusUpdateMessage>builder()
                .transactionContext(header)
                .message(MessageRequest.<TripStatusUpdateMessage>builder()
                        .item(statusUpdateMessage)
                        .build())
                .build();
    }

    @Test
    void init_ShouldRegisterWithEventProcessorService() {
        publisher.init();

        verify(eventProcessorService).addRequestProcessor(
                eq(TASK_STATUS_UPDATE.name()),
                same(publisher)
        );
    }

    @Test
    void process_WhenKafkaPublishSucceeds_ShouldReturnTrue() {
        when(kafkaProducer.publishEvent(validRequest)).thenReturn(true);

        boolean result = publisher.process(validRequest);

        assertTrue(result);
        verify(kafkaProducer).publishEvent(validRequest);
    }

    @Test
    void process_WhenKafkaPublishFails_ShouldReturnFalse() {
        when(kafkaProducer.publishEvent(validRequest)).thenReturn(false);

        boolean result = publisher.process(validRequest);

        assertFalse(result);
        verify(kafkaProducer).publishEvent(validRequest);
    }

    @Test
    void process_ShouldHandleExceptionFromKafkaProducer() {
        when(kafkaProducer.publishEvent(validRequest)).thenThrow(new RuntimeException("Kafka error"));

        Exception exception = assertThrows(RuntimeException.class, () -> {
            publisher.process(validRequest);
        });

        assertEquals("Kafka error", exception.getMessage());
        verify(kafkaProducer).publishEvent(validRequest);
    }

    @Test
    void process_WithEmptyTopic_ShouldStillAttemptToPublish() {
        IntegratorMessageHeader headerWithEmptyTopic = IntegratorMessageHeader.builder()
                .action(TASK_STATUS_UPDATE.name())
                .dateTime(System.currentTimeMillis())
                .source(SOURCE_MOVE)
                .topic("")
                .build();

        IntegratorMessageRequest<TripStatusUpdateMessage> requestWithEmptyTopic =
                IntegratorMessageRequest.<TripStatusUpdateMessage>builder()
                        .transactionContext(headerWithEmptyTopic)
                        .message(validRequest.getMessage())
                        .build();

        when(kafkaProducer.publishEvent(requestWithEmptyTopic)).thenReturn(true);

        boolean result = publisher.process(requestWithEmptyTopic);

        assertTrue(result);
        verify(kafkaProducer).publishEvent(requestWithEmptyTopic);
    }

    @Test
    void process_WithDifferentTripStatuses_ShouldProcess() {
        String[] statuses = {"PLANNED", "ACTIVE", "COMPLETED", "CANCELLED", "IN_TRANSIT"};

        for (String status : statuses) {
            TripStatusUpdateMessage statusUpdateMessage = TripStatusUpdateMessage.builder()
                    .currentStatus(status)
                    .previousStatus("PLANNED")
                    .tripCode("TRIP-" + status)
                    .extTripCode("EXT-TRIP-" + status)
                    .build();

            IntegratorMessageHeader header = IntegratorMessageHeader.builder()
                    .action(TASK_STATUS_UPDATE.name())
                    .dateTime(System.currentTimeMillis())
                    .source(SOURCE_MOVE)
                    .topic("trip-status-updates")
                    .build();

            IntegratorMessageRequest<TripStatusUpdateMessage> request =
                    IntegratorMessageRequest.<TripStatusUpdateMessage>builder()
                            .transactionContext(header)
                            .message(MessageRequest.<TripStatusUpdateMessage>builder()
                                    .item(statusUpdateMessage)
                                    .build())
                            .build();

            when(kafkaProducer.publishEvent(request)).thenReturn(true);

            boolean result = publisher.process(request);

            assertTrue(result, "Should process status: " + status);
            verify(kafkaProducer).publishEvent(request);
        }
    }

    @Test
    void process_WithNullMessage_ShouldStillAttemptToPublish() {
        IntegratorMessageHeader header = IntegratorMessageHeader.builder()
                .action(TASK_STATUS_UPDATE.name())
                .dateTime(System.currentTimeMillis())
                .source(SOURCE_MOVE)
                .topic("trip-status-updates")
                .build();

        IntegratorMessageRequest<TripStatusUpdateMessage> requestWithNullMessage =
                IntegratorMessageRequest.<TripStatusUpdateMessage>builder()
                        .transactionContext(header)
                        .message(null)
                        .build();

        when(kafkaProducer.publishEvent(requestWithNullMessage)).thenReturn(true);

        boolean result = publisher.process(requestWithNullMessage);

        assertTrue(result);
        verify(kafkaProducer).publishEvent(requestWithNullMessage);
    }

    @Test
    void process_MultipleConsecutiveCalls_ShouldProcessEach() {
        when(kafkaProducer.publishEvent(any())).thenReturn(true);

        boolean result1 = publisher.process(validRequest);
        boolean result2 = publisher.process(validRequest);
        boolean result3 = publisher.process(validRequest);

        assertTrue(result1);
        assertTrue(result2);
        assertTrue(result3);
        verify(kafkaProducer, times(3)).publishEvent(validRequest);
    }

    @Test
    void process_KafkaProducerThrowsSpecificException_ShouldPropagateException() {
        RuntimeException specificException = new IllegalStateException("Kafka connection lost");
        when(kafkaProducer.publishEvent(validRequest)).thenThrow(specificException);

        IllegalStateException exception = assertThrows(IllegalStateException.class, () -> {
            publisher.process(validRequest);
        });

        assertEquals("Kafka connection lost", exception.getMessage());
        verify(kafkaProducer).publishEvent(validRequest);
    }
}