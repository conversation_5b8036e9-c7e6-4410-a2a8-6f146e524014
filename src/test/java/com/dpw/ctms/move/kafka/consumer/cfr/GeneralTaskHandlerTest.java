package com.dpw.ctms.move.kafka.consumer.cfr;

import com.dpw.ctms.move.BaseTest;
import com.dpw.ctms.move.dto.ParamValueShipmentDTO;
import com.dpw.ctms.move.dto.TimeDTO;
import com.dpw.ctms.move.dto.consumer.IntegratorHeaderDTO;
import com.dpw.ctms.move.dto.consumer.IntegratorTaskMessageRequestDTO;
import com.dpw.ctms.move.dto.consumer.cfr.GeneralTaskMessageDTO;
import com.dpw.ctms.move.entity.*;
import com.dpw.ctms.move.enums.StateMachineEntityType;
import com.dpw.ctms.move.enums.TaskParamType;
import com.dpw.ctms.move.enums.TaskStatus;
import com.dpw.ctms.move.integration.adapter.OmsServiceAdapter;
import com.dpw.ctms.move.integration.adapter.ResourceServiceAdapter;
import com.dpw.ctms.move.integration.adapter.TrackingServiceAdapter;
import com.dpw.ctms.move.integration.dto.trackingservice.TrackingServiceIntegrationToggleDTO;
import com.dpw.ctms.move.integration.response.ListResponse;
import com.dpw.ctms.move.integration.response.oms.OmsListResponse;
import com.dpw.ctms.move.integration.response.oms.consignment.ConsignmentRecord;
import com.dpw.ctms.move.integration.response.resource.facility.FacilityRecord;
import com.dpw.ctms.move.integration.service.impl.UnleashConfigServiceIntegratorImpl;
import com.dpw.ctms.move.repository.StopTaskRepository;
import com.dpw.ctms.move.service.IShipmentService;
import com.dpw.ctms.move.service.ITaskService;
import com.dpw.ctms.move.service.ITripService;
import com.dpw.ctms.move.statemachine.IStateMachineService;
import com.dpw.ctms.move.statemachine.registry.StateMachineServiceRegistry;
import com.dpw.tmsutils.utils.ObjectMapperUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static com.dpw.ctms.move.fakers.Fakers.createFacilityResponse;
import static com.dpw.ctms.move.fakers.Fakers.createOmsConsignmentResponse;
import static com.dpw.ctms.move.fakers.Fakers.createTripTrackingResponse;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

@SpringBootTest
@Transactional
@AutoConfigureMockMvc
class GeneralTaskHandlerTest extends BaseTest {
    @Autowired
    private ITaskService taskService;
    @Autowired
    private IShipmentService shipmentService;
    @Autowired
    private ITripService tripService;
    @Autowired
    private GeneralTaskHandler generalTaskHandler;
    @MockBean
    private StateMachineServiceRegistry stateMachineServiceRegistry;
    @Autowired
    private StopTaskRepository stopTaskRepository;
    @MockBean
    private ResourceServiceAdapter resourceServiceAdapter;
    @MockBean
    UnleashConfigServiceIntegratorImpl configServiceIntegrator;
    @MockBean
    private TrackingServiceAdapter trackingServiceAdapter;
    @MockBean
    private OmsServiceAdapter omsServiceAdapter;

    @BeforeEach
    void mockStateMachine() throws JsonProcessingException {
        IStateMachineService<?> mockStateMachine = Mockito.mock(IStateMachineService.class);
        Mockito.doNothing().when(mockStateMachine).handleEvent(Mockito.any(), Mockito.any(), Mockito.any());
        when(stateMachineServiceRegistry.getService(Mockito.any(StateMachineEntityType.class)))
                .thenAnswer(invocation -> mockStateMachine);
        OmsListResponse<ConsignmentRecord> consignmentResponse = createOmsConsignmentResponse();
        ListResponse<FacilityRecord> facilityRecordListResponse = createFacilityResponse();


        when(trackingServiceAdapter.trackTrip(any())).thenReturn(createTripTrackingResponse());

        when(omsServiceAdapter.getConsignmentList(any(), any())).thenReturn(consignmentResponse);
        when(resourceServiceAdapter.getFacilityList(any(), any())).thenReturn(facilityRecordListResponse);

        TrackingServiceIntegrationToggleDTO mockDto = TrackingServiceIntegrationToggleDTO.builder()
                .isEnabled(false)
                .build();

        when(configServiceIntegrator.fetchConfig(
                anyString(),
                any(TypeReference.class)
        )).thenReturn(mockDto);
    }

    @Test
    void testHandle() {

        Trip trip = new Trip();
        trip.setActualStartAt(null);

        // Create and initialize 2 stops for the trip
        Stop stop1 = new Stop();
        stop1.setCode("STOP1");
        stop1.setSequence(1);
        stop1.setTrip(trip);

        Stop stop2 = new Stop();
        stop2.setCode("STOP2");
        stop2.setSequence(2);
        stop2.setTrip(trip);

        Set<Stop> stops = new HashSet<>();
        stops.add(stop1);
        stops.add(stop2);
        trip.setStops(stops);

        trip = tripService.saveTrip(trip);

        Shipment shipment = new Shipment();
        shipment.setCode("SHIP1");
        shipment.setTrip(trip);
        shipment.setActualPickupAt(null);
        shipment = shipmentService.saveShipment(shipment);

        TaskParam taskParam = new TaskParam();
        taskParam.setParamName(TaskParamType.SHIPMENT);
        ParamValueShipmentDTO shipmentDTO = new ParamValueShipmentDTO();
        shipmentDTO.setCode("SHIP1");
        taskParam.setParamValue(ObjectMapperUtil.getObjectMapper().valueToTree(shipmentDTO));

        Task task = new Task();
        task.setCode("TASK123");
        task.setTaskParams(new ArrayList<>(List.of(taskParam)));
        taskService.saveTask(task);

        StopTask stopTask = new StopTask();
        stopTask.setTask(task);
        stopTask.setStop(stop1);
        stopTask = stopTaskRepository.save(stopTask);

        task.setStopTask(stopTask);
        taskService.saveTask(task);

        GeneralTaskMessageDTO.PercolatedRecordDTO percolatedRecordDTO = GeneralTaskMessageDTO.PercolatedRecordDTO.builder().build();
        IntegratorTaskMessageRequestDTO.IntegratorTaskDetailsRequestDTO taskDetails =
                IntegratorTaskMessageRequestDTO.IntegratorTaskDetailsRequestDTO.builder()
                        .taskTransactionCode("TASK123")
                        .status(TaskStatus.CLOSED.name())
                        .actualTimeRange(IntegratorTaskMessageRequestDTO.ActualTimeRange.builder()
                                .from(TimeDTO.builder().epoch(1718500000000L).timezone("Asia/Kolkata").build())
                                .to(TimeDTO.builder().epoch(1718600000000L).timezone("Asia/Kolkata").build()).build())
                        .build();
        IntegratorTaskMessageRequestDTO.MessageRequestDTO<GeneralTaskMessageDTO.PercolatedRecordDTO> messageDTO =
                IntegratorTaskMessageRequestDTO.MessageRequestDTO.<GeneralTaskMessageDTO.PercolatedRecordDTO>builder()
                        .percolatedRecords(percolatedRecordDTO)
                        .taskDetails(taskDetails)
                        .build();
        GeneralTaskMessageDTO generalTaskMessageDTO = GeneralTaskMessageDTO.builder()
                .message(messageDTO)
                .transactionContext(IntegratorHeaderDTO.builder().dateTime(1718600000000L).build())
                .build();
        generalTaskHandler.handle(generalTaskMessageDTO);
        Task updatedTask = taskService.findTaskByCode("TASK123");
        Long taskEndAtEpoch = taskDetails.getActualTimeRange().getTo().getEpoch();
        assertThat(taskEndAtEpoch).isEqualTo(updatedTask.getActualEndAt().getEpoch());
    }
}
