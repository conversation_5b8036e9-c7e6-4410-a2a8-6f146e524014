package com.dpw.ctms.move.validator;


import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.enums.TripStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class TripValidatorTest {

    private TripValidator tripValidator;

    @BeforeEach
    void setUp() {
        tripValidator = new TripValidator();
    }

    @Test
    void testValidateTripStart_shouldReturnTrue() {
        Trip initial = new Trip();
        initial.setStatus(TripStatus.CREATED);

        Trip current = new Trip();
        current.setStatus(TripStatus.IN_PROGRESS);

        boolean result = tripValidator.validateTripStart(initial, current);

        assertThat(result).isTrue();
    }

    @Test
    void testValidateTripStart_shouldReturnFalse_whenWrongStatus() {
        Trip initial = new Trip();
        initial.setStatus(TripStatus.IN_PROGRESS);

        Trip current = new Trip();
        current.setStatus(TripStatus.IN_PROGRESS);

        boolean result = tripValidator.validateTripStart(initial, current);

        assertThat(result).isFalse();
    }

    @Test
    void testValidateTripEnd_shouldReturnTrue() {
        Trip initial = new Trip();
        initial.setStatus(TripStatus.IN_PROGRESS);

        Trip current = new Trip();
        current.setStatus(TripStatus.COMPLETED);

        boolean result = tripValidator.validateTripEnd(initial, current);

        assertThat(result).isTrue();
    }

    @Test
    void testValidateTripEnd_shouldReturnFalse_whenStatusIncorrect() {
        Trip initial = new Trip();
        initial.setStatus(TripStatus.CREATED);

        Trip current = new Trip();
        current.setStatus(TripStatus.COMPLETED);

        boolean result = tripValidator.validateTripEnd(initial, current);

        assertThat(result).isFalse();
    }
}
