package com.dpw.ctms.move.service.statemachine;

import com.dpw.ctms.move.dto.StateTransitionHolderDTO;
import com.dpw.ctms.move.integration.service.impl.UnleashConfigServiceIntegratorImpl;
import com.dpw.ctms.move.statemachine.impl.TransportOrderStateMachineConfigFetcher;
import com.dpw.tmsutils.exception.TMSException;
import com.dpw.tmsutils.utils.ObjectMapperUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import static com.dpw.ctms.move.constants.ConfigConstants.TO_STATE_MACHINE_CONFIG;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


@ExtendWith(MockitoExtension.class)
class TransportOrderStateMachineConfigFetcherTest {

    @Mock
    private UnleashConfigServiceIntegratorImpl configServiceIntegrator;

    private TransportOrderStateMachineConfigFetcher configFetcher;


    @BeforeEach
    void setUp() {
        configFetcher = new TransportOrderStateMachineConfigFetcher(configServiceIntegrator);
    }

    @Test
    void shouldReturnDTO_WhenValidConfigReturned() throws Exception {
        // given
        StateTransitionHolderDTO expectedDto = new StateTransitionHolderDTO();


        JsonNode jsonNode = ObjectMapperUtil.getObjectMapper().valueToTree(expectedDto);

        when(configServiceIntegrator.fetchConfig(TO_STATE_MACHINE_CONFIG)).thenReturn(jsonNode);

        // when
        StateTransitionHolderDTO result = configFetcher.fetchStateMachineConfig();

        // then
        assertNotNull(result);
        verify(configServiceIntegrator).fetchConfig(TO_STATE_MACHINE_CONFIG);
    }

    @Test
    void shouldThrowTMSException_WhenConfigIsNull() {
        // given
        when(configServiceIntegrator.fetchConfig(TO_STATE_MACHINE_CONFIG)).thenReturn(null);

        // when & then
        TMSException ex = assertThrows(TMSException.class, () -> configFetcher.fetchStateMachineConfig());
        assertEquals("INVALID_CONFIG", ex.getErrorCode());
    }

    @Test
    void shouldThrowTMSException_WhenJsonMappingFails() throws Exception{
        // given
        JsonNode malformedNode = mock(JsonNode.class); // intentionally mock to break mapping
        when(configServiceIntegrator.fetchConfig(TO_STATE_MACHINE_CONFIG)).thenReturn(malformedNode);

        // force ObjectMapper to throw by giving a node it cannot map
        try (MockedStatic<ObjectMapperUtil> mocked = mockStatic(ObjectMapperUtil.class)) {
            ObjectMapper mockMapper = mock(ObjectMapper.class);
            mocked.when(ObjectMapperUtil::getObjectMapper).thenReturn(mockMapper);

            when(mockMapper.treeToValue(eq(malformedNode), eq(StateTransitionHolderDTO.class)))
                    .thenThrow(new RuntimeException("Mapping error"));

            // when & then
            TMSException ex = assertThrows(TMSException.class, () -> configFetcher.fetchStateMachineConfig());
            assertEquals("INVALID_CONFIG", ex.getErrorCode());
        }
    }
}
