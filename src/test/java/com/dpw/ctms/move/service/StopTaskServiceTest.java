package com.dpw.ctms.move.service;

import com.dpw.ctms.move.entity.StopTask;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.repository.StopTaskRepository;
import com.dpw.ctms.move.service.impl.StopTaskServiceImpl;
import com.dpw.tmsutils.exception.TMSException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Optional;

import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.DATA_NOT_FOUND;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class StopTaskServiceTest {

    private StopTaskRepository stopTaskRepository;
    private StopTaskServiceImpl stopTaskService;

    @BeforeEach
    void setUp() {
        stopTaskRepository = mock(StopTaskRepository.class);
        stopTaskService = new StopTaskServiceImpl(stopTaskRepository);
    }

    @Test
    void testGetStopByTask_whenStopTaskExists_shouldReturnStopTask() {
        Task task = new Task();
        task.setCode("TASK123");

        StopTask expectedStopTask = new StopTask();
        when(stopTaskRepository.findByTask(task)).thenReturn(Optional.of(expectedStopTask));

        StopTask actualStopTask = stopTaskService.getStopByTask(task);
        assertEquals(expectedStopTask, actualStopTask);
        verify(stopTaskRepository, times(1)).findByTask(task);
    }

    @Test
    void testGetStopByTask_whenStopTaskNotFound_shouldThrowTMSException() {
        Task task = new Task();
        task.setCode("TASK404");

        when(stopTaskRepository.findByTask(task)).thenReturn(Optional.empty());

        TMSException exception = assertThrows(TMSException.class, () -> stopTaskService.getStopByTask(task));
        assertEquals(DATA_NOT_FOUND.name(), exception.getErrorCode());
        verify(stopTaskRepository, times(1)).findByTask(task);
    }
}

