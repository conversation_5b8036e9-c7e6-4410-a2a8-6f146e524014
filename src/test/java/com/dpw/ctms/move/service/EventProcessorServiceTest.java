package com.dpw.ctms.move.service;

import com.dpw.ctms.move.BaseTest;
import com.dpw.ctms.move.dto.TimeDTO;
import com.dpw.ctms.move.kafka.producer.processor.IEventRequestPublisher;
import com.dpw.ctms.move.request.common.IntegratorMessageHeader;
import com.dpw.ctms.move.request.common.IntegratorMessageRequest;
import com.dpw.ctms.move.request.common.MessageRequest;
import com.dpw.ctms.move.request.message.ShipmentStatusUpdateMessage;
import com.dpw.ctms.move.request.message.TaskStatusUpdateMessage;
import com.dpw.ctms.move.request.message.TripStatusUpdateMessage;
import com.dpw.ctms.move.response.message.IntegratorMessageResponse;
import com.dpw.ctms.move.service.impl.EventProcessorServiceImpl;
import com.dpw.tmsutils.exception.TMSException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static com.dpw.ctms.move.constants.MoveConstants.SOURCE_MOVE;
import static com.dpw.ctms.move.enums.MessageActionType.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class EventProcessorServiceTest {

    private EventProcessorServiceImpl<ShipmentStatusUpdateMessage> shipmentEventProcessorService;
    private EventProcessorServiceImpl<TaskStatusUpdateMessage> taskEventProcessorService;
    private EventProcessorServiceImpl<TripStatusUpdateMessage> tripEventProcessorService;

    @Mock
    private IEventRequestPublisher<ShipmentStatusUpdateMessage> shipmentEventRequestPublisher;

    @Mock
    private IEventRequestPublisher<TaskStatusUpdateMessage> taskEventRequestPublisher;

    @Mock
    private IEventRequestPublisher<TripStatusUpdateMessage> tripEventRequestPublisher;

    @BeforeEach
    void setUp() {
        shipmentEventProcessorService = new EventProcessorServiceImpl<>();
        taskEventProcessorService = new EventProcessorServiceImpl<>();
        tripEventProcessorService = new EventProcessorServiceImpl<>();
    }

    // Unit Tests
    @Test
    void addRequestProcessor_ShouldRegisterProcessor() {
        String actionCode = SHIPMENT_STATUS_UPDATE.name();
        
        shipmentEventProcessorService.addRequestProcessor(actionCode, shipmentEventRequestPublisher);
        
        IntegratorMessageRequest<ShipmentStatusUpdateMessage> request = createShipmentMessageRequest();
        when(shipmentEventRequestPublisher.process(request)).thenReturn(true);
        
        IntegratorMessageResponse response = shipmentEventProcessorService.processRequest(actionCode, request);
        
        assertNotNull(response);
        assertTrue(response.isSuccess());
        verify(shipmentEventRequestPublisher).process(request);
    }

    @Test
    void addRequestProcessor_WithDuplicateActionCode_ShouldNotOverrideExisting() {
        String actionCode = SHIPMENT_STATUS_UPDATE.name();
        IEventRequestPublisher<ShipmentStatusUpdateMessage> secondPublisher = mock(IEventRequestPublisher.class);
        
        shipmentEventProcessorService.addRequestProcessor(actionCode, shipmentEventRequestPublisher);
        shipmentEventProcessorService.addRequestProcessor(actionCode, secondPublisher);
        
        IntegratorMessageRequest<ShipmentStatusUpdateMessage> request = createShipmentMessageRequest();
        when(shipmentEventRequestPublisher.process(request)).thenReturn(true);
        
        shipmentEventProcessorService.processRequest(actionCode, request);
        
        verify(shipmentEventRequestPublisher).process(request);
        verify(secondPublisher, never()).process(any());
    }

    @Test
    void processRequest_WithValidActionCode_ShouldProcessSuccessfully() {
        String actionCode = TASK_STATUS_UPDATE.name();
        taskEventProcessorService.addRequestProcessor(actionCode, taskEventRequestPublisher);
        
        IntegratorMessageRequest<TaskStatusUpdateMessage> request = createTaskMessageRequest();
        when(taskEventRequestPublisher.process(request)).thenReturn(true);
        
        IntegratorMessageResponse response = taskEventProcessorService.processRequest(actionCode, request);
        
        assertNotNull(response);
        assertTrue(response.isSuccess());
        verify(taskEventRequestPublisher).process(request);
    }

    @Test
    void processRequest_WithEmptyActionCode_ShouldThrowTMSException() {
        IntegratorMessageRequest<ShipmentStatusUpdateMessage> request = createShipmentMessageRequest();
        
        TMSException exception = assertThrows(TMSException.class, () ->
                shipmentEventProcessorService.processRequest("", request));
        
        assertEquals("INVALID_REQUEST", exception.getErrorCode());
        assertTrue(exception.getErrorMessage().contains("Action type is invalid"));
    }

    @Test
    void processRequest_WithNullActionCode_ShouldThrowTMSException() {
        IntegratorMessageRequest<ShipmentStatusUpdateMessage> request = createShipmentMessageRequest();
        
        TMSException exception = assertThrows(TMSException.class, () ->
                shipmentEventProcessorService.processRequest(null, request));
        
        assertEquals("INVALID_REQUEST", exception.getErrorCode());
        assertTrue(exception.getErrorMessage().contains("Action type is invalid"));
    }

    @Test
    void processRequest_WithUnregisteredActionCode_ShouldThrowTMSException() {
        String actionCode = "UNKNOWN_ACTION";
        IntegratorMessageRequest<ShipmentStatusUpdateMessage> request = createShipmentMessageRequest();
        
        TMSException exception = assertThrows(TMSException.class, () ->
                shipmentEventProcessorService.processRequest(actionCode, request));
        
        assertEquals("INVALID_REQUEST", exception.getErrorCode());
        assertTrue(exception.getErrorMessage().contains("No processor found for given action"));
    }

    @Test
    void processRequest_WhenProcessorReturnsFalse_ShouldReturnFailureResponse() {
        String actionCode = TRIP_STATUS_UPDATE.name();
        tripEventProcessorService.addRequestProcessor(actionCode, tripEventRequestPublisher);
        
        IntegratorMessageRequest<TripStatusUpdateMessage> request = createTripMessageRequest();
        when(tripEventRequestPublisher.process(request)).thenReturn(false);
        
        IntegratorMessageResponse response = tripEventProcessorService.processRequest(actionCode, request);
        
        assertNotNull(response);
        assertFalse(response.isSuccess());
        verify(tripEventRequestPublisher).process(request);
    }

    @Test
    void processRequest_WhenProcessorThrowsException_ShouldPropagateException() {
        String actionCode = SHIPMENT_STATUS_UPDATE.name();
        shipmentEventProcessorService.addRequestProcessor(actionCode, shipmentEventRequestPublisher);
        
        IntegratorMessageRequest<ShipmentStatusUpdateMessage> request = createShipmentMessageRequest();
        RuntimeException processorException = new RuntimeException("Kafka publishing failed");
        when(shipmentEventRequestPublisher.process(request)).thenThrow(processorException);
        
        RuntimeException exception = assertThrows(RuntimeException.class, () ->
                shipmentEventProcessorService.processRequest(actionCode, request));
        
        assertEquals("Kafka publishing failed", exception.getMessage());
        verify(shipmentEventRequestPublisher).process(request);
    }

    @Test
    void integrationTest_MultipleProcessors_ShouldUseCorrectProcessor() {
        String shipmentActionCode = SHIPMENT_STATUS_UPDATE.name();
        String taskActionCode = TASK_STATUS_UPDATE.name();
        
        shipmentEventProcessorService.addRequestProcessor(shipmentActionCode, shipmentEventRequestPublisher);
        taskEventProcessorService.addRequestProcessor(taskActionCode, taskEventRequestPublisher);
        
        IntegratorMessageRequest<ShipmentStatusUpdateMessage> shipmentRequest = createShipmentMessageRequest();
        IntegratorMessageRequest<TaskStatusUpdateMessage> taskRequest = createTaskMessageRequest();
        
        when(shipmentEventRequestPublisher.process(shipmentRequest)).thenReturn(true);
        when(taskEventRequestPublisher.process(taskRequest)).thenReturn(true);
        
        IntegratorMessageResponse shipmentResponse = shipmentEventProcessorService.processRequest(shipmentActionCode, shipmentRequest);
        IntegratorMessageResponse taskResponse = taskEventProcessorService.processRequest(taskActionCode, taskRequest);
        
        assertTrue(shipmentResponse.isSuccess());
        assertTrue(taskResponse.isSuccess());
        verify(shipmentEventRequestPublisher).process(shipmentRequest);
        verify(taskEventRequestPublisher).process(taskRequest);
    }


    @Test
    void integrationTest_EndToEndEventProcessing_ShouldHandleAllEntityTypes() {
        // Setup all processors
        shipmentEventProcessorService.addRequestProcessor(SHIPMENT_STATUS_UPDATE.name(), shipmentEventRequestPublisher);
        taskEventProcessorService.addRequestProcessor(TASK_STATUS_UPDATE.name(), taskEventRequestPublisher);
        tripEventProcessorService.addRequestProcessor(TRIP_STATUS_UPDATE.name(), tripEventRequestPublisher);
        
        // Create requests
        IntegratorMessageRequest<ShipmentStatusUpdateMessage> shipmentRequest = createShipmentMessageRequest();
        IntegratorMessageRequest<TaskStatusUpdateMessage> taskRequest = createTaskMessageRequest();
        IntegratorMessageRequest<TripStatusUpdateMessage> tripRequest = createTripMessageRequest();
        
        // Mock responses
        when(shipmentEventRequestPublisher.process(shipmentRequest)).thenReturn(true);
        when(taskEventRequestPublisher.process(taskRequest)).thenReturn(true);
        when(tripEventRequestPublisher.process(tripRequest)).thenReturn(true);
        
        // Process all requests
        IntegratorMessageResponse shipmentResponse = shipmentEventProcessorService.processRequest(SHIPMENT_STATUS_UPDATE.name(), shipmentRequest);
        IntegratorMessageResponse taskResponse = taskEventProcessorService.processRequest(TASK_STATUS_UPDATE.name(), taskRequest);
        IntegratorMessageResponse tripResponse = tripEventProcessorService.processRequest(TRIP_STATUS_UPDATE.name(), tripRequest);
        
        // Verify all processed successfully
        assertTrue(shipmentResponse.isSuccess());
        assertTrue(taskResponse.isSuccess());
        assertTrue(tripResponse.isSuccess());
        
        verify(shipmentEventRequestPublisher).process(shipmentRequest);
        verify(taskEventRequestPublisher).process(taskRequest);
        verify(tripEventRequestPublisher).process(tripRequest);
    }

    private IntegratorMessageRequest<ShipmentStatusUpdateMessage> createShipmentMessageRequest() {
        ShipmentStatusUpdateMessage message = ShipmentStatusUpdateMessage.builder()
                .currentStatus("PICKED_UP")
                .previousStatus("CREATED")
                .shipmentCode("SHP001")
                .extShipmentCode("EXT-SHP001")
                .pickupTime(new TimeDTO(System.currentTimeMillis(), "UTC"))
                .updatedBy("test-user")
                .updatedAt(System.currentTimeMillis())
                .eventType("STATUS_UPDATE")
                .build();
        
        return IntegratorMessageRequest.<ShipmentStatusUpdateMessage>builder()
                .transactionContext(IntegratorMessageHeader.builder()
                        .action(SHIPMENT_STATUS_UPDATE.name())
                        .dateTime(System.currentTimeMillis())
                        .source(SOURCE_MOVE)
                        .topic("test-shipment-topic")
                        .build())
                .message(MessageRequest.<ShipmentStatusUpdateMessage>builder()
                        .item(message)
                        .build())
                .build();
    }

    private IntegratorMessageRequest<TaskStatusUpdateMessage> createTaskMessageRequest() {
        TaskStatusUpdateMessage message = TaskStatusUpdateMessage.builder()
                .currentStatus("IN_PROGRESS")
                .previousStatus("CREATED")
                .taskCode("TASK001")
                .extTaskCode("EXT-TASK001")
                .taskRegistrationCode("REG-001")
                .startTime(new TimeDTO(System.currentTimeMillis(), "UTC"))
                .updatedBy("test-user")
                .updatedAt(System.currentTimeMillis())
                .eventType("STATUS_UPDATE")
                .build();
        
        return IntegratorMessageRequest.<TaskStatusUpdateMessage>builder()
                .transactionContext(IntegratorMessageHeader.builder()
                        .action(TASK_STATUS_UPDATE.name())
                        .dateTime(System.currentTimeMillis())
                        .source(SOURCE_MOVE)
                        .topic("test-task-topic")
                        .build())
                .message(MessageRequest.<TaskStatusUpdateMessage>builder()
                        .item(message)
                        .build())
                .build();
    }

    private IntegratorMessageRequest<TripStatusUpdateMessage> createTripMessageRequest() {
        TripStatusUpdateMessage message = TripStatusUpdateMessage.builder()
                .currentStatus("IN_PROGRESS")
                .previousStatus("PLANNED")
                .tripCode("TRIP001")
                .extTripCode("EXT-TRIP001")
                .startTime(new TimeDTO(System.currentTimeMillis(), "UTC"))
                .updatedBy("test-user")
                .updatedAt(System.currentTimeMillis())
                .eventType("STATUS_UPDATE")
                .build();
        
        return IntegratorMessageRequest.<TripStatusUpdateMessage>builder()
                .transactionContext(IntegratorMessageHeader.builder()
                        .action(TRIP_STATUS_UPDATE.name())
                        .dateTime(System.currentTimeMillis())
                        .source(SOURCE_MOVE)
                        .topic("test-trip-topic")
                        .build())
                .message(MessageRequest.<TripStatusUpdateMessage>builder()
                        .item(message)
                        .build())
                .build();
    }
}