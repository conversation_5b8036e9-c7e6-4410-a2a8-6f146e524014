package com.dpw.ctms.move.service.statemachine;

import com.dpw.ctms.move.BaseTest;
import com.dpw.ctms.move.constants.ConfigConstants;
import com.dpw.ctms.move.dto.StateMachineTenantDTO;
import com.dpw.ctms.move.dto.StateTransitionHolderDTO;
import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.enums.*;
import com.dpw.ctms.move.fakers.Fakers;
import com.dpw.ctms.move.helper.ShipmentOperationInDB;
import com.dpw.ctms.move.helper.TaskOperationInDB;
import com.dpw.ctms.move.helper.TransportOrderOperationInDB;
import com.dpw.ctms.move.helper.TripOperationInDB;
import com.dpw.ctms.move.integration.service.impl.UnleashConfigServiceIntegratorImpl;
import com.dpw.ctms.move.statemachine.IStateMachineService;
import com.dpw.ctms.move.statemachine.registry.StateMachineServiceRegistry;
import com.dpw.tmsutils.exception.TMSException;
import com.dpw.tmsutils.utils.ObjectMapperUtil;
import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.util.Map;

import static org.mockito.Mockito.when;


public class StateMachineTest extends BaseTest {

    @Autowired
    private TaskOperationInDB taskOperationInDB;

    @Autowired
    private StateMachineServiceRegistry stateMachineServiceRegistry;

    @Autowired
    private ShipmentOperationInDB shipmentOperationInDB;

    @Autowired
    private TripOperationInDB tripOperationInDB;

    @Autowired
    private TransportOrderOperationInDB transportOrderOperationInDB;

    @MockBean
    private UnleashConfigServiceIntegratorImpl configServiceIntegrator;


    @Test
    public void exception_thrown_when_is_initial_is_not_set_for_any_states() {
        Task task = taskOperationInDB.createTask();
        IStateMachineService<?> stateMachineService = stateMachineServiceRegistry
                .getService(StateMachineEntityType.TASK);
        TMSException exception = Assertions.assertThrows(TMSException.class, () -> {
            stateMachineService.handleEvent("CFR", "TASK_COMPLETED", task.getId());
        });
        Assertions.assertTrue(exception.getErrorMessage().contains("Exception occurred while creating state machine"));
    }

    @Test
    public void change_task_status_from_CREATED_to_COMPLETED() {
        Map<String, StateMachineTenantDTO> stateMachineTenantDTOMap = Fakers.createStateMachineTenantConfigMap("CFR");
        StateTransitionHolderDTO taskStateTransitionHolder = stateMachineTenantDTOMap.get("CFR").getTask();
        JsonNode taskStateTransitionHolderJsonNode = convertDtoToJsonNode(taskStateTransitionHolder);

        when(configServiceIntegrator.fetchConfig(ConfigConstants.TASK_STATE_MACHINE_CONFIG)).thenReturn(
                taskStateTransitionHolderJsonNode
        );

        Task task = taskOperationInDB.createTask();
        IStateMachineService<?> stateMachineService = stateMachineServiceRegistry
                .getService(StateMachineEntityType.TASK);
        stateMachineService.handleEvent("CFR", "TASK_COMPLETED", task.getId());
        Assertions.assertEquals(TaskStatus.COMPLETED, taskOperationInDB.getTaskById(task.getId()).getStatus());
    }


    @Test
    public void change_shipment_status_from_ASSIGNED_to_ALLOCATED() {
        Map<String, StateMachineTenantDTO> stateMachineTenantDTOMap = Fakers.createStateMachineTenantConfigMap("CFR");
        StateTransitionHolderDTO shipmentStateTransitionHolder = stateMachineTenantDTOMap.get("CFR").getShipment();
        JsonNode shipmentStateTransitionHolderJsonNode = convertDtoToJsonNode(shipmentStateTransitionHolder);

        when(configServiceIntegrator.fetchConfig(ConfigConstants.SHIPMENT_STATE_MACHINE_CONFIG)).thenReturn(
                shipmentStateTransitionHolderJsonNode
        );

        Shipment shipment = shipmentOperationInDB.createShipment();
        IStateMachineService<?> stateMachineService = stateMachineServiceRegistry
                .getService(StateMachineEntityType.SHIPMENT);
        stateMachineService.handleEvent("CFR",
                ShipmentLifecycleEvent.RESOURCE_ALLOCATED.name(), shipment.getId());
        Assertions.assertEquals(ShipmentStatus.ALLOCATED,
                shipmentOperationInDB.getShipmentById(shipment.getId()).getStatus());
    }

    @Test
    public void change_trip_status_from_CREATED_to_IN_PROGRESS() {
        Map<String, StateMachineTenantDTO> stateMachineTenantDTOMap = Fakers.createStateMachineTenantConfigMap("CFR");
        StateTransitionHolderDTO tripStateTransitionHolder = stateMachineTenantDTOMap.get("CFR").getTrip();
        JsonNode tripStateTransitionHolderJsonNode = convertDtoToJsonNode(tripStateTransitionHolder);

        when(configServiceIntegrator.fetchConfig(ConfigConstants.TRIP_STATE_MACHINE_CONFIG)).thenReturn(
                tripStateTransitionHolderJsonNode
        );

        Trip trip = tripOperationInDB.createTrip();
        IStateMachineService<?> stateMachineService = stateMachineServiceRegistry
                .getService(StateMachineEntityType.TRIP);
        stateMachineService.handleEvent("CFR",
                TripLifecycleEvent.START_TRIP.name(), trip.getId());
        Assertions.assertEquals(TripStatus.IN_PROGRESS,
                tripOperationInDB.getTripById(trip.getId()).getStatus());
    }

    @Test
    public void change_transport_order_status_from_ASSIGNED_to_IN_PROGESS() {
        Map<String, StateMachineTenantDTO> stateMachineTenantDTOMap = Fakers.createStateMachineTenantConfigMap("CFR");
        StateTransitionHolderDTO toStateTransitionHolder = stateMachineTenantDTOMap.get("CFR").getTransportOrder();
        JsonNode toStateTransitionHolderJsonNode = convertDtoToJsonNode(toStateTransitionHolder);

        when(configServiceIntegrator.fetchConfig(ConfigConstants.TO_STATE_MACHINE_CONFIG)).thenReturn(
                toStateTransitionHolderJsonNode
        );

        TransportOrder transportOrder = transportOrderOperationInDB.createTransportOrder();
        IStateMachineService<?> stateMachineService = stateMachineServiceRegistry
                .getService(StateMachineEntityType.TRANSPORT_ORDER);
        stateMachineService.handleEvent("CFR",
                TransportOrderLifecycleEvent.START_TRANSPORT_ORDER_EXECUTION.name(), transportOrder.getId());
        Assertions.assertEquals(TransportOrderStatus.IN_PROGRESS,
                transportOrderOperationInDB.findById(transportOrder.getId()).getStatus());
    }

    private JsonNode convertDtoToJsonNode(StateTransitionHolderDTO stateTransitionHolderDTO) {
        return ObjectMapperUtil.getObjectMapper().valueToTree(stateTransitionHolderDTO);
    }
}