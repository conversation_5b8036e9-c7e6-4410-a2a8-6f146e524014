package com.dpw.ctms.move.service;

import com.dpw.ctms.move.config.ConfigService;
import com.dpw.ctms.move.constants.ConfigConstants;
import com.dpw.ctms.move.integration.service.IConfigServiceIntegrator;
import com.dpw.ctms.move.dto.DocumentUpdatedEventDTO;
import com.dpw.ctms.move.dto.EntityTypeWithIdsDTO;
import com.dpw.ctms.move.dto.GetDocumentsDto;
import com.dpw.ctms.move.dto.config.RunnerApiConfigDto;
import com.dpw.ctms.move.dto.document.DeliveryTaskDocumentDTO;
import com.dpw.ctms.move.entity.Document;
import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.enums.*;
import com.dpw.ctms.move.fakers.Fakers;
import com.dpw.ctms.move.helper.DocumentHelper;
import com.dpw.ctms.move.integration.service.IRunnerService;
import com.dpw.ctms.move.mapper.DocumentMapper;
import com.dpw.ctms.move.repository.DocumentRepository;
import com.dpw.ctms.move.repository.ShipmentRepository;
import com.dpw.ctms.move.request.EntityDocumentRequest;
import com.dpw.ctms.move.request.documentEvent.PreSignedUrlEvent;
import com.dpw.ctms.move.response.DocumentDownloadResponse;
import com.dpw.ctms.move.response.DocumentErrorResponse;
import com.dpw.ctms.move.response.EntityDocumentResponse;
import com.dpw.ctms.move.response.FileDownloadPreSignedUrlResponse;
import com.dpw.ctms.move.response.PreSignedUrlResponse;
import com.dpw.ctms.move.service.document.DocumentGenerator;
import com.dpw.ctms.move.service.document.DocumentGeneratorFactory;
import com.dpw.ctms.move.service.impl.DocumentsServiceImpl;
import com.dpw.ctms.move.util.CanonicalChecksum;
import com.dpw.ctms.move.util.DocumentUtil;
import com.dpw.tmsutils.exception.TMSException;
import com.dpw.tmsutils.schemaobjects.*;
import com.dpw.tmsutils.service.DocumentService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;
import java.util.stream.Collectors;

import static org.junit.Assert.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DocumentsServiceTest {

    @Mock
    private DocumentGeneratorFactory documentGeneratorFactory;

    @Mock
    private DocumentRepository documentRepository;

    @Mock
    private CanonicalChecksum canonicalChecksum;

    @Mock
    private DocumentService documentService;

    @Mock
    private IConfigServiceIntegrator configServiceIntegrator;

    @Mock
    private DocumentMapper documentMapper;

    @Mock
    private ShipmentRepository shipmentRepository;

    @Mock
    private IShipmentService shipmentService;

    @Mock
    private IRunnerService runnerService;
    
    @Mock
    private ApplicationEventPublisher applicationEventPublisher;
    
    @Mock
    private DocumentHelper documentHelper;

    @Mock
    private DocumentGenerator documentGenerator;

    @InjectMocks
    private DocumentsServiceImpl documentsService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(documentsService, "writeCount", 100);
    }

    @Test
    void downloadTripBolDocument_shouldGenerateNewDocumentWhenNotExists() {
        // Given
        String tripCode = "TRIP123";
        Tenant tenant = Tenant.CFR;
        Object jsonObject = Map.of("tripCode", "TRIP123");
        String checksum = "checksum123";
        String fileIdentifier = "file123";
        String presignedUrl = "https://example.com/download";

        when(documentGeneratorFactory.getGenerator(DocumentType.BOL)).thenReturn(documentGenerator);
        when(documentGenerator.generateJson(tripCode, tenant)).thenReturn(jsonObject);
        when(canonicalChecksum.generateChecksum(jsonObject)).thenReturn(checksum);
        when(documentRepository.findOne(any(Specification.class)))
                .thenReturn(Optional.empty());

        JsonNode config = objectMapper.createObjectNode().put("templateId", "template123");
        when(configServiceIntegrator.fetchConfig(any())).thenReturn(config);

        PrintBolResponse printBolResponse = new PrintBolResponse();
        printBolResponse.setFileIdentifier(fileIdentifier);
        printBolResponse.setPresignedDownloadUrl(presignedUrl);

        DocumentServiceResponse<PrintBolResponse> response = new DocumentServiceResponse<>();
        response.setData(printBolResponse);

        when(documentService.getBol(any(PrintBolRequest.class), eq("template123"))).thenReturn(response);

        // Mock the documentMapper.toEntity to return a Document
        Document mockDocument = Document.builder()
                .entityId(tripCode)
                .entityType(EntityType.TRIP.name())
                .documentType(DocumentType.BOL)
                .status(DocumentStatus.ACTIVE)
                .documentOperationType(DocumentOperationType.DOWNLOAD)
                .checksum(checksum)
                .build();
        when(documentMapper.toEntity(any())).thenReturn(mockDocument);

        // When
        DocumentDownloadResponse result = documentsService.downloadTripBolDocument(tripCode, tenant);

        // Then
        assertNotNull(result);
        assertEquals(presignedUrl, result.getPresignedDownloadUrl());

        verify(documentRepository).save(any(Document.class));
        verify(documentService).getBol(any(PrintBolRequest.class), eq("template123"));
    }

    @Test
    void downloadTripBolDocument_shouldReturnExistingDocumentPresignedUrl() {
        // Given
        String tripCode = "TRIP123";
        Tenant tenant = Tenant.CFR;
        Object jsonObject = Map.of("tripCode", "TRIP123");
        String checksum = "checksum123";
        String fileIdentifier = UUID.randomUUID().toString();
        String newPresignedUrl = "https://example.com/new-download";

        Document existingDocument = Document.builder()
                .fileIdentifier(fileIdentifier)
                .build();

        when(documentGeneratorFactory.getGenerator(DocumentType.BOL)).thenReturn(documentGenerator);
        when(documentGenerator.generateJson(tripCode, tenant)).thenReturn(jsonObject);
        when(canonicalChecksum.generateChecksum(jsonObject)).thenReturn(checksum);
        when(documentRepository.findOne(any(Specification.class)))
                .thenReturn(Optional.of(existingDocument));

        DownloadPreSignedURLResponse downloadResponse = DownloadPreSignedURLResponse.builder()
                .preSignedUrl(newPresignedUrl)
                .build();

        DocumentServiceResponse<List<DownloadPreSignedURLResponse>> response = new DocumentServiceResponse<>();
        response.setData(Collections.singletonList(downloadResponse));

        when(documentService.getDownloadPreSignedURLWithToken(any(GetDownloadPreSignedURLRequest.class)))
                .thenReturn(response);

        // When
        DocumentDownloadResponse result = documentsService.downloadTripBolDocument(tripCode, tenant);

        // Then
        assertNotNull(result);
        assertEquals(newPresignedUrl, result.getPresignedDownloadUrl());

        verify(documentRepository, never()).save(any(Document.class));
        verify(documentService, never()).getBol(any(PrintBolRequest.class), anyString());
        verify(documentService).getDownloadPreSignedURLWithToken(any(GetDownloadPreSignedURLRequest.class));
    }

    @Test
    void getPreSignedUrl_shouldReturnSuccessfully() {
        // Given
        String clientIdentifier = "client123";
        String presignedUrl = "https://example.com/upload";

        GetPreSignedURLResponse getPreSignedURLResponse = GetPreSignedURLResponse.builder()
                .preSignedUrl(presignedUrl)
                .build();

        DocumentServiceResponse<GetPreSignedURLResponse> response = new DocumentServiceResponse<>();
        response.setData(getPreSignedURLResponse);
        response.setError(false);

        when(documentService.getPreSignedURL(any(GetPreSignedURLRequest.class))).thenReturn(response);

        // When
        PreSignedUrlResponse result = documentsService.getPreSignedUrl();

        // Then
        assertNotNull(result);
        assertEquals(presignedUrl, result.getPreSignedURL());
        assertNotNull(result.getClientIdentifier());

        verify(documentService).getPreSignedURL(any(GetPreSignedURLRequest.class));
    }

    @Test
    void getPreSignedUrl_shouldThrowExceptionWhenServiceFails() {
        // Given
        DocumentServiceResponse<GetPreSignedURLResponse> response = new DocumentServiceResponse<>();
        response.setError(true);
        response.setErrorDescription("Service unavailable");

        when(documentService.getPreSignedURL(any(GetPreSignedURLRequest.class))).thenReturn(response);

        // When & Then
        assertThrows(TMSException.class, () -> documentsService.getPreSignedUrl());

        verify(documentService).getPreSignedURL(any(GetPreSignedURLRequest.class));
    }

    @Test
    void findAndUpdate_PreSignedUrlEvent_shouldUpdateInactiveDocument() {
        // Given
        String fileKey = "file123";
        PreSignedUrlEvent event = new PreSignedUrlEvent();
        event.setFileKey(fileKey);
        event.setFileIdentifier(fileKey);
        event.setClientIdentifier("client-123");

        Document inactiveDocument = Document.builder()
                .fileIdentifier(fileKey)
                .entityId("entity123")
                .asyncMappingUUID(fileKey)
                .status(DocumentStatus.INACTIVE)
                .build();

        // Mock helper methods
        when(documentHelper.extractAsyncMappingUUID(event)).thenReturn(List.of(fileKey));
        Map<String, Document> existingDocMap = new HashMap<>();
        existingDocMap.put(fileKey, inactiveDocument);
        when(documentHelper.processDocuments(eq(event), any())).thenReturn(List.of(inactiveDocument));
        when(documentHelper.filterActivatedDocuments(anyList())).thenReturn(List.of(inactiveDocument));
        
        when(documentRepository.findAll(any(Specification.class)))
                .thenReturn(Collections.singletonList(inactiveDocument));
        when(documentRepository.saveAll(anyList())).thenReturn(Collections.singletonList(inactiveDocument));

        // When
        documentsService.findAndUpdate(event);

        // Then
        verify(documentHelper).extractAsyncMappingUUID(event);
        verify(documentHelper).processDocuments(eq(event), any());
        verify(documentHelper).filterActivatedDocuments(anyList());
        verify(documentRepository).saveAll(anyList());
        
        // Verify event is published with activated documents
        verify(applicationEventPublisher).publishEvent(any(DocumentUpdatedEventDTO.class));
    }

    @Test
    void findAndUpdate_PreSignedUrlEvent_shouldNotUpdateActiveDocument() {
        // Given
        String fileKey = "file123";
        PreSignedUrlEvent event = new PreSignedUrlEvent();
        event.setFileKey(fileKey);
        event.setFileIdentifier(fileKey);
        event.setClientIdentifier("client-123");

        Document activeDocument = Document.builder()
                .fileIdentifier(fileKey)
                .asyncMappingUUID(fileKey)
                .status(DocumentStatus.ACTIVE)
                .build();

        // Mock helper methods - processDocuments returns empty list for active documents
        when(documentHelper.extractAsyncMappingUUID(event)).thenReturn(List.of(fileKey));
        when(documentHelper.processDocuments(eq(event), any())).thenReturn(Collections.emptyList());
        
        when(documentRepository.findAll(any(Specification.class)))
                .thenReturn(Collections.singletonList(activeDocument));

        // When
        documentsService.findAndUpdate(event);

        // Then
        verify(documentHelper).extractAsyncMappingUUID(event);
        verify(documentHelper).processDocuments(eq(event), any());
        verify(documentRepository, never()).saveAll(anyList());
        verify(applicationEventPublisher, never()).publishEvent(any(DocumentUpdatedEventDTO.class));
    }

    @Test
    void findAndUpdate_PreSignedUrlEvent_shouldCreateNewDocumentWhenNotExists() {
        // Given
        String fileKey = "file123";
        PreSignedUrlEvent event = new PreSignedUrlEvent();
        event.setFileKey(fileKey);
        event.setFileIdentifier(fileKey);
        event.setClientIdentifier("client-123");

        Document newDocument = Document.builder()
                .fileIdentifier(fileKey)
                .asyncMappingUUID(fileKey)
                .status(DocumentStatus.INACTIVE)
                .build();

        // Mock helper methods
        when(documentHelper.extractAsyncMappingUUID(event)).thenReturn(List.of(fileKey));
        when(documentHelper.processDocuments(eq(event), any())).thenReturn(List.of(newDocument));
        when(documentHelper.filterActivatedDocuments(anyList())).thenReturn(Collections.emptyList());
        
        when(documentRepository.findAll(any(Specification.class)))
                .thenReturn(Collections.emptyList());
        when(documentRepository.saveAll(anyList())).thenReturn(Collections.singletonList(newDocument));

        // When
        documentsService.findAndUpdate(event);

        // Then
        verify(documentHelper).extractAsyncMappingUUID(event);
        verify(documentHelper).processDocuments(eq(event), any());
        verify(documentRepository).saveAll(anyList());
    }

    @Test
    void findAndUpdate_DeliveryTaskDocumentDTO_shouldActivateDocumentAndDiscardOthers() {
        // Given
        String fileIdentifier = "file123";
        String entityId = "entity123";
        EntityType entityType = EntityType.SHIPMENT;
        DocumentOperationType operationType = DocumentOperationType.UPLOAD;

        DeliveryTaskDocumentDTO kafkaEvent = DeliveryTaskDocumentDTO.builder()
                .asyncMappingUUIDs(List.of(fileIdentifier))
                .entityId(entityId)
                .entityType(entityType.name())
                .operationType(operationType)
                .documentType(DocumentType.POD)
                .build();

        Document inactiveDocument = Document.builder()
                .asyncMappingUUID(fileIdentifier)
                .entityId(entityId)
                .fileIdentifier("file123")
                .status(DocumentStatus.INACTIVE)
                .build();

        Document activeDoc1 = Document.builder()
                .asyncMappingUUID("other1")
                .entityId(entityId)
                .status(DocumentStatus.ACTIVE)
                .build();

        Document activeDoc2 = Document.builder()
                .asyncMappingUUID("other2")
                .entityId(entityId)
                .status(DocumentStatus.ACTIVE)
                .build();

        // Mock DocumentHelper behavior
        when(documentHelper.extractAsyncMappingUUID(kafkaEvent)).thenReturn(List.of(fileIdentifier));

        Map<String, Document> existingDocMap = new HashMap<>();
        existingDocMap.put(fileIdentifier, inactiveDocument);

        when(documentHelper.processDocuments(eq(kafkaEvent), any())).thenReturn(List.of(inactiveDocument));

        // Mock helper for documents to discard
        when(documentHelper.discardDocuments(any())).thenAnswer(invocation -> {
            List<Document> docs = invocation.getArgument(0);
            docs.forEach(doc -> doc.setStatus(DocumentStatus.DISCARDED));
            return docs;
        });

        // Mock helper for documents to activate
        when(documentHelper.filterActivatedDocuments(any())).thenAnswer(invocation -> {
            List<Document> docs = invocation.getArgument(0);
            return docs.stream()
                    .filter(doc -> doc.getStatus() == DocumentStatus.INACTIVE)
                    .peek(doc -> doc.setStatus(DocumentStatus.ACTIVE))
                    .collect(Collectors.toList());
        });

        // Mock repository findAll calls for document queries
        when(documentRepository.findAll(any(Specification.class)))
                .thenReturn(List.of(inactiveDocument)) // First call for finding documents by asyncMappingUUID
                .thenReturn(List.of(activeDoc1, activeDoc2)); // Second call for finding active entity documents

        // Mock repository saveAll calls
        when(documentRepository.saveAll(anyList()))
                .thenAnswer(invocation -> invocation.getArgument(0)); // Return the saved documents

        // When
        documentsService.findAndUpdate(kafkaEvent);

        // Then
        // Verify document helper interactions
        verify(documentHelper).extractAsyncMappingUUID(kafkaEvent);
        verify(documentHelper).processDocuments(eq(kafkaEvent), any());

        // Verify repository interactions
        verify(documentRepository, times(2)).findAll(any(Specification.class));

        // Verify first saveAll call - for activating the inactive document
        verify(documentRepository).saveAll(argThat(docs -> {
            List<Document> docList = (List<Document>) docs;
            return docList.size() == 1 &&
                    docList.get(0).equals(inactiveDocument);
        }));

        // Verify second saveAll call - for discarding active documents
        verify(documentRepository).saveAll(argThat(docs -> {
            List<Document> docList = (List<Document>) docs;
            return docList.size() == 2 &&
                    docList.containsAll(List.of(activeDoc1, activeDoc2)) &&
                    docList.stream().allMatch(doc -> doc.getStatus() == DocumentStatus.DISCARDED);
        }));

        // Verify event publication
        ArgumentCaptor<DocumentUpdatedEventDTO> eventCaptor = ArgumentCaptor.forClass(DocumentUpdatedEventDTO.class);
        verify(applicationEventPublisher).publishEvent(eventCaptor.capture());

        // Verify event contents via the capture
        DocumentUpdatedEventDTO capturedEvent = eventCaptor.getValue();
        verify(documentHelper).filterActivatedDocuments(any());
        verify(documentHelper).discardDocuments(argThat(docs ->
                docs.size() == 2 &&
                        docs.containsAll(List.of(activeDoc1, activeDoc2))
        ));
    }

    @Test
    void findAndUpdate_DeliveryTaskDocumentDTO_shouldHandleMultipleAsyncMappingUUIDs() {
        // Given
        List<String> fileIdentifiers = Arrays.asList("file123", "file456", "file789");
        String entityId = "entity123";
        EntityType entityType = EntityType.TASK;
        DocumentOperationType operationType = DocumentOperationType.UPLOAD;

        DeliveryTaskDocumentDTO kafkaEvent = DeliveryTaskDocumentDTO.builder()
                .asyncMappingUUIDs(fileIdentifiers)
                .entityId(entityId)
                .entityType(entityType.name())
                .operationType(operationType)
                .documentType(DocumentType.POD)
                .build();

        // Create multiple inactive documents that should be activated
        Document inactiveDoc1 = Document.builder()
                .asyncMappingUUID("file123")
                .entityId(entityId)
                .fileIdentifier("fileId123")
                .status(DocumentStatus.INACTIVE)
                .build();

        Document inactiveDoc2 = Document.builder()
                .asyncMappingUUID("file456")
                .entityId(entityId)
                .fileIdentifier("fileId456")
                .status(DocumentStatus.INACTIVE)
                .build();

        // Mock the new document creation for file789 (not found in existing)
        Document newDoc = Document.builder()
                .asyncMappingUUID("file789")
                .entityId(entityId)
                .status(DocumentStatus.INACTIVE)
                .documentType(DocumentType.POD)
                .documentOperationType(DocumentOperationType.UPLOAD)
                .build();

        // Mock DocumentHelper
        when(documentHelper.extractAsyncMappingUUID(kafkaEvent)).thenReturn(fileIdentifiers);
        when(documentHelper.processDocuments(eq(kafkaEvent), any())).thenReturn(Arrays.asList(inactiveDoc1, inactiveDoc2, newDoc));
        when(documentHelper.filterActivatedDocuments(anyList())).thenReturn(Arrays.asList(inactiveDoc1, inactiveDoc2));

        // Mock processDocuments flow
        when(documentRepository.findAll(any(Specification.class)))
                .thenReturn(Arrays.asList(inactiveDoc1, inactiveDoc2)) // First call finds existing docs
                .thenReturn(Collections.emptyList()); // Second call for getDocuments (no active docs to discard)

        when(documentRepository.saveAll(anyList())).thenReturn(Arrays.asList(inactiveDoc1, inactiveDoc2, newDoc));

        // When
        documentsService.findAndUpdate(kafkaEvent);

        // Then
        // Verify helper methods were called
        verify(documentHelper).extractAsyncMappingUUID(kafkaEvent);
        verify(documentHelper).processDocuments(eq(kafkaEvent), any());
        verify(documentHelper).filterActivatedDocuments(anyList());
        
        // Verify documents are activated and saved
        verify(documentRepository).saveAll(anyList());
        
        // Verify event is published with only activated documents (no discarded documents)
        verify(applicationEventPublisher).publishEvent(any(DocumentUpdatedEventDTO.class));
    }

    @Test
    void findAndUpdate_DeliveryTaskDocumentDTO_shouldDiscardActiveDocumentsNotInNewList() {
        // Given - Testing the scenario where active documents should be discarded if not in delivery task's asyncMappingUUIDs
        List<String> newFileIdentifiers = Arrays.asList("file123", "file456");
        String entityId = "entity123";
        EntityType entityType = EntityType.TASK;
        DocumentOperationType operationType = DocumentOperationType.UPLOAD;

        DeliveryTaskDocumentDTO kafkaEvent = DeliveryTaskDocumentDTO.builder()
                .asyncMappingUUIDs(newFileIdentifiers)
                .entityId(entityId)
                .entityType(entityType.name())
                .operationType(operationType)
                .documentType(DocumentType.POD)
                .build();

        // Create inactive documents that should be activated
        Document inactiveDoc1 = Document.builder()
                .asyncMappingUUID("file123")
                .entityId(entityId)
                .fileIdentifier("fileId123")
                .status(DocumentStatus.INACTIVE)
                .build();

        // Create new document for file456
        Document newDoc = Document.builder()
                .asyncMappingUUID("file456")
                .entityId(entityId)
                .status(DocumentStatus.INACTIVE)
                .documentType(DocumentType.POD)
                .documentOperationType(DocumentOperationType.UPLOAD)
                .build();

        // Create active documents that should be discarded (not in delivery task's asyncMappingUUIDs)
        Document activeDocToDiscard1 = Document.builder()
                .asyncMappingUUID("oldFile789")
                .entityId(entityId)
                .entityType(entityType.name())
                .status(DocumentStatus.ACTIVE)
                .documentOperationType(DocumentOperationType.UPLOAD)
                .build();

        Document activeDocToDiscard2 = Document.builder()
                .asyncMappingUUID("oldFile999")
                .entityId(entityId)
                .entityType(entityType.name())
                .status(DocumentStatus.ACTIVE)
                .documentOperationType(DocumentOperationType.UPLOAD)
                .build();

        // Mock DocumentHelper
        when(documentHelper.extractAsyncMappingUUID(kafkaEvent)).thenReturn(newFileIdentifiers);
        when(documentHelper.processDocuments(eq(kafkaEvent), any())).thenReturn(Arrays.asList(inactiveDoc1, newDoc));
        when(documentHelper.filterActivatedDocuments(anyList())).thenReturn(Arrays.asList(inactiveDoc1));
        when(documentHelper.discardDocuments(anyList())).thenAnswer(invocation -> {
            List<Document> docs = invocation.getArgument(0);
            docs.forEach(doc -> doc.setStatus(DocumentStatus.DISCARDED));
            return docs;
        });

        // Mock processDocuments flow - find existing inactive document
        when(documentRepository.findAll(any(Specification.class)))
                .thenReturn(Arrays.asList(inactiveDoc1)) // First call for processDocuments
                .thenReturn(Arrays.asList(activeDocToDiscard1, activeDocToDiscard2)); // Second call for getDocuments (active entity documents)

        when(documentRepository.saveAll(anyList()))
                .thenReturn(Arrays.asList(inactiveDoc1, newDoc)) // First call
                .thenReturn(Arrays.asList(activeDocToDiscard1, activeDocToDiscard2)); // Second call for discarded documents

        // When
        documentsService.findAndUpdate(kafkaEvent);

        // Then
        // Verify helper methods were called
        verify(documentHelper).extractAsyncMappingUUID(kafkaEvent);
        verify(documentHelper).processDocuments(eq(kafkaEvent), any());
        verify(documentHelper).filterActivatedDocuments(anyList());
        
        // Verify that active documents not in the delivery task's asyncMappingUUIDs are discarded
        assertEquals(DocumentStatus.DISCARDED, activeDocToDiscard1.getStatus());
        assertEquals(DocumentStatus.DISCARDED, activeDocToDiscard2.getStatus());
        
        // Verify repository calls
        verify(documentRepository, times(2)).findAll(any(Specification.class));
        verify(documentRepository, times(2)).saveAll(anyList());
        
        // Verify event is published with both activated and discarded documents
        verify(applicationEventPublisher).publishEvent(any(DocumentUpdatedEventDTO.class));
    }

    @Test
    void findAndUpdate_DeliveryTaskDocumentDTO_shouldDiscardAllActiveDocumentsWhenAsyncMappingUUIDsIsEmpty() {
        // Given - Testing the scenario where asyncMappingUUIDs is empty, all active documents should be discarded
        String entityId = "entity123";
        EntityType entityType = EntityType.SHIPMENT;
        DocumentOperationType operationType = DocumentOperationType.UPLOAD;

        DeliveryTaskDocumentDTO kafkaEvent = DeliveryTaskDocumentDTO.builder()
                .asyncMappingUUIDs(Collections.emptyList()) // Empty list
                .entityId(entityId)
                .entityType(entityType.name())
                .operationType(operationType)
                .documentType(DocumentType.POD)
                .build();

        // Create active documents that should all be discarded
        Document activeDoc1 = Document.builder()
                .asyncMappingUUID("file123")
                .entityId(entityId)
                .entityType(entityType.name())
                .status(DocumentStatus.ACTIVE)
                .documentOperationType(DocumentOperationType.UPLOAD)
                .build();

        Document activeDoc2 = Document.builder()
                .asyncMappingUUID("file456")
                .entityId(entityId)
                .entityType(entityType.name())
                .status(DocumentStatus.ACTIVE)
                .documentOperationType(DocumentOperationType.UPLOAD)
                .build();

        Document activeDoc3 = Document.builder()
                .asyncMappingUUID("file789")
                .entityId(entityId)
                .entityType(entityType.name())
                .status(DocumentStatus.ACTIVE)
                .documentOperationType(DocumentOperationType.UPLOAD)
                .build();

        // Mock DocumentHelper
        when(documentHelper.extractAsyncMappingUUID(kafkaEvent)).thenReturn(Collections.emptyList());
        when(documentHelper.processDocuments(eq(kafkaEvent), any())).thenReturn(Collections.emptyList());
        when(documentHelper.discardDocuments(anyList())).thenAnswer(invocation -> {
            List<Document> docs = invocation.getArgument(0);
            docs.forEach(doc -> doc.setStatus(DocumentStatus.DISCARDED));
            return docs;
        });

        // Mock processDocuments flow - no documents to process since asyncMappingUUIDs is empty
        when(documentRepository.findAll(any(Specification.class)))
                .thenReturn(Collections.emptyList()) // First call for processDocuments (empty asyncMappingUUIDs)
                .thenReturn(Arrays.asList(activeDoc1, activeDoc2, activeDoc3)); // Second call for getDocuments (active entity documents)

        when(documentRepository.saveAll(anyList()))
                .thenReturn(Collections.emptyList()) // First call (no documents to activate)
                .thenReturn(Arrays.asList(activeDoc1, activeDoc2, activeDoc3)); // Second call for discarded documents

        // When
        documentsService.findAndUpdate(kafkaEvent);

        // Then
        // Verify helper methods were called
        verify(documentHelper).extractAsyncMappingUUID(kafkaEvent);
        verify(documentHelper).processDocuments(eq(kafkaEvent), any());
        
        // Verify that all active documents are discarded since none are in the empty asyncMappingUUIDs list
        assertEquals(DocumentStatus.DISCARDED, activeDoc1.getStatus());
        assertEquals(DocumentStatus.DISCARDED, activeDoc2.getStatus());
        assertEquals(DocumentStatus.DISCARDED, activeDoc3.getStatus());
        
        // Verify repository calls
        verify(documentRepository, times(2)).findAll(any(Specification.class));
        verify(documentRepository, times(1)).saveAll(anyList());
        
        // Verify event is published with discarded documents only
        verify(applicationEventPublisher).publishEvent(any(DocumentUpdatedEventDTO.class));
    }

    @Test
    void processActiveDocuments_shouldUpdateShipmentStatusAndCallRunnerWhenEnabled() {
        // Given - Testing the complete flow from document activation to shipment updates and runner calls when runner API is enabled
        String taskCode = "TASK-001";
        String shipmentCode = "SHIP-001";
        
        // Create multiple active POD documents
        Document activeDoc1 = Document.builder()
                .asyncMappingUUID("file123")
                .entityId(taskCode)
                .entityType(EntityType.TASK.name())
                .status(DocumentStatus.ACTIVE)
                .documentType(DocumentType.POD)
                .documentOperationType(DocumentOperationType.UPLOAD)
                .build();

        Document activeDoc2 = Document.builder()
                .asyncMappingUUID("file456")
                .entityId(taskCode)
                .entityType(EntityType.TASK.name())
                .status(DocumentStatus.ACTIVE)
                .documentType(DocumentType.POD)
                .documentOperationType(DocumentOperationType.UPLOAD)
                .build();

        List<Document> activeDocuments = Arrays.asList(activeDoc1, activeDoc2);

        // Create shipment and task entities
        Shipment shipment = Fakers.createShipment(shipmentCode, ShipmentStatus.ASSIGNED);
        shipment.setIsDocumentAttached(false);
        
        // Mock DocumentHelper
        when(documentHelper.filterActivePodUploadDocuments(activeDocuments)).thenReturn(activeDocuments);
        when(documentHelper.groupDocumentsByEntityType(activeDocuments)).thenReturn(
                List.of(EntityTypeWithIdsDTO.builder()
                        .entityType(EntityType.TASK.name())
                        .entityIds(Set.of(taskCode))
                        .build())
        );
        
        // Mock config service to return enabled=true
        JsonNode enabledConfig = objectMapper.createObjectNode().put("enable", true);
        when(configServiceIntegrator.fetchConfig(ConfigConstants.ENABLE_RUNNER_API_CALL)).thenReturn(enabledConfig);
        
        // Mock shipmentService for getShipmentsForEntities
        when(shipmentService.getShipmentsForEntities(any())).thenReturn(Arrays.asList(shipment));

        // Create DocumentUpdatedEventDTO and trigger the flow
        DocumentUpdatedEventDTO event = DocumentUpdatedEventDTO.builder()
                .activatedDocuments(activeDocuments)
                .build();

        // When
        documentsService.handleDocumentUpdated(event);

        // Then
        // Verify service calls
        verify(shipmentService).getShipmentsForEntities(any());
        verify(shipmentService).updateDocumentAttachedStatus(Arrays.asList(shipment), true);
        
        // Verify config service is called to check runner API flag
        verify(configServiceIntegrator).fetchConfig(ConfigConstants.ENABLE_RUNNER_API_CALL);
        
        // Verify runner service is called with the documents when enabled
        verify(runnerService).sendDocumentsToRunner(activeDocuments, Arrays.asList(shipment));
    }

    @Test
    void processActiveDocuments_shouldUpdateShipmentStatusButNotCallRunnerWhenDisabled() {
        // Given - Testing the flow when runner API is disabled
        String taskCode = "TASK-001";
        String shipmentCode = "SHIP-001";
        
        // Create multiple active POD documents
        Document activeDoc1 = Document.builder()
                .asyncMappingUUID("file123")
                .entityId(taskCode)
                .entityType(EntityType.TASK.name())
                .status(DocumentStatus.ACTIVE)
                .documentType(DocumentType.POD)
                .documentOperationType(DocumentOperationType.UPLOAD)
                .build();

        Document activeDoc2 = Document.builder()
                .asyncMappingUUID("file456")
                .entityId(taskCode)
                .entityType(EntityType.TASK.name())
                .status(DocumentStatus.ACTIVE)
                .documentType(DocumentType.POD)
                .documentOperationType(DocumentOperationType.UPLOAD)
                .build();

        List<Document> activeDocuments = Arrays.asList(activeDoc1, activeDoc2);

        // Create shipment and task entities
        Shipment shipment = Fakers.createShipment(shipmentCode, ShipmentStatus.ASSIGNED);
        shipment.setIsDocumentAttached(false);
        
        // Mock DocumentHelper
        when(documentHelper.filterActivePodUploadDocuments(activeDocuments)).thenReturn(activeDocuments);
        when(documentHelper.groupDocumentsByEntityType(activeDocuments)).thenReturn(
                List.of(EntityTypeWithIdsDTO.builder()
                        .entityType(EntityType.TASK.name())
                        .entityIds(Set.of(taskCode))
                        .build())
        );
        
        // Mock config service to return enabled=false
        JsonNode disabledConfig = objectMapper.createObjectNode().put("enable", false);
        when(configServiceIntegrator.fetchConfig(ConfigConstants.ENABLE_RUNNER_API_CALL)).thenReturn(disabledConfig);
        
        // Mock shipmentService for getShipmentsForEntities
        when(shipmentService.getShipmentsForEntities(any())).thenReturn(Arrays.asList(shipment));

        // Create DocumentUpdatedEventDTO and trigger the flow
        DocumentUpdatedEventDTO event = DocumentUpdatedEventDTO.builder()
                .activatedDocuments(activeDocuments)
                .build();

        // When
        documentsService.handleDocumentUpdated(event);

        // Then
        // Verify service calls
        verify(shipmentService).getShipmentsForEntities(any());
        verify(shipmentService).updateDocumentAttachedStatus(Arrays.asList(shipment), true);
        
        // Verify config service is called to check runner API flag
        verify(configServiceIntegrator).fetchConfig(ConfigConstants.ENABLE_RUNNER_API_CALL);
        
        // Verify runner service is NOT called when disabled
        verify(runnerService, never()).sendDocumentsToRunner(any(), any());
    }

    @Test
    void processActiveDocuments_shouldSkipNonPODDocuments() {
        // Given - Testing that only POD documents are processed
        Document bolDocument = Document.builder()
                .asyncMappingUUID("file123")
                .entityId("TASK-001")
                .entityType(EntityType.TASK.name())
                .status(DocumentStatus.ACTIVE)
                .documentType(DocumentType.BOL) // Not POD
                .documentOperationType(DocumentOperationType.UPLOAD)
                .build();

        Document podDocument = Document.builder()
                .asyncMappingUUID("file456")
                .entityId("TASK-001")
                .entityType(EntityType.TASK.name())
                .status(DocumentStatus.ACTIVE)
                .documentType(DocumentType.POD)
                .documentOperationType(DocumentOperationType.UPLOAD)
                .build();

        List<Document> documents = Arrays.asList(bolDocument, podDocument);
        DocumentUpdatedEventDTO event = DocumentUpdatedEventDTO.builder()
                .activatedDocuments(documents)
                .build();

        // Mock shipment data for POD document
        Shipment shipment = Fakers.createShipment("SHIP-001", ShipmentStatus.ASSIGNED);
        
        // Mock DocumentHelper - returns only POD document
        when(documentHelper.filterActivePodUploadDocuments(documents)).thenReturn(Arrays.asList(podDocument));
        when(documentHelper.groupDocumentsByEntityType(Arrays.asList(podDocument))).thenReturn(
                List.of(EntityTypeWithIdsDTO.builder()
                        .entityType(EntityType.TASK.name())
                        .entityIds(Set.of("TASK-001"))
                        .build())
        );
        
        // Mock shipmentService
        when(shipmentService.getShipmentsForEntities(any())).thenReturn(Arrays.asList(shipment));
        
        // Mock config service to return enabled=true
        JsonNode enabledConfig = objectMapper.createObjectNode().put("enable", true);
        when(configServiceIntegrator.fetchConfig(ConfigConstants.ENABLE_RUNNER_API_CALL)).thenReturn(enabledConfig);

        // When
        documentsService.handleDocumentUpdated(event);

        // Then
        // Verify only POD document is processed (only one document should be sent to runner)
        verify(runnerService).sendDocumentsToRunner(Arrays.asList(podDocument), Arrays.asList(shipment));
    }

    @Test
    void processActiveDocuments_shouldHandleMultipleEntityTypes() {
        // Given - Testing documents with different entity types (TASK, SHIPMENT, TRIP)
        Document taskDoc = Document.builder()
                .asyncMappingUUID("file123")
                .entityId("TASK-001")
                .entityType(EntityType.TASK.name())
                .status(DocumentStatus.ACTIVE)
                .documentType(DocumentType.POD)
                .documentOperationType(DocumentOperationType.UPLOAD)
                .build();

        Document shipmentDoc = Document.builder()
                .asyncMappingUUID("file456")
                .entityId("SHIP-001")
                .entityType(EntityType.SHIPMENT.name())
                .status(DocumentStatus.ACTIVE)
                .documentType(DocumentType.POD)
                .documentOperationType(DocumentOperationType.UPLOAD)
                .build();

        Document tripDoc = Document.builder()
                .asyncMappingUUID("file789")
                .entityId("TRIP-001")
                .entityType(EntityType.TRIP.name())
                .status(DocumentStatus.ACTIVE)
                .documentType(DocumentType.POD)
                .documentOperationType(DocumentOperationType.UPLOAD)
                .build();

        List<Document> documents = Arrays.asList(taskDoc, shipmentDoc, tripDoc);

        // Mock entities
        Shipment shipment1 = Fakers.createShipment("SHIP-001", ShipmentStatus.ASSIGNED);
        Shipment shipment2 = Fakers.createShipment("SHIP-002", ShipmentStatus.ASSIGNED);
        Shipment shipment3 = Fakers.createShipment("SHIP-003", ShipmentStatus.ASSIGNED);
        
        // Mock DocumentHelper
        when(documentHelper.filterActivePodUploadDocuments(documents)).thenReturn(documents);
        when(documentHelper.groupDocumentsByEntityType(documents)).thenReturn(
                Arrays.asList(
                        EntityTypeWithIdsDTO.builder()
                                .entityType(EntityType.TASK.name())
                                .entityIds(Set.of("TASK-001"))
                                .build(),
                        EntityTypeWithIdsDTO.builder()
                                .entityType(EntityType.SHIPMENT.name())
                                .entityIds(Set.of("SHIP-001"))
                                .build(),
                        EntityTypeWithIdsDTO.builder()
                                .entityType(EntityType.TRIP.name())
                                .entityIds(Set.of("TRIP-001"))
                                .build()
                )
        );
        
        // Mock shipmentService for all entity types
        when(shipmentService.getShipmentsForEntities(any())).thenReturn(Arrays.asList(shipment1, shipment2, shipment3));

        // Mock config service to return enabled=true
        JsonNode enabledConfig = objectMapper.createObjectNode().put("enable", true);
        when(configServiceIntegrator.fetchConfig(ConfigConstants.ENABLE_RUNNER_API_CALL)).thenReturn(enabledConfig);

        DocumentUpdatedEventDTO event = DocumentUpdatedEventDTO.builder()
                .activatedDocuments(documents)
                .build();

        // When
        documentsService.handleDocumentUpdated(event);

        // Then
        // Verify shipmentService is called
        verify(shipmentService).getShipmentsForEntities(any());
        verify(shipmentService).updateDocumentAttachedStatus(anyList(), eq(true));
        
        // Verify runner service is called with all documents and found shipments
        verify(runnerService).sendDocumentsToRunner(eq(documents), anyList());
    }

    @Test
    void getAllErrors_shouldReturnErrorsForInactiveDocuments() {
        // Given
        List<String> fileIdentifiers = Arrays.asList("file1", "file2", "file3");

        Document activeDoc = Document.builder()
                .asyncMappingUUID("file1")
                .status(DocumentStatus.ACTIVE)
                .build();

        Document inactiveDoc = Document.builder()
                .asyncMappingUUID("file2")
                .status(DocumentStatus.INACTIVE)
                .fileName("test.pdf")
                .fileSize(1024)
                .build();

        when(documentRepository.findAll(any(Specification.class)))
                .thenReturn(Arrays.asList(activeDoc, inactiveDoc));

        try (MockedStatic<DocumentUtil> documentUtilMock = mockStatic(DocumentUtil.class)) {
            documentUtilMock.when(() -> DocumentUtil.isDocumentInactive(activeDoc)).thenReturn(false);
            documentUtilMock.when(() -> DocumentUtil.isDocumentInactive(inactiveDoc)).thenReturn(true);

            // When
            DocumentErrorResponse result = documentsService.getAllErrors(fileIdentifiers);

            // Then
            assertNotNull(result);
            assertNotNull(result.getFailedDocumentDetails());
            assertEquals(2, result.getFailedDocumentDetails().size());

            DocumentErrorResponse.FileErrorDetails errorDetail = result.getFailedDocumentDetails().get(1);
            assertEquals("file2", errorDetail.getAsyncMappingUUID());
            assertEquals("test.pdf", errorDetail.getFileName());
            assertEquals(1024, errorDetail.getFileSize());
        }
    }

    @Test
    void getAllErrors_shouldReturnEmptyWhenAllDocumentsActive() {
        // Given
        List<String> asyncMappingUUID = Arrays.asList("file1", "file2");

        Document activeDoc1 = Document.builder()
                .asyncMappingUUID("file1")
                .status(DocumentStatus.ACTIVE)
                .build();

        Document activeDoc2 = Document.builder()
                .asyncMappingUUID("file2")
                .status(DocumentStatus.ACTIVE)
                .build();

        when(documentRepository.findAll(any(Specification.class)))
                .thenReturn(Arrays.asList(activeDoc1, activeDoc2));

        try (MockedStatic<DocumentUtil> documentUtilMock = mockStatic(DocumentUtil.class)) {
            documentUtilMock.when(() -> DocumentUtil.isDocumentInactive(any(Document.class))).thenReturn(false);

            // When
            DocumentErrorResponse result = documentsService.getAllErrors(asyncMappingUUID);

            // Then
            assertNotNull(result);
            assertNull(result.getFailedDocumentDetails());
        }
    }

    @Test
    void getAllErrors_shouldReturnEmptyWhenNoDocumentsFound() {
        // Given
        List<String> fileIdentifiers = Arrays.asList("file1", "file2");

        when(documentRepository.findAll(any(Specification.class)))
                .thenReturn(Collections.emptyList());

        // When
        DocumentErrorResponse result = documentsService.getAllErrors(fileIdentifiers);

        // Then
        assertNotNull(result);
        assertNull(result.getFailedDocumentDetails());
    }

    @Test
    void getAllErrors_shouldHandleEmptyFileIdentifiersList() {
        // Given
        List<String> emptyList = Collections.emptyList();

        when(documentRepository.findAll(any(Specification.class)))
                .thenReturn(Collections.emptyList());

        // When
        DocumentErrorResponse result = documentsService.getAllErrors(emptyList);

        // Then
        assertNotNull(result);
        assertNull(result.getFailedDocumentDetails());
    }

    @Test
    void getDocumentsByEntity_shouldReturnDocumentsForMultipleEntities() {
        // Given
        List<EntityDocumentRequest> entityRequests = Arrays.asList(
                EntityDocumentRequest.builder()
                        .entityCode("TASK-1234")
                        .entityType("TASK")
                        .build(),
                EntityDocumentRequest.builder()
                        .entityCode("TASK-5678")
                        .entityType("TASK")
                        .build()
        );

        Document doc1 = Document.builder()
                .entityId("TASK-1234")
                .entityType("TASK")
                .asyncMappingUUID("uuid1")
                .fileName("doc1.pdf")
                .fileSize(1024)
                .fileType("application/pdf")
                .fileIdentifier("file-id-1")
                .status(DocumentStatus.ACTIVE)
                .documentOperationType(DocumentOperationType.UPLOAD)
                .build();

        Document doc2 = Document.builder()
                .entityId("TASK-5678")
                .entityType("TASK")
                .asyncMappingUUID("uuid2")
                .fileName("doc2.pdf")
                .fileSize(2048)
                .fileType("application/pdf")
                .fileIdentifier("file-id-2")
                .status(DocumentStatus.ACTIVE)
                .documentOperationType(DocumentOperationType.UPLOAD)
                .build();

        EntityDocumentResponse.FileDetail fileDetail1 = EntityDocumentResponse.FileDetail.builder()
                .entityType("TASK")
                .entityCode("TASK-1234")
                .asyncMappingUUID("uuid1")
                .fileName("doc1.pdf")
                .fileSize(1024)
                .fileType("application/pdf")
                .externalDocumentIdentifier("file-id-1")
                .build();

        EntityDocumentResponse.FileDetail fileDetail2 = EntityDocumentResponse.FileDetail.builder()
                .entityType("TASK")
                .entityCode("TASK-5678")
                .asyncMappingUUID("uuid2")
                .fileName("doc2.pdf")
                .fileSize(2048)
                .fileType("application/pdf")
                .externalDocumentIdentifier("file-id-2")
                .build();

        // getDocuments should return both documents
        when(documentRepository.findAll(any(Specification.class)))
                .thenReturn(Collections.singletonList(doc1))
                .thenReturn(Collections.singletonList(doc2));
        when(documentMapper.toFileDetail(doc1)).thenReturn(fileDetail1);
        when(documentMapper.toFileDetail(doc2)).thenReturn(fileDetail2);

        // When
        EntityDocumentResponse response = documentsService.getDocumentsByEntity(entityRequests);

        // Then
        assertNotNull(response);
        assertNotNull(response.getData());
        assertNotNull(response.getData().getFileDetails());
        assertEquals(2, response.getData().getFileDetails().size());
        assertTrue(response.getData().getFileDetails().contains(fileDetail1));
        assertTrue(response.getData().getFileDetails().contains(fileDetail2));
        
        // Verify that DocumentSpecifications.byDocumentFilter was used
        verify(documentRepository, times(2)).findAll(any(Specification.class));
    }

    @Test
    void getDocumentsByEntity_shouldReturnEmptyListWhenNoDocumentsFound() {
        // Given
        List<EntityDocumentRequest> entityRequests = Collections.singletonList(
                EntityDocumentRequest.builder()
                        .entityCode("TASK-9999")
                        .entityType("TASK")
                        .build()
        );

        when(documentRepository.findAll(any(Specification.class)))
                .thenReturn(Collections.emptyList());

        // When
        EntityDocumentResponse response = documentsService.getDocumentsByEntity(entityRequests);

        // Then
        assertNotNull(response);
        assertNotNull(response.getData());
        assertNotNull(response.getData().getFileDetails());
        assertTrue(response.getData().getFileDetails().isEmpty());
        
        // Verify getDocuments was called internally
        verify(documentRepository).findAll(any(Specification.class));
    }

    @Test
    void getFileDownloadUrls_shouldReturnPresignedUrlsForValidIdentifiers() {
        // Given
        String fileId1 = "550e8400-e29b-41d4-a716-************";
        String fileId2 = "550e8400-e29b-41d4-a716-************";
        List<String> externalDocumentIdentifiers = Arrays.asList(fileId1, fileId2);

        List<DownloadPreSignedURLResponse> downloadResponses = Arrays.asList(
                DownloadPreSignedURLResponse.builder()
                        .fileIdentifier(fileId1)
                        .preSignedUrl("https://example.com/download1")
                        .build(),
                DownloadPreSignedURLResponse.builder()
                        .fileIdentifier(fileId2)
                        .preSignedUrl("https://example.com/download2")
                        .build()
        );

        DocumentServiceResponse<List<DownloadPreSignedURLResponse>> serviceResponse = 
                DocumentServiceResponse.<List<DownloadPreSignedURLResponse>>builder()
                        .data(downloadResponses)
                        .error(false)
                        .build();

        when(documentService.getDownloadPreSignedURLWithToken(any(GetDownloadPreSignedURLRequest.class)))
                .thenReturn(serviceResponse);

        // When
        FileDownloadPreSignedUrlResponse response = documentsService.getFileDownloadUrls(externalDocumentIdentifiers);

        // Then
        assertNotNull(response);
        assertNotNull(response.getData());
        assertEquals(2, response.getData().size());
        assertEquals("https://example.com/download1", response.getData().get(fileId1));
        assertEquals("https://example.com/download2", response.getData().get(fileId2));
    }

    @Test
    void getFileDownloadUrls_shouldThrowExceptionForInvalidUUIDFormat() {
        // Given
        List<String> externalDocumentIdentifiers = Arrays.asList("invalid-uuid", "550e8400-e29b-41d4-a716-************");

        // When & Then
        TMSException exception = assertThrows(TMSException.class, () -> 
                documentsService.getFileDownloadUrls(externalDocumentIdentifiers));
        
        assertTrue(exception.getErrorMessage().contains("Invalid file identifier format: invalid-uuid"));
        verifyNoInteractions(documentService);
    }

    @Test
    void getFileDownloadUrls_shouldThrowExceptionWhenDocumentServiceReturnsError() {
        // Given
        String fileId = "550e8400-e29b-41d4-a716-************";
        List<String> externalDocumentIdentifiers = Collections.singletonList(fileId);

        DocumentServiceResponse<List<DownloadPreSignedURLResponse>> errorResponse = 
                DocumentServiceResponse.<List<DownloadPreSignedURLResponse>>builder()
                        .error(true)
                        .errorDescription("Document service error")
                        .build();

        when(documentService.getDownloadPreSignedURLWithToken(any(GetDownloadPreSignedURLRequest.class)))
                .thenReturn(errorResponse);

        // When & Then
        TMSException exception = assertThrows(TMSException.class, () -> 
                documentsService.getFileDownloadUrls(externalDocumentIdentifiers));
        
        assertEquals("Document service error", exception.getErrorMessage());
    }

    @Test
    void getFileDownloadUrls_shouldHandleEmptyList() {
        // Given
        List<String> emptyList = Collections.emptyList();

        DocumentServiceResponse<List<DownloadPreSignedURLResponse>> serviceResponse = 
                DocumentServiceResponse.<List<DownloadPreSignedURLResponse>>builder()
                        .data(Collections.emptyList())
                        .error(false)
                        .build();

        when(documentService.getDownloadPreSignedURLWithToken(any(GetDownloadPreSignedURLRequest.class)))
                .thenReturn(serviceResponse);

        // When
        FileDownloadPreSignedUrlResponse response = documentsService.getFileDownloadUrls(emptyList);

        // Then
        assertNotNull(response);
        assertNotNull(response.getData());
        assertTrue(response.getData().isEmpty());
    }

    @Test
    void getDocuments_shouldReturnDocumentsUsingSpecifications() {
        // Given
        GetDocumentsDto filter1 = GetDocumentsDto.builder()
                .entityIds(List.of("TASK-001", "TASK-002"))
                .entityType("TASK")
                .documentType(DocumentType.POD)
                .operationType(DocumentOperationType.UPLOAD)
                .status(DocumentStatus.ACTIVE)
                .build();
                
        GetDocumentsDto filter2 = GetDocumentsDto.builder()
                .entityIds(List.of("SHIP-001"))
                .entityType("SHIPMENT")
                .documentType(DocumentType.POD)
                .operationType(DocumentOperationType.UPLOAD)
                .status(DocumentStatus.ACTIVE)
                .build();
                
        List<GetDocumentsDto> filters = Arrays.asList(filter1, filter2);
        
        Document doc1 = Document.builder()
                .entityId("TASK-001")
                .entityType("TASK")
                .documentType(DocumentType.POD)
                .status(DocumentStatus.ACTIVE)
                .build();
                
        Document doc2 = Document.builder()
                .entityId("SHIP-001")
                .entityType("SHIPMENT")
                .documentType(DocumentType.POD)
                .status(DocumentStatus.ACTIVE)
                .build();
        
        when(documentRepository.findAll(any(Specification.class)))
                .thenReturn(List.of(doc1))
                .thenReturn(List.of(doc2));
        
        // When
        List<Document> result = documentsService.getDocuments(filters);
        
        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains(doc1));
        assertTrue(result.contains(doc2));
        
        // Verify DocumentSpecifications.byDocumentFilter was used
        verify(documentRepository, times(2)).findAll(any(Specification.class));
    }

    @Test
    void getDocuments_shouldHandleEmptyFilters() {
        // Given
        List<GetDocumentsDto> emptyFilters = Collections.emptyList();
        
        // When
        List<Document> result = documentsService.getDocuments(emptyFilters);
        
        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        verify(documentRepository, never()).findAll(any(Specification.class));
    }

    @Test
    void processDiscardedDocuments_shouldUpdateShipmentPodStatusWhenNoActiveDocuments() {
        // Given
        String entityId = "TASK-001";
        String shipmentCode = "SHIP-001";
        
        Document discardedDoc = Document.builder()
                .entityId(entityId)
                .entityType(EntityType.TASK.name())
                .documentType(DocumentType.POD)
                .documentOperationType(DocumentOperationType.UPLOAD)
                .status(DocumentStatus.DISCARDED)
                .build();
                
        List<Document> discardedDocuments = List.of(discardedDoc);
        
        Shipment shipment = Fakers.createShipment(shipmentCode, ShipmentStatus.ASSIGNED);
        shipment.setIsDocumentAttached(true); // Initially has documents attached
        
        // Mock DocumentHelper
        when(documentHelper.filterDiscardedPodUploadDocuments(discardedDocuments))
                .thenReturn(discardedDocuments);
        when(documentHelper.buildActivePodDocumentFilters(discardedDocuments))
                .thenReturn(List.of(GetDocumentsDto.builder()
                        .entityIds(List.of(entityId))
                        .entityType(EntityType.TASK.name())
                        .documentType(DocumentType.POD)
                        .operationType(DocumentOperationType.UPLOAD)
                        .status(DocumentStatus.ACTIVE)
                        .build()));
        when(documentHelper.getDocumentsWithNoActiveEntityDocuments(discardedDocuments, Collections.emptyList(), DocumentType.POD))
                .thenReturn(discardedDocuments);
        when(documentHelper.groupDocumentsByEntityType(discardedDocuments))
                .thenReturn(List.of(EntityTypeWithIdsDTO.builder()
                        .entityType(EntityType.TASK.name())
                        .entityIds(Set.of(entityId))
                        .build()));
        
        // Mock no active documents found
        when(documentRepository.findAll(any(Specification.class)))
                .thenReturn(Collections.emptyList());
        
        // Mock shipment service
        when(shipmentService.getShipmentsForEntities(any()))
                .thenReturn(List.of(shipment));
        
        // Create event and trigger processing
        DocumentUpdatedEventDTO event = DocumentUpdatedEventDTO.builder()
                .discardedDocuments(discardedDocuments)
                .build();
        
        // When
        documentsService.handleDocumentUpdated(event);
        
        // Then
        verify(shipmentService).updateDocumentAttachedStatus(List.of(shipment), false);
    }

    @Test
    void processDiscardedDocuments_shouldNotUpdateShipmentPodStatusWhenActiveDocumentsExist() {
        // Given
        String entityId = "TASK-001";
        String shipmentCode = "SHIP-001";
        
        Document discardedDoc = Document.builder()
                .entityId(entityId)
                .entityType(EntityType.TASK.name())
                .documentType(DocumentType.POD)
                .documentOperationType(DocumentOperationType.UPLOAD)
                .status(DocumentStatus.DISCARDED)
                .build();
                
        Document activeDoc = Document.builder()
                .entityId(entityId)
                .entityType(EntityType.TASK.name())
                .documentType(DocumentType.POD)
                .documentOperationType(DocumentOperationType.UPLOAD)
                .status(DocumentStatus.ACTIVE)
                .build();
                
        List<Document> discardedDocuments = List.of(discardedDoc);
        List<Document> activeDocuments = List.of(activeDoc);
        
        Shipment shipment = Fakers.createShipment(shipmentCode, ShipmentStatus.ASSIGNED);
        shipment.setIsDocumentAttached(true);
        
        // Mock DocumentHelper
        when(documentHelper.filterDiscardedPodUploadDocuments(discardedDocuments))
                .thenReturn(discardedDocuments);
        when(documentHelper.buildActivePodDocumentFilters(discardedDocuments))
                .thenReturn(List.of(GetDocumentsDto.builder()
                        .entityIds(List.of(entityId))
                        .entityType(EntityType.TASK.name())
                        .documentType(DocumentType.POD)
                        .operationType(DocumentOperationType.UPLOAD)
                        .status(DocumentStatus.ACTIVE)
                        .build()));
        when(documentHelper.getDocumentsWithNoActiveEntityDocuments(discardedDocuments, activeDocuments, DocumentType.POD))
                .thenReturn(Collections.emptyList()); // No documents to process as entity has active documents
        
        // Mock active documents found
        when(documentRepository.findAll(any(Specification.class)))
                .thenReturn(activeDocuments);
        
        // Create event and trigger processing
        DocumentUpdatedEventDTO event = DocumentUpdatedEventDTO.builder()
                .discardedDocuments(discardedDocuments)
                .build();
        
        // When
        documentsService.handleDocumentUpdated(event);
        
        // Then
        assertTrue(shipment.getIsDocumentAttached()); // Should remain true
        verify(shipmentService, never()).getShipmentsForEntities(any());
        verify(shipmentService, never()).updateDocumentAttachedStatus(any(), anyBoolean());
    }

    @Test
    void updateShipmentDocumentAttached_shouldUpdateShipmentForActiveUploadPodDocument() {
        // Given
        String shipmentCode = "SHIP-001";
        Document uploadPodDocument = createUploadPodDocument(shipmentCode, EntityType.SHIPMENT.name());
        uploadPodDocument.setStatus(DocumentStatus.INACTIVE); // Start as INACTIVE
        Shipment shipment = Fakers.createShipment(shipmentCode, ShipmentStatus.ASSIGNED);
        shipment.setIsDocumentAttached(false);

        // Mock DocumentHelper
        when(documentHelper.extractAsyncMappingUUID(any(PreSignedUrlEvent.class))).thenReturn(List.of("file-123"));
        when(documentHelper.processDocuments(any(), any())).thenReturn(List.of(uploadPodDocument));
        when(documentHelper.filterActivatedDocuments(anyList())).thenReturn(List.of(uploadPodDocument));
        when(documentHelper.filterActivePodUploadDocuments(anyList())).thenReturn(List.of(uploadPodDocument));
        when(documentHelper.groupDocumentsByEntityType(anyList())).thenReturn(
                List.of(EntityTypeWithIdsDTO.builder()
                        .entityType(EntityType.SHIPMENT.name())
                        .entityIds(Set.of(shipmentCode))
                        .build())
        );

        when(shipmentService.getShipmentsForEntities(any())).thenReturn(Collections.singletonList(shipment));
        when(documentRepository.findAll(any(Specification.class))).thenReturn(Collections.singletonList(uploadPodDocument));
        when(documentRepository.saveAll(anyList())).thenReturn(Collections.singletonList(uploadPodDocument));
        
        // Mock config service to return enabled=true
        JsonNode enabledConfig = objectMapper.createObjectNode().put("enable", true);
        when(configServiceIntegrator.fetchConfig(ConfigConstants.ENABLE_RUNNER_API_CALL)).thenReturn(enabledConfig);

        // When
        documentsService.findAndUpdate(createPreSignedUrlEvent());
        
        // Then we need to manually trigger the event listener since we're in a test
        uploadPodDocument.setStatus(DocumentStatus.ACTIVE); // Set to ACTIVE for event processing
        documentsService.handleDocumentUpdated(DocumentUpdatedEventDTO.builder()
                .activatedDocuments(Collections.singletonList(uploadPodDocument))
                .build());

        // Then
        verify(shipmentService).updateDocumentAttachedStatus(Collections.singletonList(shipment), true);
        verify(applicationEventPublisher).publishEvent(any(DocumentUpdatedEventDTO.class));
    }

    @Test
    void updateShipmentDocumentAttached_shouldUpdateMultipleShipmentsForTrip() {
        // Given
        String tripCode = "TRIP-001";
        Document uploadPodDocument = createUploadPodDocument(tripCode, EntityType.TRIP.name());
        uploadPodDocument.setStatus(DocumentStatus.INACTIVE); // Start as INACTIVE
        List<Shipment> shipments = Arrays.asList(
                Fakers.createShipment("SHIP-001", ShipmentStatus.ASSIGNED),
                Fakers.createShipment("SHIP-002", ShipmentStatus.ASSIGNED)
        );
        shipments.forEach(s -> s.setIsDocumentAttached(false));

        // Mock DocumentHelper
        when(documentHelper.extractAsyncMappingUUID(any(PreSignedUrlEvent.class))).thenReturn(List.of("file-123"));
        when(documentHelper.processDocuments(any(), any())).thenReturn(List.of(uploadPodDocument));
        when(documentHelper.filterActivatedDocuments(anyList())).thenReturn(List.of(uploadPodDocument));
        when(documentHelper.filterActivePodUploadDocuments(anyList())).thenReturn(List.of(uploadPodDocument));
        when(documentHelper.groupDocumentsByEntityType(anyList())).thenReturn(
                List.of(EntityTypeWithIdsDTO.builder()
                        .entityType(EntityType.TRIP.name())
                        .entityIds(Set.of(tripCode))
                        .build())
        );

        when(shipmentService.getShipmentsForEntities(any())).thenReturn(shipments);
        when(documentRepository.findAll(any(Specification.class))).thenReturn(Collections.singletonList(uploadPodDocument));
        when(documentRepository.saveAll(anyList())).thenReturn(Collections.singletonList(uploadPodDocument));
        
        // Mock config service to return enabled=true
        JsonNode enabledConfig = objectMapper.createObjectNode().put("enable", true);
        when(configServiceIntegrator.fetchConfig(ConfigConstants.ENABLE_RUNNER_API_CALL)).thenReturn(enabledConfig);

        // When
        documentsService.findAndUpdate(createPreSignedUrlEvent());
        
        // Then we need to manually trigger the event listener since we're in a test
        uploadPodDocument.setStatus(DocumentStatus.ACTIVE); // Set to ACTIVE for event processing
        documentsService.handleDocumentUpdated(DocumentUpdatedEventDTO.builder()
                .activatedDocuments(Collections.singletonList(uploadPodDocument))
                .build());

        // Then
        verify(shipmentService).updateDocumentAttachedStatus(shipments, true);
        verify(applicationEventPublisher).publishEvent(any(DocumentUpdatedEventDTO.class));
    }

    @Test
    void updateShipmentDocumentAttached_shouldUpdateShipmentForTaskWithValidShipmentParam() {
        // Given
        String taskCode = "TASK-001";
        String shipmentCode = "SHIP-001";
        Document uploadPodDocument = createUploadPodDocument(taskCode, EntityType.TASK.name());
        uploadPodDocument.setStatus(DocumentStatus.INACTIVE); // Start as INACTIVE
        Shipment shipment = Fakers.createShipment(shipmentCode, ShipmentStatus.ASSIGNED);
        shipment.setIsDocumentAttached(false);

        // Mock DocumentHelper
        when(documentHelper.extractAsyncMappingUUID(any(PreSignedUrlEvent.class))).thenReturn(List.of("file-123"));
        when(documentHelper.processDocuments(any(), any())).thenReturn(List.of(uploadPodDocument));
        when(documentHelper.filterActivatedDocuments(anyList())).thenReturn(List.of(uploadPodDocument));
        when(documentHelper.filterActivePodUploadDocuments(anyList())).thenReturn(Collections.singletonList(uploadPodDocument));
        when(documentHelper.groupDocumentsByEntityType(anyList())).thenReturn(
                List.of(EntityTypeWithIdsDTO.builder()
                        .entityType(EntityType.TASK.name())
                        .entityIds(Set.of(taskCode))
                        .build())
        );
        
        // Mock shipmentService
        when(shipmentService.getShipmentsForEntities(any())).thenReturn(Collections.singletonList(shipment));
        when(documentRepository.findAll(any(Specification.class))).thenReturn(Collections.singletonList(uploadPodDocument));
        when(documentRepository.saveAll(anyList())).thenReturn(Collections.singletonList(uploadPodDocument));
        
        // Mock config service to return enabled=true
        JsonNode enabledConfig = objectMapper.createObjectNode().put("enable", true);
        when(configServiceIntegrator.fetchConfig(ConfigConstants.ENABLE_RUNNER_API_CALL)).thenReturn(enabledConfig);

        // When
        documentsService.findAndUpdate(createPreSignedUrlEvent());

        uploadPodDocument.setStatus(DocumentStatus.ACTIVE);
        documentsService.handleDocumentUpdated(DocumentUpdatedEventDTO.builder()
                .activatedDocuments(Collections.singletonList(uploadPodDocument))
                .build());

        // Then
        verify(shipmentService).updateDocumentAttachedStatus(Collections.singletonList(shipment), true);
        // Verify that shipmentService is called
        verify(shipmentService).getShipmentsForEntities(any());
        verify(applicationEventPublisher).publishEvent(any(DocumentUpdatedEventDTO.class));
    }

    @Test
    void updateShipmentDocumentAttached_shouldHandleNoShipmentsFoundForTask() {
        // Given
        String taskCode = "TASK-002";
        Document uploadPodDocument = createUploadPodDocument(taskCode, EntityType.TASK.name());
        uploadPodDocument.setStatus(DocumentStatus.INACTIVE); // Start as INACTIVE

        // Mock DocumentHelper and shipmentService to return empty list
        when(documentHelper.extractAsyncMappingUUID(any(PreSignedUrlEvent.class))).thenReturn(List.of("file-123"));
        when(documentHelper.processDocuments(any(), any())).thenReturn(List.of(uploadPodDocument));
        when(documentHelper.filterActivatedDocuments(anyList())).thenReturn(List.of(uploadPodDocument));
        when(documentHelper.filterActivePodUploadDocuments(anyList())).thenReturn(Collections.singletonList(uploadPodDocument));
        when(documentHelper.groupDocumentsByEntityType(anyList())).thenReturn(
                List.of(EntityTypeWithIdsDTO.builder()
                        .entityType(EntityType.TASK.name())
                        .entityIds(Set.of(taskCode))
                        .build())
        );
        when(shipmentService.getShipmentsForEntities(any())).thenReturn(new ArrayList<>());
        when(documentRepository.findAll(any(Specification.class))).thenReturn(Collections.singletonList(uploadPodDocument));
        when(documentRepository.saveAll(anyList())).thenReturn(Collections.singletonList(uploadPodDocument));

        // When
        documentsService.findAndUpdate(createPreSignedUrlEvent());
        // Manually trigger the event listener to simulate post-transaction execution
        uploadPodDocument.setStatus(DocumentStatus.ACTIVE);
        documentsService.handleDocumentUpdated(DocumentUpdatedEventDTO.builder()
                .activatedDocuments(Collections.singletonList(uploadPodDocument))
                .build());

        // Then
        verify(shipmentService).getShipmentsForEntities(any());
        verify(shipmentService, never()).updateDocumentAttachedStatus(anyList(), anyBoolean());
        // Verify runner service is not called when no shipments found
        verify(runnerService, never()).sendDocumentsToRunner(anyList(), anyList());
    }

    @Test
    void updateShipmentDocumentAttached_shouldNotUpdateForWrongDocumentType() {
        // Given - Create a document that should NOT trigger shipment updates (wrong document type)
        Document bolDocument = createUploadPodDocument("SHIP-001", EntityType.SHIPMENT.name());
        bolDocument.setStatus(DocumentStatus.INACTIVE); // Start as INACTIVE
        bolDocument.setDocumentType(DocumentType.BOL); // Change to BOL

        // Mock DocumentHelper
        when(documentHelper.extractAsyncMappingUUID(any(PreSignedUrlEvent.class))).thenReturn(List.of("file-123"));
        when(documentHelper.processDocuments(any(), any())).thenReturn(List.of(bolDocument));
        when(documentHelper.filterActivatedDocuments(anyList())).thenReturn(List.of(bolDocument));
        when(documentHelper.filterActivePodUploadDocuments(anyList())).thenReturn(Collections.emptyList()); // No POD documents
        
        when(documentRepository.findAll(any(Specification.class))).thenReturn(Collections.singletonList(bolDocument));
        when(documentRepository.saveAll(anyList())).thenReturn(Collections.singletonList(bolDocument));

        // When
        documentsService.findAndUpdate(createPreSignedUrlEvent());
        bolDocument.setStatus(DocumentStatus.ACTIVE);
        documentsService.handleDocumentUpdated(DocumentUpdatedEventDTO.builder()
                .activatedDocuments(Collections.singletonList(bolDocument))
                .build());

        // Then
        verify(shipmentService, never()).updateDocumentAttachedStatus(anyList(), anyBoolean());
        verify(runnerService, never()).sendDocumentsToRunner(anyList(), anyList());
    }

    private Document createUploadPodDocument(String entityId, String entityType) {
        Document document = Fakers.createDocument("file-123", DocumentStatus.ACTIVE, entityId, entityType);
        document.setDocumentType(DocumentType.POD);
        document.setDocumentOperationType(DocumentOperationType.UPLOAD);
        return document;
    }

    private PreSignedUrlEvent createPreSignedUrlEvent() {
        PreSignedUrlEvent event = new PreSignedUrlEvent();
        event.setFileKey("file-123");
        return event;
    }

    private void mockDocumentProcessing(Document document) {
        // Ensure document has the required fields for activation
        document.setAsyncMappingUUID("file-123");
        document.setFileIdentifier("file-123");
        if (document.getEntityId() == null) {
            document.setEntityId("entity-123");
        }
    }

    @Test
    void processActiveDocuments_shouldUseRunnerApiConfigDto() {
        // Given
        Document uploadPodDocument = createUploadPodDocument("SHIP-001", EntityType.SHIPMENT.name());
        uploadPodDocument.setStatus(DocumentStatus.ACTIVE);
        List<Document> activeDocuments = List.of(uploadPodDocument);
        
        Shipment shipment = Fakers.createShipment("SHIP-001", ShipmentStatus.ASSIGNED);
        shipment.setIsDocumentAttached(false);

        // Mock DocumentHelper
        when(documentHelper.filterActivePodUploadDocuments(activeDocuments)).thenReturn(activeDocuments);
        when(documentHelper.groupDocumentsByEntityType(activeDocuments)).thenReturn(
                List.of(EntityTypeWithIdsDTO.builder()
                        .entityType(EntityType.SHIPMENT.name())
                        .entityIds(Set.of("SHIP-001"))
                        .build())
        );
        
        // Mock config service to return enabled=true using RunnerApiConfigDto structure
        JsonNode enabledConfig = objectMapper.createObjectNode().put("enable", true);
        when(configServiceIntegrator.fetchConfig(ConfigConstants.ENABLE_RUNNER_API_CALL)).thenReturn(enabledConfig);
        
        // Mock shipmentService
        when(shipmentService.getShipmentsForEntities(any())).thenReturn(List.of(shipment));

        // Create DocumentUpdatedEventDTO and trigger the flow
        DocumentUpdatedEventDTO event = DocumentUpdatedEventDTO.builder()
                .activatedDocuments(activeDocuments)
                .build();

        // When
        documentsService.handleDocumentUpdated(event);

        // Then
        // Verify the config service was called
        verify(configServiceIntegrator).fetchConfig(ConfigConstants.ENABLE_RUNNER_API_CALL);
        
        // Verify shipment service was called to update document attached status
        verify(shipmentService).updateDocumentAttachedStatus(List.of(shipment), true);
        
        // Verify runner service was called because config enabled=true
        verify(runnerService).sendDocumentsToRunner(activeDocuments, List.of(shipment));
    }

    @Test
    void processActiveDocuments_shouldNotCallRunnerServiceWhenDisabled() {
        // Given
        Document uploadPodDocument = createUploadPodDocument("SHIP-001", EntityType.SHIPMENT.name());
        uploadPodDocument.setStatus(DocumentStatus.ACTIVE);
        List<Document> activeDocuments = List.of(uploadPodDocument);
        
        Shipment shipment = Fakers.createShipment("SHIP-001", ShipmentStatus.ASSIGNED);
        shipment.setIsDocumentAttached(false);

        // Mock DocumentHelper
        when(documentHelper.filterActivePodUploadDocuments(activeDocuments)).thenReturn(activeDocuments);
        when(documentHelper.groupDocumentsByEntityType(activeDocuments)).thenReturn(
                List.of(EntityTypeWithIdsDTO.builder()
                        .entityType(EntityType.SHIPMENT.name())
                        .entityIds(Set.of("SHIP-001"))
                        .build())
        );
        
        // Mock config service to return enabled=false using RunnerApiConfigDto structure
        JsonNode disabledConfig = objectMapper.createObjectNode().put("enable", false);
        when(configServiceIntegrator.fetchConfig(ConfigConstants.ENABLE_RUNNER_API_CALL)).thenReturn(disabledConfig);
        
        // Mock shipmentService
        when(shipmentService.getShipmentsForEntities(any())).thenReturn(List.of(shipment));

        // Create DocumentUpdatedEventDTO and trigger the flow
        DocumentUpdatedEventDTO event = DocumentUpdatedEventDTO.builder()
                .activatedDocuments(activeDocuments)
                .build();

        // When
        documentsService.handleDocumentUpdated(event);

        // Then
        // Verify the config service was called
        verify(configServiceIntegrator).fetchConfig(ConfigConstants.ENABLE_RUNNER_API_CALL);
        
        // Verify shipment service was called to update document attached status
        verify(shipmentService).updateDocumentAttachedStatus(List.of(shipment), true);
        
        // Verify runner service was NOT called because config enabled=false
        verify(runnerService, never()).sendDocumentsToRunner(any(), any());
    }

    @Test
    void runnerApiConfigDto_shouldDeserializeCorrectly() {
        // Given
        JsonNode enabledConfig = objectMapper.createObjectNode().put("enable", true);
        JsonNode disabledConfig = objectMapper.createObjectNode().put("enable", false);

        // When
        RunnerApiConfigDto enabledDto = objectMapper.convertValue(enabledConfig, RunnerApiConfigDto.class);
        RunnerApiConfigDto disabledDto = objectMapper.convertValue(disabledConfig, RunnerApiConfigDto.class);

        // Then
        assertTrue(enabledDto.isEnable());
        assertFalse(disabledDto.isEnable());
    }

}