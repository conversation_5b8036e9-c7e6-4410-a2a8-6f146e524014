package com.dpw.ctms.move.service.document.generator;

import com.dpw.ctms.move.BaseTest;
import com.dpw.ctms.move.enums.DocumentType;
import com.dpw.ctms.move.enums.Tenant;
import com.dpw.ctms.move.dto.document.BOLDocumentDataDTO;
import com.dpw.ctms.move.dto.document.DocumentGenerationContextDTO;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.fakers.Fakers;
import com.dpw.ctms.move.integration.service.IOmsIntegratorService;
import com.dpw.ctms.move.integration.service.IResourceIntegratorService;
import com.dpw.ctms.move.integration.dto.oms.OmsConsignmentDto;
import com.dpw.ctms.move.integration.dto.resource.ResourceFacilitiesDto;
import com.dpw.ctms.move.integration.dto.resource.ResourceProductDetailsDto;
import com.dpw.ctms.move.integration.dto.resource.ResourceUomDto;
import com.dpw.ctms.move.service.ITripDataService;
import com.dpw.ctms.move.config.ConfigService;
import com.dpw.ctms.move.constants.ConfigConstants;
import com.dpw.ctms.move.constants.ProductPropertyConstants;
import com.dpw.ctms.move.util.JsonUtils;
import com.dpw.tmsutils.exception.GenericException;
import com.dpw.tmsutils.exception.NotFoundException;
import com.dpw.tmsutils.exception.TMSException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import java.util.List;
import java.util.Collections;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ExecutionException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@DisplayName("BOL Document Generator Tests")
class BOLDocumentGeneratorTest extends BaseTest {

    @Autowired
    private BOLDocumentGenerator bolDocumentGenerator;

    @MockBean
    private ITripDataService tripDataService;

    @MockBean
    private IOmsIntegratorService omsIntegratorService;

    @MockBean
    private IResourceIntegratorService resourceIntegratorService;

    @MockBean
    private ConfigService configService;

    @MockBean
    private com.dpw.ctms.move.util.DateTimeUtil dateTimeUtil;

    private Trip testTrip;
    private List<OmsConsignmentDto> omsConsignmentDtos;
    private List<ResourceFacilitiesDto> resourceFacilitiesDtos;
    private List<ResourceProductDetailsDto> resourceProductDetailsDtos;
    private List<ResourceUomDto> resourceUomDtos;

    @BeforeEach
    void setUp() {
        testTrip = Fakers.createBOLTestTrip();
        omsConsignmentDtos = Fakers.createOmsConsignmentDtoList();
        resourceFacilitiesDtos = Fakers.createResourceFacilitiesDtoList();
        resourceProductDetailsDtos = Fakers.createResourceProductDetailsDtoList();
        resourceUomDtos = Fakers.createResourceUomDtoList();
    }

    @Test
    @DisplayName("Should return correct document type")
    void shouldReturnCorrectDocumentType() {
        // When
        DocumentType result = bolDocumentGenerator.getDocumentType();

        // Then
        assertEquals(DocumentType.BOL, result);
    }

    @Test
    @DisplayName("Should generate context successfully with valid trip")
    void shouldGenerateContextSuccessfully() throws ExecutionException, InterruptedException {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        Tenant tenant = Tenant.CFR;

        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsIntegratorService.getOmsConsignmentDtos(anyList()))
                .thenReturn(omsConsignmentDtos);
        when(resourceIntegratorService.getFacilitiesDTOs(anyList()))
                .thenReturn(resourceFacilitiesDtos);
        when(resourceIntegratorService.getProductDetailsDTOs(anyList()))
                .thenReturn(resourceProductDetailsDtos);
        when(resourceIntegratorService.getUomDTOs(anyList()))
                .thenReturn(resourceUomDtos);

        // When
        CompletableFuture<DocumentGenerationContextDTO<BOLDocumentDataDTO>> future =
                bolDocumentGenerator.generateContext(tripCode, tenant);
        DocumentGenerationContextDTO<BOLDocumentDataDTO> result = future.get();

        // Then
        assertNotNull(result);
        assertEquals(tenant, result.getTenant());
        assertEquals(DocumentType.BOL, result.getDocumentType());
        assertNotNull(result.getDocumentData());

        BOLDocumentDataDTO documentData = result.getDocumentData();
        assertNotNull(documentData.getTripDetails());
        assertNotNull(documentData.getConsignmentDetailsMap());
        assertNotNull(documentData.getFacilityDetailsMap());
        assertFalse(documentData.getExternalConsignmentIds().isEmpty());
        assertFalse(documentData.getExternalFacilityCodes().isEmpty());
        
        // Verify timezone fields are populated
        assertNotNull(documentData.getPickUpTimeZoneId());
        assertNotNull(documentData.getDeliveryTimeZoneId());

        verify(tripDataService).getTripByCodeWithAllDetails(tripCode);
        verify(omsIntegratorService).getOmsConsignmentDtos(anyList());
        verify(resourceIntegratorService).getFacilitiesDTOs(anyList());
        verify(resourceIntegratorService).getProductDetailsDTOs(anyList());
        verify(resourceIntegratorService).getUomDTOs(anyList());
    }

    @Test
    @DisplayName("Should throw exception when trip not found")
    void shouldThrowExceptionWhenTripNotFound() {
        // Given
        String tripCode = "INVALID_TRIP";
        when(tripDataService.getTripByCodeWithAllDetails(tripCode))
                .thenThrow(new NotFoundException("Trip not found", "Trip with code " + tripCode + " not found"));

        // When & Then
        CompletableFuture<DocumentGenerationContextDTO<BOLDocumentDataDTO>> future =
                bolDocumentGenerator.generateContext(tripCode, Tenant.CFR);

        CompletionException exception = assertThrows(CompletionException.class, future::join);
        assertTrue(exception.getCause() instanceof NotFoundException);
    }

    @Test
    @DisplayName("Should propagate OMS service exceptions")
    void shouldPropagateOmsServiceExceptions() {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        GenericException omsException = new GenericException("OMS Error", "OMS_001");

        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsIntegratorService.getOmsConsignmentDtos(anyList()))
                .thenThrow(omsException);

        // When & Then
        CompletableFuture<DocumentGenerationContextDTO<BOLDocumentDataDTO>> future =
                bolDocumentGenerator.generateContext(tripCode, Tenant.CFR);

        CompletionException exception = assertThrows(CompletionException.class, future::join);
        assertEquals(omsException, exception.getCause());
    }

    @Test
    @DisplayName("Should propagate Resource service exceptions")
    void shouldPropagateResourceServiceExceptions() {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        GenericException resourceException = new GenericException("Resource Error", "RES_001");

        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsIntegratorService.getOmsConsignmentDtos(anyList()))
                .thenReturn(omsConsignmentDtos);
        when(resourceIntegratorService.getFacilitiesDTOs(anyList()))
                .thenThrow(resourceException);
        when(resourceIntegratorService.getProductDetailsDTOs(anyList()))
                .thenReturn(resourceProductDetailsDtos);
        when(resourceIntegratorService.getUomDTOs(anyList()))
                .thenReturn(resourceUomDtos);

        // When & Then
        CompletableFuture<DocumentGenerationContextDTO<BOLDocumentDataDTO>> future =
                bolDocumentGenerator.generateContext(tripCode, Tenant.CFR);

        CompletionException exception = assertThrows(CompletionException.class, future::join);
        assertEquals(resourceException, exception.getCause());
    }

    @Test
    @DisplayName("Should handle empty consignment response")
    void shouldHandleEmptyConsignmentResponse() throws ExecutionException, InterruptedException {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        List<OmsConsignmentDto> emptyConsignmentList = Collections.emptyList();

        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsIntegratorService.getOmsConsignmentDtos(anyList()))
                .thenReturn(emptyConsignmentList);
        when(resourceIntegratorService.getFacilitiesDTOs(anyList()))
                .thenReturn(resourceFacilitiesDtos);
        when(resourceIntegratorService.getProductDetailsDTOs(anyList()))
                .thenReturn(resourceProductDetailsDtos);
        when(resourceIntegratorService.getUomDTOs(anyList()))
                .thenReturn(resourceUomDtos);

        // When
        CompletableFuture<DocumentGenerationContextDTO<BOLDocumentDataDTO>> future =
                bolDocumentGenerator.generateContext(tripCode, Tenant.CFR);
        DocumentGenerationContextDTO<BOLDocumentDataDTO> result = future.get();

        // Then
        assertNotNull(result);
        assertNotNull(result.getDocumentData());
        assertTrue(result.getDocumentData().getConsignmentDetailsMap().isEmpty());
    }

    @Test
    @DisplayName("Should handle empty facility response")
    void shouldHandleEmptyFacilityResponse() throws ExecutionException, InterruptedException {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        List<ResourceFacilitiesDto> emptyFacilityList = Collections.emptyList();

        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsIntegratorService.getOmsConsignmentDtos(anyList()))
                .thenReturn(omsConsignmentDtos);
        when(resourceIntegratorService.getFacilitiesDTOs(anyList()))
                .thenReturn(emptyFacilityList);
        when(resourceIntegratorService.getProductDetailsDTOs(anyList()))
                .thenReturn(resourceProductDetailsDtos);
        when(resourceIntegratorService.getUomDTOs(anyList()))
                .thenReturn(resourceUomDtos);

        // When
        CompletableFuture<DocumentGenerationContextDTO<BOLDocumentDataDTO>> future =
                bolDocumentGenerator.generateContext(tripCode, Tenant.CFR);
        DocumentGenerationContextDTO<BOLDocumentDataDTO> result = future.get();

        // Then
        assertNotNull(result);
        assertNotNull(result.getDocumentData());
        assertTrue(result.getDocumentData().getFacilityDetailsMap().isEmpty());
    }

    @Test
    @DisplayName("Should generate JSON successfully with valid JOLT configuration")
    void shouldGenerateJsonSuccessfully() {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        Tenant tenant = Tenant.CFR;
        
        // Mock all the dependencies for generateContext
        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsIntegratorService.getOmsConsignmentDtos(anyList()))
                .thenReturn(omsConsignmentDtos);
        when(resourceIntegratorService.getFacilitiesDTOs(anyList()))
                .thenReturn(resourceFacilitiesDtos);
        when(resourceIntegratorService.getProductDetailsDTOs(anyList()))
                .thenReturn(resourceProductDetailsDtos);
        when(resourceIntegratorService.getUomDTOs(anyList()))
                .thenReturn(resourceUomDtos);

        // Mock document configuration - simple transformation that copies input to output
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode joltConfig = objectMapper.createArrayNode()
                .add(objectMapper.createObjectNode()
                        .put("operation", "shift")
                        .set("spec", objectMapper.createObjectNode()
                                .put("*", "&")));
        
        JsonNode documentConfig = objectMapper.createObjectNode()
                .put("templateId", "BOL_CFR_TEMPLATE_V1")
                .set("joltConfig", joltConfig);
        
        when(configService.getConfig(ConfigConstants.BOL_CONFIG, tenant))
                .thenReturn(documentConfig);

        // When
        Object result = bolDocumentGenerator.generateJson(tripCode, tenant);

        // Then
        assertNotNull(result);
        String jsonResult = JsonUtils.toJson(result);
        assertFalse(jsonResult.trim().isEmpty());
        assertTrue(jsonResult.contains("tripDetails") || jsonResult.contains("{}") || jsonResult.length() > 2);
        
        verify(tripDataService).getTripByCodeWithAllDetails(tripCode);
        verify(configService).getConfig(ConfigConstants.BOL_CONFIG, tenant);
    }

    @Test
    @DisplayName("Should throw TMSException when JOLT configuration is null")
    void shouldThrowExceptionWhenJoltConfigIsNull() {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        Tenant tenant = Tenant.CFR;
        
        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsIntegratorService.getOmsConsignmentDtos(anyList()))
                .thenReturn(omsConsignmentDtos);
        when(resourceIntegratorService.getFacilitiesDTOs(anyList()))
                .thenReturn(resourceFacilitiesDtos);
        when(resourceIntegratorService.getProductDetailsDTOs(anyList()))
                .thenReturn(resourceProductDetailsDtos);
        when(resourceIntegratorService.getUomDTOs(anyList()))
                .thenReturn(resourceUomDtos);
        
        when(configService.getConfig(ConfigConstants.BOL_CONFIG, tenant))
                .thenReturn(null);

        // When & Then
        TMSException exception = assertThrows(TMSException.class,
                () -> bolDocumentGenerator.generateJson(tripCode, tenant));
    }

    @Test
    @DisplayName("Should throw TMSException when JOLT configuration is empty")
    void shouldThrowExceptionWhenJoltConfigIsEmpty() {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        Tenant tenant = Tenant.CFR;
        
        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsIntegratorService.getOmsConsignmentDtos(anyList()))
                .thenReturn(omsConsignmentDtos);
        when(resourceIntegratorService.getFacilitiesDTOs(anyList()))
                .thenReturn(resourceFacilitiesDtos);
        when(resourceIntegratorService.getProductDetailsDTOs(anyList()))
                .thenReturn(resourceProductDetailsDtos);
        when(resourceIntegratorService.getUomDTOs(anyList()))
                .thenReturn(resourceUomDtos);
        
        // Empty JOLT configuration array
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode emptyJoltConfig = objectMapper.createArrayNode();
        
        JsonNode documentConfig = objectMapper.createObjectNode()
                .put("templateId", "BOL_CFR_TEMPLATE_V1")
                .set("joltConfig", emptyJoltConfig);
        
        when(configService.getConfig(ConfigConstants.BOL_CONFIG, tenant))
                .thenReturn(documentConfig);

        // When & Then
        TMSException exception = assertThrows(TMSException.class, 
                () -> bolDocumentGenerator.generateJson(tripCode, tenant));
    }

    @Test
    @DisplayName("Should propagate exception from generateContext")
    void shouldPropagateExceptionFromGenerateContext() {
        // Given
        String tripCode = "INVALID_TRIP";
        Tenant tenant = Tenant.CFR;
        
        when(tripDataService.getTripByCodeWithAllDetails(tripCode))
                .thenThrow(new NotFoundException("Trip not found", "Trip with code " + tripCode + " not found"));

        // When & Then
        GenericException exception = assertThrows(GenericException.class, 
                () -> bolDocumentGenerator.generateJson(tripCode, tenant));
        
        assertTrue(exception.getMessage().contains("Trip not found"));
    }

    @Test
    @DisplayName("Should handle JOLT transformation returning null")
    void shouldHandleJoltTransformationReturningNull() {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        Tenant tenant = Tenant.CFR;
        
        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsIntegratorService.getOmsConsignmentDtos(anyList()))
                .thenReturn(omsConsignmentDtos);
        when(resourceIntegratorService.getFacilitiesDTOs(anyList()))
                .thenReturn(resourceFacilitiesDtos);
        when(resourceIntegratorService.getProductDetailsDTOs(anyList()))
                .thenReturn(resourceProductDetailsDtos);
        when(resourceIntegratorService.getUomDTOs(anyList()))
                .thenReturn(resourceUomDtos);

        // JOLT configuration that might return null (invalid transformation)
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode joltConfig = objectMapper.createArrayNode()
                .add(objectMapper.createObjectNode()
                        .put("operation", "remove")
                        .set("spec", objectMapper.createObjectNode()
                                .put("*", "")));
        
        JsonNode documentConfig = objectMapper.createObjectNode()
                .put("templateId", "BOL_CFR_TEMPLATE_V1")
                .set("joltConfig", joltConfig);
        
        when(configService.getConfig(ConfigConstants.BOL_CONFIG, tenant))
                .thenReturn(documentConfig);

        // When
        Object result = bolDocumentGenerator.generateJson(tripCode, tenant);

        // Then - Should return empty object as JSON when transformation returns null
        assertNotNull(result);
        String jsonResult = JsonUtils.toJson(result);
        assertTrue(jsonResult.equals("{}") || jsonResult.equals("{ }") || jsonResult.trim().equals("{}"));
    }

    @Test
    @DisplayName("Should generate different JSON for different vendors")
    void shouldGenerateDifferentJsonForDifferentVendors() {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        Tenant tenant1 = Tenant.CFR;
        Tenant tenant2 = Tenant.IHS;
        
        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsIntegratorService.getOmsConsignmentDtos(anyList()))
                .thenReturn(omsConsignmentDtos);
        when(resourceIntegratorService.getFacilitiesDTOs(anyList()))
                .thenReturn(resourceFacilitiesDtos);
        when(resourceIntegratorService.getProductDetailsDTOs(anyList()))
                .thenReturn(resourceProductDetailsDtos);
        when(resourceIntegratorService.getUomDTOs(anyList()))
                .thenReturn(resourceUomDtos);

        ObjectMapper objectMapper = new ObjectMapper();
        
        // Different JOLT configs for different vendors
        JsonNode joltConfig1 = objectMapper.createArrayNode()
                .add(objectMapper.createObjectNode()
                        .put("operation", "shift")
                        .set("spec", objectMapper.createObjectNode()
                                .put("tripDetails", "trip")));
        
        JsonNode joltConfig2 = objectMapper.createArrayNode()
                .add(objectMapper.createObjectNode()
                        .put("operation", "shift")
                        .set("spec", objectMapper.createObjectNode()
                                .put("tripDetails", "tripInfo")));
        
        JsonNode documentConfig1 = objectMapper.createObjectNode()
                .put("templateId", "BOL_CFR_TEMPLATE_V1")
                .set("joltConfig", joltConfig1);
        
        JsonNode documentConfig2 = objectMapper.createObjectNode()
                .put("templateId", "BOL_IHS_TEMPLATE_V1")
                .set("joltConfig", joltConfig2);
        
        when(configService.getConfig(ConfigConstants.BOL_CONFIG, tenant1))
                .thenReturn(documentConfig1);
        when(configService.getConfig(ConfigConstants.BOL_CONFIG, tenant2))
                .thenReturn(documentConfig2);

        // When
        Object result1 = bolDocumentGenerator.generateJson(tripCode, tenant1);
        Object result2 = bolDocumentGenerator.generateJson(tripCode, tenant2);

        // Then
        assertNotNull(result1);
        assertNotNull(result2);
        // Results should be different due to different JOLT transformations
        // (Though both might be valid JSON, the structure will differ)
        String jsonResult1 = JsonUtils.toJson(result1);
        String jsonResult2 = JsonUtils.toJson(result2);
        assertFalse(jsonResult1.trim().isEmpty());
        assertFalse(jsonResult2.trim().isEmpty());
        
        verify(configService).getConfig(ConfigConstants.BOL_CONFIG, tenant1);
        verify(configService).getConfig(ConfigConstants.BOL_CONFIG, tenant2);
    }

    @Test
    @DisplayName("Should retrieve correct templateId for different vendors")
    void shouldRetrieveCorrectTemplateIdForDifferentVendors() {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        Tenant cfrTenant = Tenant.CFR;
        Tenant ihsTenant = Tenant.IHS;
        
        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsIntegratorService.getOmsConsignmentDtos(anyList()))
                .thenReturn(omsConsignmentDtos);
        when(resourceIntegratorService.getFacilitiesDTOs(anyList()))
                .thenReturn(resourceFacilitiesDtos);
        when(resourceIntegratorService.getProductDetailsDTOs(anyList()))
                .thenReturn(resourceProductDetailsDtos);
        when(resourceIntegratorService.getUomDTOs(anyList()))
                .thenReturn(resourceUomDtos);

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode joltConfig = objectMapper.createArrayNode()
                .add(objectMapper.createObjectNode()
                        .put("operation", "shift")
                        .set("spec", objectMapper.createObjectNode()
                                .put("*", "&")));
        
        JsonNode cfrConfig = objectMapper.createObjectNode()
                .put("templateId", "BOL_CFR_TEMPLATE_V1")
                .set("joltConfig", joltConfig);
        
        JsonNode ihsConfig = objectMapper.createObjectNode()
                .put("templateId", "BOL_IHS_TEMPLATE_V1")
                .set("joltConfig", joltConfig);
        
        when(configService.getConfig(ConfigConstants.BOL_CONFIG, cfrTenant))
                .thenReturn(cfrConfig);
        when(configService.getConfig(ConfigConstants.BOL_CONFIG, ihsTenant))
                .thenReturn(ihsConfig);

        // When
        bolDocumentGenerator.generateJson(tripCode, cfrTenant);
        bolDocumentGenerator.generateJson(tripCode, ihsTenant);

        // Then - Verify different template IDs are retrieved for different vendors
        verify(configService).getConfig(ConfigConstants.BOL_CONFIG, cfrTenant);
        verify(configService).getConfig(ConfigConstants.BOL_CONFIG, ihsTenant);
    }

    @Test
    @DisplayName("Should handle InterruptedException correctly")
    void shouldHandleInterruptedException() {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        Tenant tenant = Tenant.CFR;
        
        // Mock to throw InterruptedException during execution
        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsIntegratorService.getOmsConsignmentDtos(anyList()))
                .thenAnswer(invocation -> {
                    Thread.currentThread().interrupt();
                    throw new InterruptedException("Thread was interrupted");
                });

        // When & Then
        GenericException exception = assertThrows(GenericException.class, 
                () -> bolDocumentGenerator.generateJson(tripCode, tenant));
    }

    @Test
    @DisplayName("Should handle ExecutionException from product details service")
    void shouldHandleExecutionExceptionFromProductService() throws ExecutionException, InterruptedException {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        Tenant tenant = Tenant.CFR;
        GenericException productServiceException = new GenericException("Product service failed", "PROD_001");

        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsIntegratorService.getOmsConsignmentDtos(anyList()))
                .thenReturn(omsConsignmentDtos);
        when(resourceIntegratorService.getFacilitiesDTOs(anyList()))
                .thenReturn(resourceFacilitiesDtos);
        when(resourceIntegratorService.getProductDetailsDTOs(anyList()))
                .thenThrow(productServiceException);
        when(resourceIntegratorService.getUomDTOs(anyList()))
                .thenReturn(resourceUomDtos);

        // When & Then
        CompletableFuture<DocumentGenerationContextDTO<BOLDocumentDataDTO>> future =
                bolDocumentGenerator.generateContext(tripCode, tenant);

        CompletionException exception = assertThrows(CompletionException.class, future::join);
        assertEquals(productServiceException, exception.getCause());
    }

    @Test
    @DisplayName("Should handle ExecutionException from UOM service")
    void shouldHandleExecutionExceptionFromUomService() throws ExecutionException, InterruptedException {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        Tenant tenant = Tenant.CFR;
        GenericException uomServiceException = new GenericException("UOM service failed", "UOM_001");

        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsIntegratorService.getOmsConsignmentDtos(anyList()))
                .thenReturn(omsConsignmentDtos);
        when(resourceIntegratorService.getFacilitiesDTOs(anyList()))
                .thenReturn(resourceFacilitiesDtos);
        when(resourceIntegratorService.getProductDetailsDTOs(anyList()))
                .thenReturn(resourceProductDetailsDtos);
        when(resourceIntegratorService.getUomDTOs(anyList()))
                .thenThrow(uomServiceException);

        // When & Then
        CompletableFuture<DocumentGenerationContextDTO<BOLDocumentDataDTO>> future =
                bolDocumentGenerator.generateContext(tripCode, tenant);

        CompletionException exception = assertThrows(CompletionException.class, future::join);
        assertEquals(uomServiceException, exception.getCause());
    }

    @Test
    @DisplayName("Should handle InterruptedException in parallel processing")
    void shouldHandleInterruptedExceptionInParallelProcessing() throws ExecutionException, InterruptedException {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        Tenant tenant = Tenant.CFR;

        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsIntegratorService.getOmsConsignmentDtos(anyList()))
                .thenReturn(omsConsignmentDtos);
        when(resourceIntegratorService.getFacilitiesDTOs(anyList()))
                .thenReturn(resourceFacilitiesDtos);
        when(resourceIntegratorService.getProductDetailsDTOs(anyList()))
                .thenAnswer(invocation -> {
                    Thread.currentThread().interrupt();
                    throw new InterruptedException("Product service interrupted");
                });
        when(resourceIntegratorService.getUomDTOs(anyList()))
                .thenReturn(resourceUomDtos);

        // When & Then
        CompletableFuture<DocumentGenerationContextDTO<BOLDocumentDataDTO>> future =
                bolDocumentGenerator.generateContext(tripCode, tenant);

        CompletionException exception = assertThrows(CompletionException.class, future::join);
        
        // Debug: print actual exception details
        System.out.println("Actual exception cause: " + exception.getCause().getClass().getName());
        System.out.println("Actual exception message: " + exception.getCause().getMessage());
        
        assertTrue(exception.getCause() instanceof GenericException, 
            "Expected GenericException but got: " + exception.getCause().getClass().getSimpleName());
        assertTrue(exception.getCause().getMessage().contains("Failed to generate document for trip"));
    }

    @Test
    @DisplayName("Should handle unexpected exceptions in parallel processing")
    void shouldHandleUnexpectedExceptionsInParallelProcessing() throws ExecutionException, InterruptedException {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        Tenant tenant = Tenant.CFR;

        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsIntegratorService.getOmsConsignmentDtos(anyList()))
                .thenReturn(omsConsignmentDtos);
        when(resourceIntegratorService.getFacilitiesDTOs(anyList()))
                .thenReturn(resourceFacilitiesDtos);
        when(resourceIntegratorService.getProductDetailsDTOs(anyList()))
                .thenThrow(new RuntimeException("Unexpected product service error"));
        when(resourceIntegratorService.getUomDTOs(anyList()))
                .thenReturn(resourceUomDtos);

        // When & Then
        CompletableFuture<DocumentGenerationContextDTO<BOLDocumentDataDTO>> future =
                bolDocumentGenerator.generateContext(tripCode, tenant);

        CompletionException exception = assertThrows(CompletionException.class, future::join);
        
        // Debug: print actual exception details
        System.out.println("Actual exception cause: " + exception.getCause().getClass().getName());
        System.out.println("Actual exception message: " + exception.getCause().getMessage());
        
        assertTrue(exception.getCause() instanceof TMSException,
            "Expected TMSException but got: " + exception.getCause().getClass().getSimpleName());
        TMSException tmsException = (TMSException) exception.getCause();
        // Check if the exception was created with the expected error code
        assertTrue(tmsException.getMessage().contains("ERR_REQUEST_NOT_PROCESSED") || 
                   tmsException.toString().contains("Failed to fetch product and UOM data"));
    }

    @Test
    @DisplayName("Should calculate volume from dimensions when volume is not present")
    void shouldCalculateVolumeFromDimensions() throws ExecutionException, InterruptedException {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        Tenant tenant = Tenant.CFR;
        
        // Create consignment DTOs with dimensions but no volume
        List<OmsConsignmentDto> consignmentWithDimensionsNoVolume = 
                Fakers.createOmsConsignmentDtoListWithDimensionsNoVolume();

        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsIntegratorService.getOmsConsignmentDtos(anyList()))
                .thenReturn(consignmentWithDimensionsNoVolume);
        when(resourceIntegratorService.getFacilitiesDTOs(anyList()))
                .thenReturn(resourceFacilitiesDtos);
        when(resourceIntegratorService.getProductDetailsDTOs(anyList()))
                .thenReturn(resourceProductDetailsDtos);
        when(resourceIntegratorService.getUomDTOs(anyList()))
                .thenReturn(resourceUomDtos);

        // When
        CompletableFuture<DocumentGenerationContextDTO<BOLDocumentDataDTO>> future =
                bolDocumentGenerator.generateContext(tripCode, tenant);
        DocumentGenerationContextDTO<BOLDocumentDataDTO> result = future.get();

        // Then
        assertNotNull(result);
        assertNotNull(result.getDocumentData());
        assertNotNull(result.getDocumentData().getConsignmentDetailsMap());
        
        // Verify volume was calculated
        var consignmentDetails = result.getDocumentData().getConsignmentDetailsMap().values().iterator().next();
        assertNotNull(consignmentDetails.getProductDetailsDTO());
        assertNotNull(consignmentDetails.getProductDetailsDTO().getProperties());
        
        var properties = consignmentDetails.getProductDetailsDTO().getProperties();
        
        // Check that VOLUME property exists using constant
        assertTrue(properties.containsKey(ProductPropertyConstants.VOLUME));
        
        // Verify the calculated volume (2.0 * 1.5 * 1.0 = 3.0)
        var volumeProperty = properties.get(ProductPropertyConstants.VOLUME);
        assertNotNull(volumeProperty);
        assertEquals(3.0, volumeProperty.getValue());
        
        // Verify volume has same UOM as LENGTH
        var lengthProperty = properties.get(ProductPropertyConstants.LENGTH);
        assertNotNull(lengthProperty);
        assertEquals(lengthProperty.getResourceUomId(), volumeProperty.getResourceUomId());
        
        // Verify other dimensions are still present using constants
        assertTrue(properties.containsKey(ProductPropertyConstants.LENGTH));
        assertTrue(properties.containsKey(ProductPropertyConstants.BREADTH));
        assertTrue(properties.containsKey(ProductPropertyConstants.HEIGHT));
        
        verify(tripDataService).getTripByCodeWithAllDetails(tripCode);
        verify(omsIntegratorService).getOmsConsignmentDtos(anyList());
    }

    @Test
    @DisplayName("Should handle parallel processing of product and UOM data")
    void shouldHandleParallelProcessingOfProductAndUomData() throws ExecutionException, InterruptedException {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        Tenant tenant = Tenant.CFR;

        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsIntegratorService.getOmsConsignmentDtos(anyList()))
                .thenReturn(omsConsignmentDtos);
        when(resourceIntegratorService.getFacilitiesDTOs(anyList()))
                .thenReturn(resourceFacilitiesDtos);
        
        // Simulate parallel calls to product and UOM services
        when(resourceIntegratorService.getProductDetailsDTOs(anyList()))
                .thenReturn(resourceProductDetailsDtos);
        when(resourceIntegratorService.getUomDTOs(anyList()))
                .thenReturn(resourceUomDtos);

        // When
        CompletableFuture<DocumentGenerationContextDTO<BOLDocumentDataDTO>> future =
                bolDocumentGenerator.generateContext(tripCode, tenant);
        DocumentGenerationContextDTO<BOLDocumentDataDTO> result = future.get();

        // Then
        assertNotNull(result);
        assertNotNull(result.getDocumentData());
        
        // Verify that both product and UOM services were called
        verify(resourceIntegratorService).getProductDetailsDTOs(anyList());
        verify(resourceIntegratorService).getUomDTOs(anyList());
        
        // Verify that the data was properly integrated
        assertNotNull(result.getDocumentData().getConsignmentDetailsMap());
        assertFalse(result.getDocumentData().getConsignmentDetailsMap().isEmpty());
    }

    @Test
    @DisplayName("Should extract and populate timezone information")
    void shouldExtractAndPopulateTimezoneInformation() throws ExecutionException, InterruptedException {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        Tenant tenant = Tenant.CFR;

        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsIntegratorService.getOmsConsignmentDtos(anyList()))
                .thenReturn(omsConsignmentDtos);
        when(resourceIntegratorService.getFacilitiesDTOs(anyList()))
                .thenReturn(resourceFacilitiesDtos);
        when(resourceIntegratorService.getProductDetailsDTOs(anyList()))
                .thenReturn(resourceProductDetailsDtos);
        when(resourceIntegratorService.getUomDTOs(anyList()))
                .thenReturn(resourceUomDtos);

        // When
        CompletableFuture<DocumentGenerationContextDTO<BOLDocumentDataDTO>> future =
                bolDocumentGenerator.generateContext(tripCode, tenant);
        DocumentGenerationContextDTO<BOLDocumentDataDTO> result = future.get();

        // Then
        assertNotNull(result);
        BOLDocumentDataDTO documentData = result.getDocumentData();
        
        // Verify timezone fields are populated
        assertNotNull(documentData.getPickUpTimeZoneId());
        assertNotNull(documentData.getDeliveryTimeZoneId());
        
        // Verify trip details contain timezone-converted dates  
        assertNotNull(documentData.getTripDetails());
        
        verify(tripDataService).getTripByCodeWithAllDetails(tripCode);
    }

    @Test
    @DisplayName("Should process first product correctly based on current implementation")
    void shouldProcessFirstProductCorrectly() throws ExecutionException, InterruptedException {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        Tenant tenant = Tenant.CFR;

        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsIntegratorService.getOmsConsignmentDtos(anyList()))
                .thenReturn(omsConsignmentDtos);
        when(resourceIntegratorService.getFacilitiesDTOs(anyList()))
                .thenReturn(resourceFacilitiesDtos);
        when(resourceIntegratorService.getProductDetailsDTOs(anyList()))
                .thenReturn(resourceProductDetailsDtos);
        when(resourceIntegratorService.getUomDTOs(anyList()))
                .thenReturn(resourceUomDtos);

        // When
        CompletableFuture<DocumentGenerationContextDTO<BOLDocumentDataDTO>> future =
                bolDocumentGenerator.generateContext(tripCode, tenant);
        DocumentGenerationContextDTO<BOLDocumentDataDTO> result = future.get();

        // Then
        assertNotNull(result);
        BOLDocumentDataDTO documentData = result.getDocumentData();
        
        // Verify consignment details are mapped
        assertFalse(documentData.getConsignmentDetailsMap().isEmpty());
        
        var consignmentDetails = documentData.getConsignmentDetailsMap().values().iterator().next();
        assertNotNull(consignmentDetails);
        assertNotNull(consignmentDetails.getProductDetailsDTO());
        
        // Current implementation processes only the first product
        // Verify the first product's properties are correctly mapped
        var productDetails = consignmentDetails.getProductDetailsDTO();
        assertNotNull(productDetails.getProperties());
        
        // Should contain Weight and Volume properties from first product
        assertTrue(productDetails.getProperties().containsKey("WEIGHT"));
        assertTrue(productDetails.getProperties().containsKey("VOLUME"));
        
        // Verify resource ID matches first product
        assertEquals(101L, productDetails.getResourceId());
        
        verify(tripDataService).getTripByCodeWithAllDetails(tripCode);
        verify(omsIntegratorService).getOmsConsignmentDtos(anyList());
        verify(resourceIntegratorService).getProductDetailsDTOs(anyList());
        verify(resourceIntegratorService).getUomDTOs(anyList());
    }
}