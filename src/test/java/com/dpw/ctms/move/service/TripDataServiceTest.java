package com.dpw.ctms.move.service;

import com.dpw.ctms.move.entity.Stop;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.repository.StopRepository;
import com.dpw.ctms.move.repository.StopTaskRepository;
import com.dpw.ctms.move.repository.TripRepository;
import com.dpw.ctms.move.request.common.Pagination;
import com.dpw.ctms.move.service.impl.TripDataServiceImpl;
import com.dpw.tmsutils.exception.TMSException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.dpw.ctms.move.enums.StopStatus.DISCARDED;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TripDataServiceTest {

    @Mock
    private StopTaskRepository stopTaskRepository;

    @Mock
    private StopRepository stopRepository;

    @Mock
    private TripRepository tripRepository;

    @InjectMocks
    private TripDataServiceImpl tripDataService;

    private Trip mockTrip;
    private Stop mockStop1;
    private Stop mockStop2;
    private Pagination mockPagination;

    @BeforeEach
    void setUp() {
        mockTrip = new Trip();
        mockTrip.setCode("TRIP001");
        mockTrip.setId(1L);

        mockStop1 = new Stop();
        mockStop1.setId(1L);
        mockStop1.setTrip(mockTrip);

        mockStop2 = new Stop();
        mockStop2.setId(2L);
        mockStop2.setTrip(mockTrip);

        mockPagination = new Pagination();
        mockPagination.setPageNo(0);
        mockPagination.setPageSize(10);
    }

    @Test
    void getTripByCode_WhenTripExists_ShouldReturnTrip() {
        // Given
        String tripCode = "TRIP001";
        when(tripRepository.findByCodeAndDeletedAtIsNull(tripCode)).thenReturn(Optional.of(mockTrip));

        // When
        Trip result = tripDataService.getTripByCode(tripCode);

        // Then
        assertNotNull(result);
        assertEquals(tripCode, result.getCode());
        verify(tripRepository, times(1)).findByCodeAndDeletedAtIsNull(tripCode);
    }

    @Test
    void getTripByCode_WhenTripDoesNotExist_ShouldThrowTMSException() {
        // Given
        String tripCode = "NONEXISTENT";
        when(tripRepository.findByCodeAndDeletedAtIsNull(tripCode)).thenReturn(Optional.empty());

        // When & Then
        TMSException exception = assertThrows(TMSException.class,
                () -> tripDataService.getTripByCode(tripCode));

        assertEquals("INVALID_REQUEST", exception.getErrorCode());
        assertEquals("Trip not found with code: " + tripCode, exception.getErrorMessage());
        verify(tripRepository, times(1)).findByCodeAndDeletedAtIsNull(tripCode);
    }

    @Test
    void getTripByCode_WithNullTripCode_ShouldCallRepositoryWithNull() {
        // Given
        String tripCode = null;
        when(tripRepository.findByCodeAndDeletedAtIsNull(null)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(TMSException.class, () -> tripDataService.getTripByCode(tripCode));
        verify(tripRepository, times(1)).findByCodeAndDeletedAtIsNull(null);
    }

    @Test
    void getTripStopByTripCode_WhenStopsExist_ShouldReturnStopList() {
        // Given
        String tripCode = "TRIP001";
        List<Stop> expectedStops = Arrays.asList(mockStop1, mockStop2);
        PageRequest expectedPageRequest = PageRequest.of(0, 10);
        Page<Stop> expectedStopsPage = new PageImpl<>(expectedStops);
        when(stopRepository.findAllByTripCodeAndStatusNotInOrNullAndTripDeletedAtIsNull(
                eq(tripCode),
                eq(List.of(DISCARDED.name())),
                eq(expectedPageRequest)
        )).thenReturn(expectedStopsPage);

        // When
        List<Stop> result = tripDataService.getTripStopByTripCode(tripCode, mockPagination).getContent();

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(expectedStops, result);
        verify(stopRepository, times(1)).findAllByTripCodeAndStatusNotInOrNullAndTripDeletedAtIsNull(
                tripCode, List.of(DISCARDED.name()), expectedPageRequest);
    }

    @Test
    void getTripStopByTripCode_WithDifferentPagination_ShouldUseCorrectPageRequest() {
        // Given
        String tripCode = "TRIP001";
        Pagination customPagination = new Pagination();
        customPagination.setPageNo(2);
        customPagination.setPageSize(5);
        PageRequest expectedPageRequest = PageRequest.of(2, 5);
        Page<Stop> expectedStopsPage = new PageImpl<>(Arrays.asList(mockStop1));
        when(stopRepository.findAllByTripCodeAndStatusNotInOrNullAndTripDeletedAtIsNull(
                eq(tripCode),
                eq(List.of(DISCARDED.name())),
                eq(expectedPageRequest)
        )).thenReturn(expectedStopsPage);

        // When
        Page<Stop> result = tripDataService.getTripStopByTripCode(tripCode, customPagination);

        // Then
        assertNotNull(result);
        verify(stopRepository, times(1)).findAllByTripCodeAndStatusNotInOrNullAndTripDeletedAtIsNull(
                tripCode, List.of(DISCARDED.name()), expectedPageRequest);
    }

    @Test
    void getTripStopByTripCode_WhenNoStopsExist_ShouldReturnEmptyList() {
        // Given
        String tripCode = "TRIP001";
        Page<Stop> emptyPage = new PageImpl<>(Collections.emptyList());
        when(stopRepository.findAllByTripCodeAndStatusNotInOrNullAndTripDeletedAtIsNull(
                anyString(), anyList(), any(PageRequest.class)
        )).thenReturn(emptyPage);

        // When
        Page<Stop> result = tripDataService.getTripStopByTripCode(tripCode, mockPagination);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(stopRepository, times(1)).findAllByTripCodeAndStatusNotInOrNullAndTripDeletedAtIsNull(
                anyString(), anyList(), any(PageRequest.class));
    }

    @Test
    void getTripStopByTripCode_WithNullTripCode_ShouldPassNullToRepository() {
        // Given
        String tripCode = null;
        Page<Stop> emptyPage = new PageImpl<>(Collections.emptyList());
        when(stopRepository.findAllByTripCodeAndStatusNotInOrNullAndTripDeletedAtIsNull(
                isNull(), anyList(), any(PageRequest.class)
        )).thenReturn(emptyPage);

        // When
        Page<Stop> result = tripDataService.getTripStopByTripCode(tripCode, mockPagination);

        // Then
        assertNotNull(result);
        verify(stopRepository, times(1)).findAllByTripCodeAndStatusNotInOrNullAndTripDeletedAtIsNull(
                isNull(), eq(List.of(DISCARDED.name())), any(PageRequest.class));
    }

    @Test
    void getTotalStopCount_WhenStopsExist_ShouldReturnCount() {
        // Given
        String tripCode = "TRIP001";
        Long expectedCount = 5L;
        when(stopRepository.countByTripCodeAndStatusNotInAndTripDeletedAtIsNull(
                tripCode, List.of(DISCARDED.name())
        )).thenReturn(expectedCount);

        // When
        Long result = tripDataService.getTotalStopCount(tripCode);

        // Then
        assertEquals(expectedCount, result);
        verify(stopRepository, times(1)).countByTripCodeAndStatusNotInAndTripDeletedAtIsNull(
                tripCode, List.of(DISCARDED.name()));
    }

    @Test
    void getTotalStopCount_WhenNoStopsExist_ShouldReturnZero() {
        // Given
        String tripCode = "TRIP001";
        when(stopRepository.countByTripCodeAndStatusNotInAndTripDeletedAtIsNull(
                tripCode, List.of(DISCARDED.name())
        )).thenReturn(0L);

        // When
        Long result = tripDataService.getTotalStopCount(tripCode);

        // Then
        assertEquals(0, result);
        verify(stopRepository, times(1)).countByTripCodeAndStatusNotInAndTripDeletedAtIsNull(
                tripCode, List.of(DISCARDED.name()));
    }

    @Test
    void getTotalStopCount_WithNullTripCode_ShouldPassNullToRepository() {
        // Given
        String tripCode = null;
        when(stopRepository.countByTripCodeAndStatusNotInAndTripDeletedAtIsNull(
                null, List.of(DISCARDED.name())
        )).thenReturn(0L);

        // When
        Long result = tripDataService.getTotalStopCount(tripCode);

        // Then
        assertEquals(0, result);
        verify(stopRepository, times(1)).countByTripCodeAndStatusNotInAndTripDeletedAtIsNull(
                null, List.of(DISCARDED.name()));
    }


    @Test
    void getTotalStopCount_WhenRepositoryThrowsException_ShouldPropagateException() {
        // Given
        String tripCode = "TRIP001";
        RuntimeException expectedException = new RuntimeException("Database connection failed");
        when(stopRepository.countByTripCodeAndStatusNotInAndTripDeletedAtIsNull(
                anyString(), anyList()
        )).thenThrow(expectedException);

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class,
                () -> tripDataService.getTotalStopCount(tripCode));

        assertEquals("Database connection failed", exception.getMessage());
        verify(stopRepository, times(1)).countByTripCodeAndStatusNotInAndTripDeletedAtIsNull(
                tripCode, List.of(DISCARDED.name()));
    }

    @Test
    void allMethods_ShouldUseDiscardedStatusCorrectly() {
        // This test verifies that all methods consistently use DISCARDED.name()
        String tripCode = "TRIP001";
        List<String> expectedDiscardedList = List.of(DISCARDED.name());

        Page<Stop> expectedStopsPage = new PageImpl<>(Arrays.asList(mockStop1));
        // Setup mocks
        when(tripRepository.findByCodeAndDeletedAtIsNull(anyString())).thenReturn(Optional.of(mockTrip));
        when(stopRepository.findAllByTripCodeAndStatusNotInOrNullAndTripDeletedAtIsNull(
                anyString(), anyList(), any(PageRequest.class)
        )).thenReturn(expectedStopsPage);
        when(stopRepository.countByTripCodeAndStatusNotInAndTripDeletedAtIsNull(
                anyString(), anyList()
        )).thenReturn(5L);

        // Execute all methods
        tripDataService.getTripByCode(tripCode);
        tripDataService.getTripStopByTripCode(tripCode, mockPagination);
        tripDataService.getTotalStopCount(tripCode);

        // Verify DISCARDED status is used consistently
        verify(stopRepository).findAllByTripCodeAndStatusNotInOrNullAndTripDeletedAtIsNull(
                eq(tripCode), eq(expectedDiscardedList), any(PageRequest.class));
        verify(stopRepository).countByTripCodeAndStatusNotInAndTripDeletedAtIsNull(
                tripCode, expectedDiscardedList);
    }
}