package com.dpw.ctms.move.service.impl;

import com.dpw.ctms.move.dto.EntityTypeWithIdsDTO;
import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.ShipmentTask;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.enums.EntityType;
import com.dpw.ctms.move.mapper.ShipmentViewMapper;
import com.dpw.ctms.move.repository.ShipmentRepository;
import com.dpw.ctms.move.repository.ShipmentTaskRepository;
import com.dpw.ctms.move.repository.TaskRepository;
import com.dpw.ctms.move.service.ShipmentFilteringService;
import com.dpw.tmsutils.exception.TMSException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.data.jpa.domain.Specification;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class ShipmentServiceImplTest {

    @Mock
    private ShipmentRepository shipmentRepository;

    @Mock
    private ShipmentFilteringService shipmentFilteringService;

    @Mock
    private ShipmentViewMapper shipmentViewMapper;

    @Mock
    private TaskRepository taskRepository;

    @Mock
    private ShipmentTaskRepository shipmentTaskRepository;

    @InjectMocks
    private ShipmentServiceImpl shipmentService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void findShipmentById_found() {
        Shipment shipment = new Shipment();
        shipment.setId(1L);
        Mockito.when(shipmentRepository.findById(1L)).thenReturn(Optional.of(shipment));
        Shipment result = shipmentService.findShipmentById(1L);
        assertThat(result).isEqualTo(shipment);
    }

    @Test
    void findShipmentById_notFound() {
        Mockito.when(shipmentRepository.findById(2L)).thenReturn(Optional.empty());
        assertThatThrownBy(() -> shipmentService.findShipmentById(2L))
                .isInstanceOf(TMSException.class)
                .hasMessageContaining("NOT_FOUND");
    }

    @Test
    void findShipmentByCode_found() {
        Shipment shipment = new Shipment();
        shipment.setCode("CODE123");
        Mockito.when(shipmentRepository.findByCodeAndDeletedAtIsNull("CODE123")).thenReturn(Optional.of(shipment));
        Shipment result = shipmentService.findShipmentByCode("CODE123");
        assertThat(result).isEqualTo(shipment);
    }

    @Test
    void findShipmentByCode_notFound() {
        Mockito.when(shipmentRepository.findByCodeAndDeletedAtIsNull("NOT_FOUND")).thenReturn(Optional.empty());
        assertThatThrownBy(() -> shipmentService.findShipmentByCode("NOT_FOUND"))
                .isInstanceOf(TMSException.class)
                .hasMessageContaining("NOT_FOUND");
    }

    @Test
    void getShipmentsForEntities_withShipmentEntityType_shouldReturnCorrectShipments() {
        // Given
        Set<String> shipmentCodes = Set.of("SHIP-001", "SHIP-002");
        List<EntityTypeWithIdsDTO> entityTypeDTOs = List.of(
                EntityTypeWithIdsDTO.builder()
                        .entityType(EntityType.SHIPMENT.name())
                        .entityIds(shipmentCodes)
                        .build()
        );

        Shipment shipment1 = new Shipment();
        shipment1.setCode("SHIP-001");
        Shipment shipment2 = new Shipment();
        shipment2.setCode("SHIP-002");
        List<Shipment> expectedShipments = List.of(shipment1, shipment2);

        when(shipmentRepository.findAllByCodeInAndDeletedAtIsNull(shipmentCodes))
                .thenReturn(expectedShipments);

        // When
        List<Shipment> result = shipmentService.getShipmentsForEntities(entityTypeDTOs);

        // Then
        assertThat(result).hasSize(2);
        assertThat(result).containsExactlyInAnyOrder(shipment1, shipment2);
    }

    @Test
    void getShipmentsForEntities_withTripEntityType_shouldReturnCorrectShipments() {
        // Given
        Set<String> tripCodes = Set.of("TRIP-001");
        List<EntityTypeWithIdsDTO> entityTypeDTOs = List.of(
                EntityTypeWithIdsDTO.builder()
                        .entityType(EntityType.TRIP.name())
                        .entityIds(tripCodes)
                        .build()
        );

        Shipment shipment1 = new Shipment();
        shipment1.setCode("SHIP-001");
        List<Shipment> expectedShipments = List.of(shipment1);

        when(shipmentRepository.findAll(any(Specification.class)))
                .thenReturn(expectedShipments);

        // When
        List<Shipment> result = shipmentService.getShipmentsForEntities(entityTypeDTOs);

        // Then
        assertThat(result).hasSize(1);
        assertThat(result).contains(shipment1);
    }

    @Test
    void getShipmentsForEntities_withTaskEntityType_shouldReturnCorrectShipments() {
        // Given
        Set<String> taskCodes = Set.of("TASK-001");
        List<EntityTypeWithIdsDTO> entityTypeDTOs = List.of(
                EntityTypeWithIdsDTO.builder()
                        .entityType(EntityType.TASK.name())
                        .entityIds(taskCodes)
                        .build()
        );

        Task task = new Task();
        task.setId(1L);
        task.setCode("TASK-001");
        List<Task> tasks = List.of(task);

        Shipment shipment = new Shipment();
        shipment.setCode("SHIP-001");
        
        ShipmentTask shipmentTask = new ShipmentTask();
        shipmentTask.setShipment(shipment);
        List<ShipmentTask> shipmentTasks = List.of(shipmentTask);

        when(taskRepository.findAllByCodeInAndDeletedAtIsNull(taskCodes))
                .thenReturn(tasks);
        when(shipmentTaskRepository.findAllByTaskIdsAndDeletedAtIsNull(List.of(1L)))
                .thenReturn(shipmentTasks);

        // When
        List<Shipment> result = shipmentService.getShipmentsForEntities(entityTypeDTOs);

        // Then
        assertThat(result).hasSize(1);
        assertThat(result).contains(shipment);
    }

    @Test
    void getShipmentsForEntities_withMixedEntityTypes_shouldReturnAllShipments() {
        // Given
        List<EntityTypeWithIdsDTO> entityTypeDTOs = List.of(
                EntityTypeWithIdsDTO.builder()
                        .entityType(EntityType.SHIPMENT.name())
                        .entityIds(Set.of("SHIP-001"))
                        .build(),
                EntityTypeWithIdsDTO.builder()
                        .entityType(EntityType.TASK.name())
                        .entityIds(Set.of("TASK-001"))
                        .build()
        );

        // Mock shipment entity
        Shipment directShipment = new Shipment();
        directShipment.setCode("SHIP-001");
        when(shipmentRepository.findAllByCodeInAndDeletedAtIsNull(Set.of("SHIP-001")))
                .thenReturn(List.of(directShipment));

        // Mock task entity
        Task task = new Task();
        task.setId(1L);
        task.setCode("TASK-001");
        when(taskRepository.findAllByCodeInAndDeletedAtIsNull(Set.of("TASK-001")))
                .thenReturn(List.of(task));

        Shipment taskShipment = new Shipment();
        taskShipment.setCode("SHIP-002");
        ShipmentTask shipmentTask = new ShipmentTask();
        shipmentTask.setShipment(taskShipment);
        when(shipmentTaskRepository.findAllByTaskIdsAndDeletedAtIsNull(List.of(1L)))
                .thenReturn(List.of(shipmentTask));

        // When
        List<Shipment> result = shipmentService.getShipmentsForEntities(entityTypeDTOs);

        // Then
        assertThat(result).hasSize(2);
        assertThat(result).containsExactlyInAnyOrder(directShipment, taskShipment);
    }

    @Test
    void getShipmentsForEntities_withEdgeCases_shouldHandleCorrectly() {
        // Test empty list case
        assertThat(shipmentService.getShipmentsForEntities(Collections.emptyList())).isEmpty();

        // Test non-existent task case
        Set<String> nonExistentTaskCodes = Set.of("TASK-999");
        List<EntityTypeWithIdsDTO> nonExistentTaskDTOs = List.of(
                EntityTypeWithIdsDTO.builder()
                        .entityType(EntityType.TASK.name())
                        .entityIds(nonExistentTaskCodes)
                        .build()
        );
        when(taskRepository.findAllByCodeInAndDeletedAtIsNull(nonExistentTaskCodes))
                .thenReturn(Collections.emptyList());
        assertThat(shipmentService.getShipmentsForEntities(nonExistentTaskDTOs)).isEmpty();
    }

    @Test
    void updateDocumentAttachedStatus_shouldUpdateShipmentsCorrectly() {
        // Given
        Shipment shipment1 = new Shipment();
        shipment1.setId(1L);
        shipment1.setCode("SHIP-001");
        shipment1.setIsDocumentAttached(false);

        Shipment shipment2 = new Shipment();
        shipment2.setId(2L);
        shipment2.setCode("SHIP-002");
        shipment2.setIsDocumentAttached(false);

        List<Shipment> inputShipments = List.of(shipment1, shipment2);

        when(shipmentRepository.saveAll(inputShipments)).thenReturn(inputShipments);

        // When
        shipmentService.updateDocumentAttachedStatus(inputShipments, true);

        // Then
        assertThat(shipment1.getIsDocumentAttached()).isTrue();
        assertThat(shipment2.getIsDocumentAttached()).isTrue();
        verify(shipmentRepository).saveAll(inputShipments);
    }

    @Test
    void updateDocumentAttachedStatus_withNullIds_shouldStillUpdate() {
        // Given
        Shipment shipmentWithoutId = new Shipment();
        shipmentWithoutId.setId(null);
        shipmentWithoutId.setCode("SHIP-NO-ID");
        shipmentWithoutId.setIsDocumentAttached(false);

        List<Shipment> inputShipments = List.of(shipmentWithoutId);

        when(shipmentRepository.saveAll(inputShipments)).thenReturn(inputShipments);

        // When
        shipmentService.updateDocumentAttachedStatus(inputShipments, true);

        // Then
        assertThat(shipmentWithoutId.getIsDocumentAttached()).isTrue();
        verify(shipmentRepository).saveAll(inputShipments);
    }

    @Test
    void updateDocumentAttachedStatus_withEmptyList_shouldHandleGracefully() {
        // Given
        List<Shipment> emptyList = Collections.emptyList();

        when(shipmentRepository.saveAll(emptyList)).thenReturn(emptyList);

        // When
        shipmentService.updateDocumentAttachedStatus(emptyList, true);

        // Then
        verify(shipmentRepository).saveAll(emptyList);
    }

    @Test
    void updateDocumentAttachedStatus_shouldSetToFalse() {
        // Given
        Shipment shipment = new Shipment();
        shipment.setId(1L);
        shipment.setCode("SHIP-001");
        shipment.setIsDocumentAttached(true);

        List<Shipment> inputShipments = List.of(shipment);

        when(shipmentRepository.saveAll(inputShipments)).thenReturn(inputShipments);

        // When
        shipmentService.updateDocumentAttachedStatus(inputShipments, false);

        // Then
        assertThat(shipment.getIsDocumentAttached()).isFalse();
        verify(shipmentRepository).saveAll(inputShipments);
    }
}
