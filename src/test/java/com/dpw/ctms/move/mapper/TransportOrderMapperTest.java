package com.dpw.ctms.move.mapper;

import com.dpw.ctms.move.controller.IntegrationTestBase;
import com.dpw.ctms.move.dto.ShipmentDTO;
import com.dpw.ctms.move.dto.TransportOrderDTO;
import com.dpw.ctms.move.dto.TripDTO;
import com.dpw.ctms.move.dto.VehicleResourceDTO;
import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.enums.AssignmentType;
import com.dpw.ctms.move.enums.TransportOrderStatus;
import com.dpw.ctms.move.response.TransportOrderDetailsResponse;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertSame;


@SpringBootTest
class TransportOrderMapperTest extends IntegrationTestBase {

    @Autowired
    private TransportOrderMapper mapper;

    @Test
    void testToEntity_setsBackReferences_correctly() {
        // Given
        TransportOrderDTO dto = new TransportOrderDTO();

        TripDTO tripDTO = new TripDTO();
        ShipmentDTO shipmentDTO = new ShipmentDTO();

        dto.setTrips(List.of(tripDTO));
        dto.setShipments(List.of(shipmentDTO));

        VehicleResourceDTO vehicleResourceDTO = new VehicleResourceDTO();
        tripDTO.setVehicleResource(vehicleResourceDTO);


        // When
        TransportOrder entity = mapper.toEntity(dto);

        // Then
        Assertions.assertNotNull(entity);
        Assertions.assertNotNull(entity.getTrips());
        Assertions.assertNotNull(entity.getShipments());

        Assertions.assertEquals(1, entity.getTrips().size());
        Assertions.assertEquals(1, entity.getShipments().size());

        Trip mappedTrip = entity.getTrips().stream().toList().getFirst();
        Shipment mappedShipment = entity.getShipments().stream().toList().getFirst();

        assertSame(entity, mappedTrip.getTransportOrder(), "Trip should have back-reference to TransportOrder");
        assertSame(entity, mappedShipment.getTransportOrder(), "Shipment should have back-reference to TransportOrder");
    }

    @Test
    void testMapToDetailsResponse() {
        // Given
        TransportOrder transportOrder = new TransportOrder();
        transportOrder.setCode("TO-12345");
        transportOrder.setCreatedBy("testuser");
        transportOrder.setCreatedAt(1700000000000L);
        transportOrder.setStatus(TransportOrderStatus.ASSIGNED);
        transportOrder.setAssignmentType(AssignmentType.INTERNAL);
        transportOrder.setAssigneeIdentifier("CARRIER-001");
        
        // Setup trips
        Set<Trip> trips = new HashSet<>();
        Trip trip1 = new Trip();
        trip1.setCode("TRIP-001");
        trips.add(trip1);
        
        Trip trip2 = new Trip();
        trip2.setCode("TRIP-002");
        trips.add(trip2);
        
        transportOrder.setTrips(trips);

        // When
        TransportOrderDetailsResponse response = mapper.mapToDetailsResponse(transportOrder);

        // Then
        Assertions.assertNotNull(response);
        Assertions.assertEquals("testuser", response.getCreatedBy());
        Assertions.assertEquals(1700000000000L, response.getCreatedAt());
        Assertions.assertEquals("CARRIER-001", response.getAssignmentDetails().getAssigneeIdentifier());
        
        // Verify status
        Assertions.assertNotNull(response.getStatus());
        Assertions.assertEquals(TransportOrderStatus.ASSIGNED.getDisplayName(), response.getStatus().getLabel());
        Assertions.assertEquals(TransportOrderStatus.ASSIGNED.name(), response.getStatus().getValue());
        
        // Verify assignment type
        Assertions.assertNotNull(response.getAssignmentDetails());
        Assertions.assertEquals(AssignmentType.INTERNAL.getDisplayName(), response.getAssignmentDetails().getAssignmentType().getLabel());
        Assertions.assertEquals(AssignmentType.INTERNAL.name(), response.getAssignmentDetails().getAssignmentType().getValue());
        
        // Verify trips
        Assertions.assertNotNull(response.getTrips());
        Assertions.assertEquals(2, response.getTrips().size());
        
        Set<String> tripCodes = new HashSet<>();
        response.getTrips().forEach(trip -> tripCodes.add(trip.getTripCode()));
        Assertions.assertTrue(tripCodes.contains("TRIP-001"));
        Assertions.assertTrue(tripCodes.contains("TRIP-002"));
    }
}
