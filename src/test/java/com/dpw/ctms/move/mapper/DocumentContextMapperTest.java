 package com.dpw.ctms.move.mapper;

import com.dpw.ctms.move.BaseTest;
import com.dpw.ctms.move.dto.ConsignmentDetailsDTO;
import com.dpw.ctms.move.dto.FacilityDetailsDTO;
import com.dpw.ctms.move.dto.ProductDetailsDTO;
import com.dpw.ctms.move.dto.TimeDTO;
import com.dpw.ctms.move.dto.DateTimeDTO;
import com.dpw.ctms.move.integration.dto.oms.OmsConsignmentDto;
import com.dpw.ctms.move.integration.dto.resource.ResourceFacilitiesDto;
import com.dpw.ctms.move.integration.dto.resource.ResourceProductDetailsDto;
import com.dpw.ctms.move.integration.dto.resource.ResourceUomDto;
import com.dpw.ctms.move.util.DateTimeUtil;
import com.dpw.ctms.move.constants.ProductPropertyConstants;
import com.dpw.ctms.move.fakers.Fakers;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@DisplayName("Document Context Mapper Tests")
class DocumentContextMapperTest extends BaseTest {

    @Autowired
    private DocumentContextMapper documentContextMapper;

    @MockBean
    private DateTimeUtil dateTimeUtil;

    private OmsConsignmentDto omsConsignmentDto;
    private Map<String, ResourceProductDetailsDto> productMap;
    private Map<String, ResourceUomDto> uomMap;
    private ResourceFacilitiesDto resourceFacilitiesDto;

    @BeforeEach
    void setUp() {
        omsConsignmentDto = Fakers.setupOmsConsignmentDto();
        productMap = Fakers.setupProductMap();
        uomMap = Fakers.setupUomMap();
        resourceFacilitiesDto = Fakers.setupResourceFacilitiesDto();
        setupDateTimeUtil();
    }


    private void setupDateTimeUtil() {
        DateTimeDTO mockDateTime = DateTimeDTO.builder()
            .epoch(1704096000000L)
            .iso("2024-01-01T10:00:00")
            .timeZoneId("UTC")
            .build();
        when(dateTimeUtil.fromEpochMillisToDate(anyLong(), anyString()))
            .thenReturn(mockDateTime);
    }

    @Test
    @DisplayName("Should map OMS consignment to ConsignmentDetailsDTO successfully")
    void shouldMapOmsConsignmentToConsignmentDetailsDTO() {
        // When
        ConsignmentDetailsDTO result = documentContextMapper.mapToConsignmentDetailsDTO(
            omsConsignmentDto, productMap, uomMap);

        // Then
        assertNotNull(result);
        assertEquals("123456", result.getConsignmentId());
        assertEquals("CONS001", result.getConsignmentCode());
        assertEquals(12345L, result.getLineItemId());
        assertEquals("LI-123456", result.getLineItemCode());
        assertEquals(12345L, result.getCustomerOrderId());
        assertEquals("CO-123456", result.getCustomerOrderCode());
        assertEquals("Handle with care", result.getSpecialInstructions());
        assertEquals("CUST-1234", result.getCustomerId());
        assertEquals("ORIGIN-LOC", result.getOriginFacilityId());
        assertEquals("DEST-LOC", result.getDestinationFacilityId());
        assertEquals("CONFIRMED", result.getStatus());
        assertEquals("DELIVERY", result.getMovementType());

        // Verify customer order metadata
        assertNotNull(result.getCustomerOrderMetaData());
        assertEquals("CO-123456", result.getCustomerOrderMetaData().getCustomerOrderNumber());
        assertEquals("REF-12345678", result.getCustomerOrderMetaData().getInternalReferenceNumber());
    }

    @Test
    @DisplayName("Should map product details correctly from first product")
    void shouldMapProductDetailsFromFirstProduct() {
        // When
        ConsignmentDetailsDTO result = documentContextMapper.mapToConsignmentDetailsDTO(
            omsConsignmentDto, productMap, uomMap);

        // Then
        assertNotNull(result.getProductDetailsDTO());
        ProductDetailsDTO productDetails = result.getProductDetailsDTO();
        
        assertEquals(101L, productDetails.getResourceId());
        assertEquals("Test Product", productDetails.getName());
        assertEquals("PROD-001", productDetails.getCode());
        assertEquals("UN1234", productDetails.getUnNumber());
        assertEquals("Test product description", productDetails.getDescription());
        assertEquals(false, productDetails.getIsContainerFillingRuleApplied());
        assertEquals(85.0, productDetails.getFilledPercentage());
        assertEquals(false, productDetails.getIsAttentionNeeded());

        // Verify product category details
        assertNotNull(productDetails.getProductCategoryDetails());
        assertEquals(1L, productDetails.getProductCategoryDetails().getId());
        assertEquals("CAT-001", productDetails.getProductCategoryDetails().getCode());
        assertEquals("Electronics", productDetails.getProductCategoryDetails().getName());

        // Verify status details
        assertNotNull(productDetails.getStatus());
        assertEquals("Active", productDetails.getStatus().getLabel());
        assertEquals("ACTIVE", productDetails.getStatus().getValue());
    }

    @Test
    @DisplayName("Should map product properties with UOM enrichment")
    void shouldMapProductPropertiesWithUomEnrichment() {
        // When
        ConsignmentDetailsDTO result = documentContextMapper.mapToConsignmentDetailsDTO(
            omsConsignmentDto, productMap, uomMap);

        // Then
        assertNotNull(result.getProductDetailsDTO());
        Map<String, ProductDetailsDTO.PropertyDetails> properties = 
            result.getProductDetailsDTO().getProperties();
        
        assertNotNull(properties);
        assertEquals(2, properties.size());

        // Verify Weight property
        assertTrue(properties.containsKey("WEIGHT"));
        ProductDetailsDTO.PropertyDetails weightProperty = properties.get("WEIGHT");
        assertEquals("Weight", weightProperty.getName());
        assertEquals(15.5, weightProperty.getValue());
        assertEquals(1L, weightProperty.getResourceUomId());
        assertEquals("Kilogram", weightProperty.getResourceUomName());

        // Verify Volume property
        assertTrue(properties.containsKey("VOLUME"));
        ProductDetailsDTO.PropertyDetails volumeProperty = properties.get("VOLUME");
        assertEquals("Volume", volumeProperty.getName());
        assertEquals(0.02, volumeProperty.getValue());
        assertEquals(2L, volumeProperty.getResourceUomId());
        assertEquals("Cubic Meter", volumeProperty.getResourceUomName());
    }

    @Test
    @DisplayName("Should handle null OMS consignment")
    void shouldHandleNullOmsConsignment() {
        // When
        ConsignmentDetailsDTO result = documentContextMapper.mapToConsignmentDetailsDTO(
            null, productMap, uomMap);

        // Then
        assertNull(result);
    }

    @Test
    @DisplayName("Should handle empty products list")
    void shouldHandleEmptyProductsList() {
        // Given
        OmsConsignmentDto consignmentWithNoProducts = OmsConsignmentDto.builder()
            .id(omsConsignmentDto.getId())
            .code(omsConsignmentDto.getCode())
            .lineItemId(omsConsignmentDto.getLineItemId())
            .lineItemCode(omsConsignmentDto.getLineItemCode())
            .customerOrderId(omsConsignmentDto.getCustomerOrderId())
            .customerOrderCode(omsConsignmentDto.getCustomerOrderCode())
            .customerOrderMetadata(omsConsignmentDto.getCustomerOrderMetadata())
            .lineItemMetadata(omsConsignmentDto.getLineItemMetadata())
            .customer(omsConsignmentDto.getCustomer())
            .origin(omsConsignmentDto.getOrigin())
            .destination(omsConsignmentDto.getDestination())
            .expectedPickupTime(omsConsignmentDto.getExpectedPickupTime())
            .expectedDeliveryTime(omsConsignmentDto.getExpectedDeliveryTime())
            .status(omsConsignmentDto.getStatus())
            .movementType(omsConsignmentDto.getMovementType())
            .products(Collections.emptyList())
            .build();

        // When
        ConsignmentDetailsDTO result = documentContextMapper.mapToConsignmentDetailsDTO(
            consignmentWithNoProducts, productMap, uomMap);

        // Then
        assertNotNull(result);
        assertNull(result.getProductDetailsDTO());
    }

    @Test
    @DisplayName("Should handle null products list")
    void shouldHandleNullProductsList() {
        // Given
        OmsConsignmentDto consignmentWithNullProducts = OmsConsignmentDto.builder()
            .id(omsConsignmentDto.getId())
            .code(omsConsignmentDto.getCode())
            .lineItemId(omsConsignmentDto.getLineItemId())
            .lineItemCode(omsConsignmentDto.getLineItemCode())
            .customerOrderId(omsConsignmentDto.getCustomerOrderId())
            .customerOrderCode(omsConsignmentDto.getCustomerOrderCode())
            .customerOrderMetadata(omsConsignmentDto.getCustomerOrderMetadata())
            .lineItemMetadata(omsConsignmentDto.getLineItemMetadata())
            .customer(omsConsignmentDto.getCustomer())
            .origin(omsConsignmentDto.getOrigin())
            .destination(omsConsignmentDto.getDestination())
            .expectedPickupTime(omsConsignmentDto.getExpectedPickupTime())
            .expectedDeliveryTime(omsConsignmentDto.getExpectedDeliveryTime())
            .status(omsConsignmentDto.getStatus())
            .movementType(omsConsignmentDto.getMovementType())
            .products(null)
            .build();

        // When
        ConsignmentDetailsDTO result = documentContextMapper.mapToConsignmentDetailsDTO(
            consignmentWithNullProducts, productMap, uomMap);

        // Then
        assertNotNull(result);
        assertNull(result.getProductDetailsDTO());
    }

    @Test
    @DisplayName("Should handle missing product in resource map")
    void shouldHandleMissingProductInResourceMap() {
        // Given
        Map<String, ResourceProductDetailsDto> emptyProductMap = Collections.emptyMap();

        // When
        ConsignmentDetailsDTO result = documentContextMapper.mapToConsignmentDetailsDTO(
            omsConsignmentDto, emptyProductMap, uomMap);

        // Then
        assertNotNull(result);
        assertNotNull(result.getProductDetailsDTO());
        
        ProductDetailsDTO productDetails = result.getProductDetailsDTO();
        assertEquals(101L, productDetails.getResourceId());
        
        // Resource details should be null when not found
        assertNull(productDetails.getName());
        assertNull(productDetails.getCode());
        assertNull(productDetails.getUnNumber());
        assertNull(productDetails.getDescription());
        
        // But properties should still be mapped
        assertNotNull(productDetails.getProperties());
        assertEquals(2, productDetails.getProperties().size());
    }

    @Test
    @DisplayName("Should handle missing UOM in resource map")
    void shouldHandleMissingUomInResourceMap() {
        // Given
        Map<String, ResourceUomDto> emptyUomMap = Collections.emptyMap();

        // When
        ConsignmentDetailsDTO result = documentContextMapper.mapToConsignmentDetailsDTO(
            omsConsignmentDto, productMap, emptyUomMap);

        // Then
        assertNotNull(result);
        assertNotNull(result.getProductDetailsDTO());
        
        Map<String, ProductDetailsDTO.PropertyDetails> properties = 
            result.getProductDetailsDTO().getProperties();
        
        // Properties should still be mapped but with null UOM names
        assertTrue(properties.containsKey("WEIGHT"));
        ProductDetailsDTO.PropertyDetails weightProperty = properties.get("WEIGHT");
        assertEquals(1L, weightProperty.getResourceUomId());
        assertNull(weightProperty.getResourceUomName());
    }

    @Test
    @DisplayName("Should calculate volume from dimensions when volume is missing")
    void shouldCalculateVolumeFromDimensions() {
        // Given - Create consignment with dimensions but no volume
        List<OmsConsignmentDto.Property> dimensionProperties = List.of(
            OmsConsignmentDto.Property.builder()
                .propertyNameLabel("Length").propertyNameValue("LENGTH")
                .propertyValue(2.0).unitOfMeasurementId(4L).build(),
            OmsConsignmentDto.Property.builder()
                .propertyNameLabel("Breadth").propertyNameValue("BREADTH")
                .propertyValue(1.5).unitOfMeasurementId(4L).build(),
            OmsConsignmentDto.Property.builder()
                .propertyNameLabel("Height").propertyNameValue("HEIGHT")
                .propertyValue(1.0).unitOfMeasurementId(4L).build()
        );

        OmsConsignmentDto.Product productWithDimensions = OmsConsignmentDto.Product.builder()
            .id(1L).resourceId(101L).properties(dimensionProperties).build();

        OmsConsignmentDto consignmentWithDimensions = OmsConsignmentDto.builder()
            .id(omsConsignmentDto.getId())
            .code(omsConsignmentDto.getCode())
            .lineItemId(omsConsignmentDto.getLineItemId())
            .lineItemCode(omsConsignmentDto.getLineItemCode())
            .customerOrderId(omsConsignmentDto.getCustomerOrderId())
            .customerOrderCode(omsConsignmentDto.getCustomerOrderCode())
            .customerOrderMetadata(omsConsignmentDto.getCustomerOrderMetadata())
            .lineItemMetadata(omsConsignmentDto.getLineItemMetadata())
            .customer(omsConsignmentDto.getCustomer())
            .origin(omsConsignmentDto.getOrigin())
            .destination(omsConsignmentDto.getDestination())
            .expectedPickupTime(omsConsignmentDto.getExpectedPickupTime())
            .expectedDeliveryTime(omsConsignmentDto.getExpectedDeliveryTime())
            .status(omsConsignmentDto.getStatus())
            .movementType(omsConsignmentDto.getMovementType())
            .products(List.of(productWithDimensions))
            .build();

        // Add meter UOM to map
        ResourceUomDto meterUom = ResourceUomDto.builder()
            .id(4L).name("Meter").code("M").type("LENGTH").build();
        Map<String, ResourceUomDto> extendedUomMap = new HashMap<>(uomMap);
        extendedUomMap.put("4", meterUom);

        // When
        ConsignmentDetailsDTO result = documentContextMapper.mapToConsignmentDetailsDTO(
            consignmentWithDimensions, productMap, extendedUomMap);

        // Then
        assertNotNull(result.getProductDetailsDTO());
        Map<String, ProductDetailsDTO.PropertyDetails> properties = 
            result.getProductDetailsDTO().getProperties();

        // Verify original dimensions are present
        assertTrue(properties.containsKey(ProductPropertyConstants.LENGTH));
        assertTrue(properties.containsKey(ProductPropertyConstants.BREADTH));
        assertTrue(properties.containsKey(ProductPropertyConstants.HEIGHT));

        // Verify volume was calculated (2.0 * 1.5 * 1.0 = 3.0)
        assertTrue(properties.containsKey(ProductPropertyConstants.VOLUME));
        ProductDetailsDTO.PropertyDetails volumeProperty = properties.get(ProductPropertyConstants.VOLUME);
        assertNotNull(volumeProperty);
        assertEquals(3.0, volumeProperty.getValue());
        
        // Volume should use same UOM as length
        ProductDetailsDTO.PropertyDetails lengthProperty = properties.get(ProductPropertyConstants.LENGTH);
        assertEquals(lengthProperty.getResourceUomId(), volumeProperty.getResourceUomId());
    }

    @Test
    @DisplayName("Should not calculate volume when volume is already present")
    void shouldNotCalculateVolumeWhenVolumeIsPresent() {
        // Given - OMS consignment already has volume property
        // When
        ConsignmentDetailsDTO result = documentContextMapper.mapToConsignmentDetailsDTO(
            omsConsignmentDto, productMap, uomMap);

        // Then
        Map<String, ProductDetailsDTO.PropertyDetails> properties = 
            result.getProductDetailsDTO().getProperties();

        // Should have the original volume value, not calculated
        assertTrue(properties.containsKey("VOLUME"));
        ProductDetailsDTO.PropertyDetails volumeProperty = properties.get("VOLUME");
        assertEquals(0.02, volumeProperty.getValue()); // Original value, not calculated
    }

    @Test
    @DisplayName("Should map ResourceFacilitiesDto to FacilityDetailsDTO successfully")
    void shouldMapResourceFacilitiesToFacilityDetailsDTO() {
        // When
        FacilityDetailsDTO result = documentContextMapper.mapToFacilityDetailsDTO(resourceFacilitiesDto);

        // Then
        assertNotNull(result);
        assertEquals(1L, result.getId());
        assertEquals("FAC-001", result.getCode());
        assertEquals("Test Facility", result.getName());

        // Verify contact details
        assertNotNull(result.getPointOfContactDetails());
        assertEquals("John", result.getPointOfContactDetails().getFirstName());
        assertEquals("Doe", result.getPointOfContactDetails().getLastName());
        assertEquals("<EMAIL>", result.getPointOfContactDetails().getEmailId());
        assertEquals("1234567890", result.getPointOfContactDetails().getPhoneNumber());
        assertEquals("+1", result.getPointOfContactDetails().getCountryCode());

        // Verify address details
        assertNotNull(result.getAddressDetails());
        assertEquals("123 Main St", result.getAddressDetails().getAddressLine());
        assertEquals("12345", result.getAddressDetails().getPostalCode());
        assertEquals("Downtown", result.getAddressDetails().getSuburbName());
        assertEquals("Test City", result.getAddressDetails().getCityName());
        assertEquals("Test Province", result.getAddressDetails().getProvinceName());
        assertEquals("Test Country", result.getAddressDetails().getCountryName());
    }

    @Test
    @DisplayName("Should handle null ResourceFacilitiesDto")
    void shouldHandleNullResourceFacilitiesDto() {
        // When
        FacilityDetailsDTO result = documentContextMapper.mapToFacilityDetailsDTO(null);

        // Then
        assertNull(result);
    }

    @Test
    @DisplayName("Should handle ResourceFacilitiesDto with null nested objects")
    void shouldHandleResourceFacilitiesDtoWithNullNestedObjects() {
        // Given
        ResourceFacilitiesDto facilityWithNulls = ResourceFacilitiesDto.builder()
            .id(1L).code("FAC-001").name("Test Facility")
            .pointOfContactDetails(null)
            .addressDetails(null)
            .build();

        // When
        FacilityDetailsDTO result = documentContextMapper.mapToFacilityDetailsDTO(facilityWithNulls);

        // Then
        assertNotNull(result);
        assertEquals(1L, result.getId());
        assertEquals("FAC-001", result.getCode());
        assertEquals("Test Facility", result.getName());

        // Contact details should be null
        assertNull(result.getPointOfContactDetails());

        // Address details should be null
        assertNull(result.getAddressDetails());
    }

    @Test
    @DisplayName("Should handle partial address details")
    void shouldHandlePartialAddressDetails() {
        // Given
        ResourceFacilitiesDto.AddressDetails partialAddress = 
            ResourceFacilitiesDto.AddressDetails.builder()
                .addressLine("123 Main St").postalCode("12345")
                .suburbDetails(null) // Missing suburb
                .cityDetails(null)   // Missing city
                .provinceDetails(null) // Missing province
                .countryDetails(null)  // Missing country
                .build();

        ResourceFacilitiesDto facilityWithPartialAddress = ResourceFacilitiesDto.builder()
            .id(resourceFacilitiesDto.getId())
            .code(resourceFacilitiesDto.getCode())
            .name(resourceFacilitiesDto.getName())
            .status(resourceFacilitiesDto.getStatus())
            .pointOfContactDetails(resourceFacilitiesDto.getPointOfContactDetails())
            .addressDetails(partialAddress)
            .build();

        // When
        FacilityDetailsDTO result = documentContextMapper.mapToFacilityDetailsDTO(facilityWithPartialAddress);

        // Then
        assertNotNull(result);
        assertNotNull(result.getAddressDetails());
        assertEquals("123 Main St", result.getAddressDetails().getAddressLine());
        assertEquals("12345", result.getAddressDetails().getPostalCode());
        
        // Missing details should be null
        assertNull(result.getAddressDetails().getSuburbName());
        assertNull(result.getAddressDetails().getCityName());
        assertNull(result.getAddressDetails().getProvinceName());
        assertNull(result.getAddressDetails().getCountryName());
    }
}