package com.dpw.ctms.move.mapper;


import com.dpw.ctms.move.entity.ResourceAssignmentDetails;
import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.Stop;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.Time;
import com.dpw.ctms.move.entity.TrailerResource;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.entity.VehicleOperatorResource;
import com.dpw.ctms.move.entity.VehicleResource;
import com.dpw.ctms.move.enums.AssignmentStatus;
import com.dpw.ctms.move.enums.AssignmentType;
import com.dpw.ctms.move.enums.ShipmentStatus;
import com.dpw.ctms.move.enums.TripStatus;
import com.dpw.ctms.move.integration.dto.resource.ResourceFacilitiesDto;
import com.dpw.ctms.move.integration.dto.trackingservice.TripTrackingDTO;
import com.dpw.ctms.move.integration.mapper.trackingservice.TrackingServiceRequestMapper;
import com.dpw.ctms.move.integration.request.trackingservice.TripTrackingRequest;
import com.dpw.ctms.move.util.DateTimeUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.time.Instant;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

import static com.dpw.ctms.move.constants.TrackingServiceConstants.CLOSE_TRIP;
import static com.dpw.ctms.move.constants.TrackingServiceConstants.START_TRIP;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

class TrackingServiceRequestMapperTest {

    private TrackingServiceRequestMapper trackingServiceRequestMapper;
    private DateTimeUtil dateTimeUtil;

    @BeforeEach
    void setUp() {
        dateTimeUtil = Mockito.mock(DateTimeUtil.class);
        trackingServiceRequestMapper = new TrackingServiceRequestMapper(dateTimeUtil);
    }

    @Test
    void testMapTripTrackingRequest_withStartTripEvent_shouldMapCorrectly() {
        // Arrange
        String tripCode = "TRP-001";
        String crpId = "user123";
        String externalOrderRef = "ORD-999";

        Trip trip = createCompleteTrip();

        Task task = new Task();
        task.setActualStartAt(new Time(Instant.now().getEpochSecond(), "UTC"));

        TripTrackingDTO tripTrackingDTO = TripTrackingDTO.builder()
                .eventType(START_TRIP)
                .currentTrip(trip)
                .task(task)
                .externalCustomerOrderReferenceNumbers(List.of(externalOrderRef))
                .facilityDetails(List.of(ResourceFacilitiesDto.builder()
                                .name("fac1")
                                .addressDetails(ResourceFacilitiesDto.AddressDetails.builder().build())
                        .build()))
                .build();

        when(dateTimeUtil.isoOffsetDateTimeString(Mockito.anyLong(), Mockito.anyString()))
                .thenReturn("2024-07-25T10:00:00+05:30");

        // Act
        TripTrackingRequest result = trackingServiceRequestMapper.mapTripTrackingRequest(tripTrackingDTO);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.getSource()).isEqualTo("CTMS"); // SYSTEM_NAME
        assertThat(result.getTripContext().getTripTypeCode()).isEqualTo(START_TRIP);
        assertThat(result.getData().getTarget().getTrip().getTripNumber()).isEqualTo(tripCode);
        assertThat(result.getData().getTarget().getTrip().getDriverUserID()).isEqualTo(crpId);
        assertThat(result.getData().getTarget().getTrip().getAddedConsignments())
                .extracting("number")
                .containsExactly(externalOrderRef);
        assertThat(result.getData().getTarget().getTrip().getDispatchTime())
                .isEqualTo("2024-07-25T10:00:00+05:30");
    }

    @Test
    void testMapTripTrackingRequest_withCloseTrip_shouldSetClosureTime() {
        // Arrange
        Trip trip = createCompleteTrip();
        Task task = new Task();

        task.setActualStartAt(new Time(Instant.now().getEpochSecond(), "UTC"));

        TripTrackingDTO tripTrackingDTO = TripTrackingDTO.builder()
                .eventType(CLOSE_TRIP)
                .currentTrip(trip)
                .task(task)
                .externalCustomerOrderReferenceNumbers(Collections.emptyList())
                .facilityDetails(List.of(ResourceFacilitiesDto.builder()
                        .name("fac1")
                        .addressDetails(ResourceFacilitiesDto.AddressDetails.builder().build())
                        .build()))
                .build();

        when(dateTimeUtil.isoOffsetDateTimeString(Mockito.anyLong(), Mockito.anyString()))
                .thenReturn("2024-07-25T18:00:00+05:30");

        // Act
        TripTrackingRequest result = trackingServiceRequestMapper.mapTripTrackingRequest(tripTrackingDTO);

        // Assert
        assertThat(result.getData().getTarget().getTrip().getClosureTime())
                .isEqualTo("2024-07-25T18:00:00+05:30");
    }

    @Test
    void testMapTripTrackingRequest_whenNoVehicleOperator_shouldReturnEmptyDriverId() {
        // Arrange
        Trip trip = createCompleteTrip();
        trip.getVehicleOperatorResources().forEach(
                vehicleOperatorResource -> vehicleOperatorResource.setCrpId("")
        );


        TripTrackingDTO tripTrackingDTO = TripTrackingDTO.builder()
                .eventType(START_TRIP)
                .currentTrip(trip)
                .task(new Task())
                .externalCustomerOrderReferenceNumbers(Collections.emptyList())
                .facilityDetails(List.of(ResourceFacilitiesDto.builder()
                        .name("fac1")
                        .addressDetails(ResourceFacilitiesDto.AddressDetails.builder().build())
                        .build()))
                .build();

        // Act
        TripTrackingRequest result = trackingServiceRequestMapper.mapTripTrackingRequest(tripTrackingDTO);

        // Assert
        assertThat(result.getData().getTarget().getTrip().getDriverUserID()).isEqualTo("");
    }

    private Trip createCompleteTrip() {
        Trip trip = new Trip();
        trip.setCode("TRP-001");
        trip.setStatus(TripStatus.CREATED);
        trip.setExternalOriginLocationCode("ORIGIN001");
        trip.setExternalDestinationLocationCode("DEST001");
        trip.setUpdatedAt(6100L);

        // Transport Order
        TransportOrder transportOrder = new TransportOrder();
        transportOrder.setCode("TO001");
        transportOrder.setAssigneeIdentifier("VENDOR001");
        transportOrder.setAssignmentType(AssignmentType.EXTERNAL);
        transportOrder.setUpdatedAt(1100L);
        trip.setTransportOrder(transportOrder);

        // Vehicle Resource
        VehicleResource vehicleResource = new VehicleResource();
        vehicleResource.setExternalResourceId("VEHICLE001");
        vehicleResource.setExternalVehicleTypeId("TRUCK");
        vehicleResource.setRegistrationNumber("REG001");
        vehicleResource.setUpdatedAt(2100L);
        vehicleResource.setTrip(trip);
        trip.setVehicleResource(vehicleResource);

        // Trailer Resources
        Set<TrailerResource> trailerResources = new LinkedHashSet<>();
        TrailerResource trailer1 = new TrailerResource();
        trailer1.setExternalResourceId("TRAILER001");
        trailer1.setUpdatedAt(3100L);
        trailer1.setTrip(trip);

        TrailerResource trailer2 = new TrailerResource();
        trailer2.setExternalResourceId("TRAILER002");
        trailer2.setUpdatedAt(3300L);
        trailer2.setTrip(trip);

        trailerResources.add(trailer1);
        trailerResources.add(trailer2);
        trip.setTrailerResources(trailerResources);

        // Vehicle Operator Resources
        Set<VehicleOperatorResource> operatorResources = new LinkedHashSet<>();
        VehicleOperatorResource operator = new VehicleOperatorResource();
        operator.setExternalResourceId("DRIVER001");
        operator.setCrpId("user123");
        operator.setUpdatedAt(4100L);
        operator.setTrip(trip);

        // Set up resource assignment details
        ResourceAssignmentDetails operatorAssignmentDetails = new ResourceAssignmentDetails();
        operatorAssignmentDetails.setAssignmentStatus(AssignmentStatus.ASSIGNED);
        operator.setResourceAssignmentDetails(operatorAssignmentDetails);

        operatorResources.add(operator);
        trip.setVehicleOperatorResources(operatorResources);

        // Shipments
        Set<Shipment> shipments = new LinkedHashSet<>();
        Shipment shipment1 = new Shipment();
        shipment1.setCode("SHIPMENT001");
        shipment1.setExternalCustomerOrderId("CO001");
        shipment1.setStatus(ShipmentStatus.ALLOCATED);
        shipment1.setUpdatedAt(5100L);
        shipment1.setTrip(trip);

        Shipment shipment2 = new Shipment();
        shipment2.setCode("SHIPMENT002");
        shipment2.setExternalCustomerOrderId("CO002");
        shipment2.setStatus(ShipmentStatus.ALLOCATED);
        shipment2.setUpdatedAt(5300L);
        shipment2.setTrip(trip);

        shipments.add(shipment1);
        shipments.add(shipment2);
        trip.setShipments(shipments);

        // Stops
        Set<Stop> stops = new LinkedHashSet<>();
        Stop stop1 = new Stop();
        stop1.setCode("STOP001");
        stop1.setExternalLocationCode("LOC001");
        stop1.setSequence(1);
        stop1.setUpdatedAt(8100L);
        stop1.setTrip(trip);

        Stop stop2 = new Stop();
        stop2.setCode("STOP002");
        stop2.setExternalLocationCode("LOC002");
        stop2.setSequence(2);
        stop2.setUpdatedAt(8300L);
        stop2.setTrip(trip);

        stops.add(stop1);
        stops.add(stop2);
        trip.setStops(stops);

        return trip;
    }
}
