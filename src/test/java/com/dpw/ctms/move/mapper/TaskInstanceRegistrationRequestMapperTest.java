package com.dpw.ctms.move.mapper;

import com.dpw.ctms.move.dto.taskmanager.TaskRegistrationDTO;
import com.dpw.ctms.move.integration.request.taskmanager.TaskInstanceRegistrationRequest;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

class TaskInstanceRegistrationRequestMapperTest {

    private final TaskInstanceRegistrationRequestMapper mapper = Mappers.getMapper(TaskInstanceRegistrationRequestMapper.class);

    @Test
    void testToTaskInstanceRegisterRequest() {
        TaskRegistrationDTO.TaskInstanceRegisterDetailsDTO task = TaskRegistrationDTO.TaskInstanceRegisterDetailsDTO
                .builder().extTaskTransactionCode("TASK123")
                .extTaskMasterCode("MASTER123")
                .build();
        List<TaskRegistrationDTO.TaskInstanceRegisterDetailsDTO> tasks = Collections.singletonList(task);

        var taskRegistrationDTO = TaskRegistrationDTO.builder().tasks(tasks).build();
        TaskInstanceRegistrationRequest request = mapper.toTaskInstanceRegisterRequest(taskRegistrationDTO);
        assertNotNull(request);
        assertNotNull(request.getTasks());
        assertEquals(1, request.getTasks().size());
        TaskInstanceRegistrationRequest.TaskInstanceRegisterDetailsRequest details = request.getTasks().get(0);
        assertEquals("TASK123", details.getExtTaskTransactionCode());
    }
}

