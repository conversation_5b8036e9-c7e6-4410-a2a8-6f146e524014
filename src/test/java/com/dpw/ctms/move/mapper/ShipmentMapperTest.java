package com.dpw.ctms.move.mapper;

import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.Stop;
import com.dpw.ctms.move.entity.Time;
import com.dpw.ctms.move.enums.ShipmentStatus;
import com.dpw.ctms.move.enums.StopStatus;
import com.dpw.ctms.move.response.EnumLabelValueResponse;
import com.dpw.ctms.move.response.ShipmentListingResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

import static org.junit.jupiter.api.Assertions.*;

public class ShipmentMapperTest {

    private ShipmentMapper shipmentMapper;
    private Shipment shipment;

    @BeforeEach
    void setUp() {
        shipmentMapper = Mappers.getMapper(ShipmentMapper.class);
        shipment = createCompleteShipment();
    }

    @Test
    void toResponse_WithCompleteShipment_ShouldMapBasicFields() {
        // Act
        ShipmentListingResponse response = shipmentMapper.toResponse(shipment);

        // Assert
        assertNotNull(response);
        assertEquals("SHIP001", response.getCode());
        assertEquals("CO001", response.getCustomerOrderId());
        assertEquals("CONSIGNMENT001", response.getConsignmentId());

        // Verify status mapping
        assertNotNull(response.getStatus());
        assertEquals("ASSIGNED", response.getStatus().getValue());

        // Verify timestamps
        assertEquals(5000L, response.getUpdatedAt());

        // Verify expected times
        assertNotNull(response.getExpectedTimes());
        assertEquals(1500L, response.getExpectedTimes().getStartAt().getEpoch());
        assertEquals("UTC", response.getExpectedTimes().getStartAt().getTimezone());
        assertEquals(1800L, response.getExpectedTimes().getEndAt().getEpoch());
        assertEquals("UTC", response.getExpectedTimes().getEndAt().getTimezone());

        // Verify actual times
        assertNotNull(response.getActualTimes());
        assertEquals(1600L, response.getActualTimes().getStartAt().getEpoch());
        assertEquals("UTC", response.getActualTimes().getStartAt().getTimezone());
        assertEquals(1900L, response.getActualTimes().getEndAt().getEpoch());
        assertEquals("UTC", response.getActualTimes().getEndAt().getTimezone());
    }

    @Test
    void toResponse_WithNullShipment_ShouldReturnNull() {
        // Act
        ShipmentListingResponse response = shipmentMapper.toResponse(null);

        // Assert
        assertNull(response);
    }

    @Test
    void toResponse_WithMinimalShipment_ShouldHandleNullFields() {
        // Arrange
        Shipment minimalShipment = new Shipment();
        minimalShipment.setCode("SHIP_MINIMAL");

        // Act
        ShipmentListingResponse response = shipmentMapper.toResponse(minimalShipment);

        // Assert
        assertNotNull(response);
        assertEquals("SHIP_MINIMAL", response.getCode());
        assertNull(response.getTripCode());
        assertNull(response.getCustomerOrderId());
        assertNull(response.getConsignmentId());
        assertNull(response.getTransportOrderCode());
        assertNull(response.getStatus());
        assertNull(response.getUpdatedAt());
        
        // Verify time ranges are created but with null values
        assertNotNull(response.getExpectedTimes());
        assertNull(response.getExpectedTimes().getStartAt());
        assertNull(response.getExpectedTimes().getEndAt());
        
        assertNotNull(response.getActualTimes());
        assertNull(response.getActualTimes().getStartAt());
        assertNull(response.getActualTimes().getEndAt());
        
        assertNull(response.getOriginStop());
        assertNull(response.getDestinationStop());
    }

    @Test
    void toResponse_WithNullStatus_ShouldHandleGracefully() {
        // Arrange
        shipment.setStatus(null);

        // Act
        ShipmentListingResponse response = shipmentMapper.toResponse(shipment);

        // Assert
        assertNotNull(response);
        assertEquals("SHIP001", response.getCode());
        assertNull(response.getStatus());
    }

    @Test
    void toResponse_WithNullTrip_ShouldHandleGracefully() {
        // Arrange
        shipment.setTrip(null);

        // Act
        ShipmentListingResponse response = shipmentMapper.toResponse(shipment);

        // Assert
        assertNotNull(response);
        assertEquals("SHIP001", response.getCode());
        assertNull(response.getTripCode());
    }

    @Test
    void toResponse_WithNullTransportOrder_ShouldHandleGracefully() {
        // Arrange
        shipment.setTransportOrder(null);

        // Act
        ShipmentListingResponse response = shipmentMapper.toResponse(shipment);

        // Assert
        assertNotNull(response);
        assertEquals("SHIP001", response.getCode());
        assertNull(response.getTransportOrderCode());
    }

    @Test
    void toResponse_WithNullStops_ShouldHandleGracefully() {
        // Arrange
        shipment.setOriginStop(null);
        shipment.setDestinationStop(null);

        // Act
        ShipmentListingResponse response = shipmentMapper.toResponse(shipment);

        // Assert
        assertNotNull(response);
        assertEquals("SHIP001", response.getCode());
        assertNull(response.getOriginStop());
        assertNull(response.getDestinationStop());
    }

    @Test
    void toResponse_WithNullTimestamps_ShouldHandleGracefully() {
        // Arrange
        shipment.setExpectedPickupAt(null);
        shipment.setExpectedDeliveryAt(null);
        shipment.setActualPickupAt(null);
        shipment.setActualDeliveryAt(null);
        shipment.setUpdatedAt(null);

        // Act
        ShipmentListingResponse response = shipmentMapper.toResponse(shipment);

        // Assert
        assertNotNull(response);
        assertEquals("SHIP001", response.getCode());
        assertNull(response.getUpdatedAt());
        
        // Verify time ranges are created but with null values
        assertNotNull(response.getExpectedTimes());
        assertNull(response.getExpectedTimes().getStartAt());
        assertNull(response.getExpectedTimes().getEndAt());
        
        assertNotNull(response.getActualTimes());
        assertNull(response.getActualTimes().getStartAt());
        assertNull(response.getActualTimes().getEndAt());
    }

    @Test
    void mapStatusInfo_WithValidEnum_ShouldMapCorrectly() {
        // Act
        EnumLabelValueResponse result = shipmentMapper.mapStatusInfo(ShipmentStatus.ASSIGNED);

        // Assert
        assertNotNull(result);
        assertEquals("Assigned", result.getLabel());
        assertEquals("ASSIGNED", result.getValue());
    }

    @Test
    void mapStatusInfo_WithNullEnum_ShouldReturnNull() {
        // Act
        EnumLabelValueResponse result = shipmentMapper.mapStatusInfo(null);

        // Assert
        assertNull(result);
    }

    @Test
    void mapShipmentExpectedTimes_WithValidTimestamps_ShouldMapCorrectly() {
        // Act
        ShipmentListingResponse.TimeRange result = shipmentMapper.mapShipmentExpectedTimes(shipment);

        // Assert
        assertNotNull(result);
        assertEquals(1500L, result.getStartAt().getEpoch());
        assertEquals("UTC", result.getStartAt().getTimezone());
        assertEquals(1800L, result.getEndAt().getEpoch());
        assertEquals("UTC", result.getEndAt().getTimezone());
    }

    @Test
    void mapShipmentActualTimes_WithValidTimestamps_ShouldMapCorrectly() {
        // Act
        ShipmentListingResponse.TimeRange result = shipmentMapper.mapShipmentActualTimes(shipment);

        // Assert
        assertNotNull(result);
        assertEquals(1600L, result.getStartAt().getEpoch());
        assertEquals("UTC", result.getStartAt().getTimezone());
        assertEquals(1900L, result.getEndAt().getEpoch());
        assertEquals("UTC", result.getEndAt().getTimezone());
    }

    @Test
    void mapStop_WithValidStop_ShouldMapCorrectly() {
        // Arrange
        Stop stop = new Stop();
        stop.setId(1L);
        stop.setCode("STOP001");
        stop.setExternalLocationCode("ORIGIN001");
        stop.setStatus(StopStatus.PLANNED);
        stop.setUpdatedAt(4000L);

        // Act
        ShipmentListingResponse.Stop result = shipmentMapper.mapStop(stop);

        // Assert
        assertNotNull(result);
        assertEquals("1", result.getId());
        assertEquals("STOP001", result.getCode());
        assertEquals("ORIGIN001", result.getExternalLocationCode());
        assertNotNull(result.getStatus());
        assertEquals("PLANNED", result.getStatus().getValue());
        assertEquals(4000L, result.getUpdatedAt());
    }

    @Test
    void mapStop_WithNullStop_ShouldReturnNull() {
        // Act
        ShipmentListingResponse.Stop result = shipmentMapper.mapStop(null);

        // Assert
        assertNull(result);
    }

    // Helper methods
    private Shipment createCompleteShipment() {
        // Create a minimal shipment for testing
        Shipment shipment = new Shipment();
        shipment.setCode("SHIP001");
        shipment.setStatus(ShipmentStatus.ASSIGNED);
        shipment.setExternalCustomerOrderId("CO001");
        shipment.setExternalConsignmentId("CONSIGNMENT001");
        shipment.setExpectedPickupAt(new Time(1500L, "UTC"));
        shipment.setExpectedDeliveryAt(new Time(1800L, "UTC"));
        shipment.setActualPickupAt(new Time(1600L, "UTC"));
        shipment.setActualDeliveryAt(new Time(1900L, "UTC"));
        shipment.setUpdatedAt(5000L);
        return shipment;
    }
}
