package com.dpw.ctms.move.mapper;

import com.dpw.ctms.move.controller.IntegrationTestBase;
import com.dpw.ctms.move.entity.*;
import com.dpw.ctms.move.enums.AssignmentType;
import com.dpw.ctms.move.enums.ResourceAssignmentType;
import com.dpw.ctms.move.enums.ShipmentStatus;
import com.dpw.ctms.move.enums.TransportOrderStatus;
import com.dpw.ctms.move.enums.TripStatus;
import com.dpw.ctms.move.response.TripViewResponse;
import com.github.javafaker.Faker;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashSet;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

class TripViewMapperIntegrationTest extends IntegrationTestBase {

    @Autowired
    private TripViewMapper tripViewMapper;

    private Faker faker;

    @BeforeEach
    void setUp() {
        faker = new Faker();
    }

    @Test
    void shouldMapTripToTripViewResponse() {
        // Given
        Trip trip = createSampleTrip();

        // When
        TripViewResponse response = tripViewMapper.toResponse(trip);

        // Then
        assertNotNull(response);
        assertEquals(trip.getCode(), response.getCode());
        assertEquals(trip.getStatus().getDisplayName(), response.getStatus().getLabel());
        assertEquals(trip.getStatus().name(), response.getStatus().getValue());
        assertNotNull(response.getTransportOrder());
        assertEquals(trip.getTransportOrder().getCode(), response.getTransportOrder().getCode());
        assertEquals(1, response.getStops().size());
        assertEquals(1, response.getShipments().size());
        assertNotNull(response.getResourceDetails());
        assertNotNull(response.getResourceDetails().getVehicleResource());
        assertEquals(1, response.getResourceDetails().getTrailerResources().size());
        assertEquals(1, response.getResourceDetails().getVehicleOperatorResources().size());
    }

    private Trip createSampleTrip() {
        Trip trip = new Trip();
        trip.setCode(faker.regexify("[A-Z]{2}-[0-9]{4}"));
        trip.setStatus(TripStatus.CREATED);

        TransportOrder transportOrder = new TransportOrder();
        transportOrder.setCode(faker.regexify("[A-Z]{2}-[0-9]{4}"));
        transportOrder.setStatus(TransportOrderStatus.ASSIGNED);
        trip.setTransportOrder(transportOrder);

        Stop stop = new Stop();
        stop.setCode(faker.regexify("[A-Z]{2}-[0-9]{4}"));
        stop.setExternalLocationCode(faker.regexify("[A-Z]{3}-[0-9]{3}"));
        stop.setTrip(trip);
        trip.setStops(new HashSet<>(List.of(stop)));

        Shipment shipment = new Shipment();
        shipment.setCode(faker.regexify("[A-Z]{2}-[0-9]{4}"));
        shipment.setStatus(ShipmentStatus.ALLOCATED);
        shipment.setTrip(trip);
        shipment.setOriginStop(stop);
        shipment.setDestinationStop(stop);
        trip.setShipments(new HashSet<>(List.of(shipment)));

        VehicleResource vehicleResource = new VehicleResource();
        vehicleResource.setExternalResourceId(faker.regexify("[A-Z]{3}-[0-9]{4}"));
        vehicleResource.setTrip(trip);
        trip.setVehicleResource(vehicleResource);

        TrailerResource trailerResource = new TrailerResource();
        trailerResource.setExternalResourceId(faker.regexify("[A-Z]{3}-[0-9]{4}"));
        trailerResource.setTrip(trip);
        trip.setTrailerResources(new HashSet<>(List.of(trailerResource)));

        VehicleOperatorResource operatorResource = new VehicleOperatorResource();
        operatorResource.setExternalResourceId(faker.regexify("[A-Z]{2}-[0-9]{4}"));
        operatorResource.setTrip(trip);
        trip.setVehicleOperatorResources(new HashSet<>(List.of(operatorResource)));

        return trip;
    }

    @Test
    void shouldUseTransportOrderExternalAssignmentTypeWhenVehicleResourceAssignmentTypeIsNull() {
        // Given
        Trip trip = createSampleTrip();
        trip.getTransportOrder().setAssignmentType(AssignmentType.EXTERNAL);
        // Vehicle resource has no assignment details
        trip.getVehicleResource().setResourceAssignmentDetails(null);

        // When
        TripViewResponse response = tripViewMapper.toResponse(trip);

        // Then
        assertNotNull(response.getResourceDetails().getVehicleResource().getResourceAssignmentType());
        assertEquals("External", response.getResourceDetails().getVehicleResource().getResourceAssignmentType().getLabel());
        assertEquals("VENDOR", response.getResourceDetails().getVehicleResource().getResourceAssignmentType().getValue());
    }

    @Test
    void shouldUseTransportOrderInternalAssignmentTypeWhenVehicleResourceAssignmentTypeIsNull() {
        // Given
        Trip trip = createSampleTrip();
        trip.getTransportOrder().setAssignmentType(AssignmentType.INTERNAL);
        // Vehicle resource has no assignment details
        trip.getVehicleResource().setResourceAssignmentDetails(null);

        // When
        TripViewResponse response = tripViewMapper.toResponse(trip);

        // Then
        assertNotNull(response.getResourceDetails().getVehicleResource().getResourceAssignmentType());
        assertEquals("Internal", response.getResourceDetails().getVehicleResource().getResourceAssignmentType().getLabel());
        assertEquals("COST_CENTER", response.getResourceDetails().getVehicleResource().getResourceAssignmentType().getValue());
    }

    @Test
    void shouldUseVehicleResourceAssignmentTypeWhenAvailable() {
        // Given
        Trip trip = createSampleTrip();
        trip.getTransportOrder().setAssignmentType(AssignmentType.EXTERNAL);
        // Vehicle resource has its own assignment type
        ResourceAssignmentDetails assignmentDetails = new ResourceAssignmentDetails();
        assignmentDetails.setResourceAssignmentType(ResourceAssignmentType.COST_CENTER);
        trip.getVehicleResource().setResourceAssignmentDetails(assignmentDetails);

        // When
        TripViewResponse response = tripViewMapper.toResponse(trip);

        // Then
        assertNotNull(response.getResourceDetails().getVehicleResource().getResourceAssignmentType());
        assertEquals("Internal", response.getResourceDetails().getVehicleResource().getResourceAssignmentType().getLabel());
        assertEquals("COST_CENTER", response.getResourceDetails().getVehicleResource().getResourceAssignmentType().getValue());
    }
} 