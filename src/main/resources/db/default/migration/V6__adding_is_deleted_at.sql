ALTER TABLE shipment
    ADD COLUMN IF NOT EXISTS deleted_at BIGINT;

ALTER TABLE shipment_aud
    ADD COLUMN IF NOT EXISTS deleted_at BIGINT;

DROP INDEX IF EXISTS idx_shipment_code_unique;


ALTER TABLE trip
    ADD COLUMN IF NOT EXISTS deleted_at BIGINT;

ALTER TABLE trip DROP CONSTRAINT IF EXISTS trip_code_key;

ALTER TABLE trip_aud
    ADD COLUMN IF NOT EXISTS deleted_at BIGINT;

ALTER TABLE task
    ADD COLUMN IF NOT EXISTS deleted_at BIGINT;

ALTER TABLE task_aud
    ADD COLUMN IF NOT EXISTS deleted_at BIGINT;

ALTER TABLE task DROP CONSTRAINT IF EXISTS task_code_key;


ALTER TABLE stop
    ADD COLUMN IF NOT EXISTS deleted_at BIGINT;

ALTER TABLE stop_aud
    ADD COLUMN IF NOT EXISTS deleted_at BIGINT;

ALTER TABLE vehicle_resource
    ADD COLUMN IF NOT EXISTS deleted_at BIGINT;

ALTER TABLE vehicle_resource_aud
    ADD COLUMN IF NOT EXISTS deleted_at BIGINT;

<PERSON>TER TABLE vehicle_resource DROP CONSTRAINT IF EXISTS vehicle_resource_code_key;


ALTER TABLE trailer_resource
    ADD COLUMN IF NOT EXISTS deleted_at BIGINT;

ALTER TABLE trailer_resource_aud
    ADD COLUMN IF NOT EXISTS deleted_at BIGINT;

ALTER TABLE trailer_resource DROP CONSTRAINT IF EXISTS trailer_resource_code_key;


ALTER TABLE vehicle_operator_resource
    ADD COLUMN IF NOT EXISTS deleted_at BIGINT;

ALTER TABLE vehicle_operator_resource_aud
    ADD COLUMN IF NOT EXISTS deleted_at BIGINT;

ALTER TABLE vehicle_operator_resource DROP CONSTRAINT IF EXISTS vehicle_operator_resource_code_key;


ALTER TABLE task_param
    ADD COLUMN IF NOT EXISTS deleted_at BIGINT;

ALTER TABLE task_param_aud
    ADD COLUMN IF NOT EXISTS deleted_at BIGINT;