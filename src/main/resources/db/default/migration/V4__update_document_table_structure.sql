-- Drop existing indexes
DROP INDEX IF EXISTS idx_document_trip_code_document_type_checksum;
DROP INDEX IF EXISTS idx_document_trip_code;

-- Add new columns if they don't exist
ALTER TABLE document 
    ADD COLUMN IF NOT EXISTS entity_id VARCHAR,
    ADD COLUMN IF NOT EXISTS entity_type VARCHAR,
    ADD COLUMN IF NOT EXISTS status VARCHAR,
    ADD COLUMN IF NOT EXISTS document_operation_type VARCHAR,
    ADD COLUMN IF NOT EXISTS client_identifier VARCHAR,
    ADD COLUMN IF NOT EXISTS async_mapping_uuid VARCHAR,
    ADD COLUMN IF NOT EXISTS file_name VARCHAR,
    ADD COLUMN IF NOT EXISTS file_size INTEGER,
    ADD COLUMN IF NOT EXISTS file_type VARCHAR,
    ADD COLUMN IF NOT EXISTS presigned_download_url VARCHAR,
    ADD COLUMN IF NOT EXISTS file_metadata JSONB;

-- Migrate data from old columns to new columns
UPDATE document
SET entity_id = trip_code,
    entity_type = 'TRIP',
    status = 'ACTIVE',
    document_operation_type = 'DOWNLOAD',
    presigned_download_url = download_url
WHERE entity_id IS NULL;

-- Drop old columns
ALTER TABLE document 
    DROP COLUMN IF EXISTS trip_code,
    DROP COLUMN IF EXISTS download_url,
    DROP COLUMN IF EXISTS document_type;

ALTER TABLE document
    ADD COLUMN  IF NOT EXISTS document_type VARCHAR;

-- Create indexes on the specified columns
CREATE INDEX IF NOT EXISTS idx_document_file_identifier ON document (file_identifier);

-- Create composite indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_document_entity_composite ON document (entity_id, entity_type, document_operation_type);

-- Update audit table structure
ALTER TABLE document_aud
    ADD COLUMN IF NOT EXISTS entity_id VARCHAR,
    ADD COLUMN IF NOT EXISTS entity_type VARCHAR,
    ADD COLUMN IF NOT EXISTS status VARCHAR,
    ADD COLUMN IF NOT EXISTS document_operation_type VARCHAR,
    ADD COLUMN IF NOT EXISTS client_identifier VARCHAR,
    ADD COLUMN IF NOT EXISTS async_mapping_uuid VARCHAR,
    ADD COLUMN IF NOT EXISTS file_name VARCHAR,
    ADD COLUMN IF NOT EXISTS file_size INTEGER,
    ADD COLUMN IF NOT EXISTS file_type VARCHAR,
    ADD COLUMN IF NOT EXISTS presigned_download_url VARCHAR,
    ADD COLUMN IF NOT EXISTS file_metadata JSONB;

-- Drop old columns from audit table
ALTER TABLE document_aud
    DROP COLUMN IF EXISTS trip_code,
    DROP COLUMN IF EXISTS download_url;


DROP  TABLE IF EXISTS attachment;
DROP  TABLE IF EXISTS attachment_aud;

-- Add unique index on shipment code
CREATE UNIQUE INDEX IF NOT EXISTS idx_shipment_code_unique ON shipment (code);