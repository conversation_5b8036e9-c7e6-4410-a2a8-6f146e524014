ALTER TABLE trip
    ADD COLUMN IF NOT EXISTS expected_start_at_timezone VARCHAR,
    ADD COLUMN IF NOT EXISTS expected_end_at_timezone VARCHAR,
    ADD COLUMN IF NOT EXISTS actual_start_at_timezone VARCHAR,
    ADD COLUMN IF NOT EXISTS actual_end_at_timezone VARCHAR;

ALTER TABLE trip_aud
    ADD COLUMN IF NOT EXISTS expected_start_at_timezone VARCHAR,
    ADD COLUMN IF NOT EXISTS expected_end_at_timezone VARCHAR,
    ADD COLUMN IF NOT EXISTS actual_start_at_timezone VARCHAR,
    ADD COLUMN IF NOT EXISTS actual_end_at_timezone VARCHAR;

ALTER TABLE shipment
    ADD COLUMN IF NOT EXISTS expected_pick_at_timezone VARCHAR,
    ADD COLUMN IF NOT EXISTS expected_delivery_at_timezone VARCHAR,
    ADD COLUMN IF NOT EXISTS actual_pick_at_timezone VARCHAR,
    ADD COLUMN IF NOT EXISTS actual_delivery_at_timezone VARCHAR;

ALTER TABLE shipment_aud
    ADD COLUMN IF NOT EXISTS expected_pick_at_timezone VARCHAR,
    ADD COLUMN IF NOT EXISTS expected_delivery_at_timezone VARCHAR,
    ADD COLUMN IF NOT EXISTS actual_pick_at_timezone VARCHAR,
    ADD COLUMN IF NOT EXISTS actual_delivery_at_timezone VARCHAR;

ALTER TABLE task
    ADD COLUMN IF NOT EXISTS expected_start_at_timezone VARCHAR,
    ADD COLUMN IF NOT EXISTS expected_end_at_timezone VARCHAR,
    ADD COLUMN IF NOT EXISTS actual_start_at_timezone VARCHAR,
    ADD COLUMN IF NOT EXISTS actual_end_at_timezone VARCHAR;

ALTER TABLE task_aud
    ADD COLUMN IF NOT EXISTS expected_start_at_timezone VARCHAR,
    ADD COLUMN IF NOT EXISTS expected_end_at_timezone VARCHAR,
    ADD COLUMN IF NOT EXISTS actual_start_at_timezone VARCHAR,
    ADD COLUMN IF NOT EXISTS actual_end_at_timezone VARCHAR;