spring:
  application:
    name: ctms-move
  datasource:
    url: jdbc:postgresql://${DB_HOST_ADDRESS:localhost}:${DB_HOST_PORT:5432}/${DB_NAME:ctms_move_staging}?reWriteBatchedInserts=true
    username: ${DB_USER_NAME:postgres}
    password: ${DB_USER_PWD:postgres}
    driverClassName: org.postgresql.Driver
    hikari:
      connection-timeout: ${HKRI_CONNECTION_TIMEOUT:50000}
      maximum-pool-size: ${HKRI_MAX_POOL_SIZE:30}
      minimum-idle: ${HKRI_MIN_IDLE:30}
      auto-commit: ${HKRI_AUTO_COMM:true}
      connection-init-sql: ${HKRI_INIT_SQL:SELECT 1}
  jpa:
    hibernate:
      ddl-auto: validate
      naming:
        implicit-strategy: "org.hibernate.boot.model.naming.ImplicitNamingStrategyLegacyJpaImpl"
        physical-strategy: "org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl"
    show_sql: false
    properties:
      org.hibernate.envers.store_data_at_delete: true
      hibernate:
        order_inserts: true
        order_updates: true
        dialect: org.hibernate.dialect.PostgreSQLDialect
        jdbc:
          batch_size: 100
          batch_versioned_data: true
          lob:
            non_contextual_creation: true

  servlet:
    multipart:
      enabled: true
      maxFileSize: 20MB
      maxRequestSize: 20MB
      location: ${user.home}/
  security:
    basic:
      enabled: false
    oauth2:
      resourceserver:
        name: oauth2-resource
        jwt:
          issuer-uri: ${crp.scheme}://${crp.baseUrl}/auth/realms/${crp.realms}
          jwk-set-uri-path: ${crp.certsPath}
  autoconfigure:
    exclude:
      - org.redisson.spring.starter.RedissonAutoConfiguration
      - org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration
      - org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration
  data:
    redis:
      repositories:
        enabled: false

multi-tenant:
  enabled: true
  entity:
    packages: com.dpw.ctms.move.entity
  multi-data-source:
    enabled: false  # Setting to true enables database-per-tenant, false defaults to schema-per-tenant
    configs:
      tenantConfigs:
        - tenantName: CFR
          flyway:
            change-log: db/default/migration,db/cfr/migration

springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    authentication:
      enabled: false


server:
  servlet:
    context-path: /move
  port: 8093
  max-http-request-header-size: 32768
eureka:
  instance:
    preferIpAddress: true
  client:
    serviceUrl:
      defaultZone: ${REGISTRY_DEFAULT_ZONE:http://${REGISTRY_SERVER:localhost}:${REGISTRY_PORT:8761}/eureka/}


logging:
  pattern:
    console: "[%d{dd-MMM;HH:mm:ss.SSS}] [${spring.application.name}] [%mdc{UserId}] [transaction-id: %mdc{transaction-id}] [trace-id: %mdc{traceId}] [%-5level] [%thread] [%logger{20}.%M.%L - %msg]%n"
  level:
    org.zalando.logbook : TRACE

ctms:
  auth:
    enabled: ${CTMS_AUTH_ENABLED:true}

tms:
  auth:
    enabled: ${TMS_AUTH_ENABLED:true}
    provider: ${TMS_AUTH_PRVDR:APIM}
    user-preference:
      fetch-always: ${TMS_USER_PREF_FETCH_ALWAYS:false}
      apis: ${TMS_USER_PREF_APIS:}
  swagger:
    enable: ${SWAGGR_ENABLE:true}
    module-name: ${SWAGGR_MOD_NAME:move}
    api-version: ${SWAGGR_API_VER:v1}
    server-urls: ${SWGR_SERVRS:http://localhost:8093/move}
  app:
    timezone: ${TMS_APP_TIMEZONE:UTC}
  audit:
    envers:
      enabled: ${TMS_AUDIT_ENVERS:false}
  crp:
    userInfoUri: ${crp.scheme}://${crp.url}
  pagination:
    page-no: ${DFLT_PG_NO:0}
    page-size: ${DFLT_PG_SZ:10}
  service-url:
    task-service: ${TASK_SERVICE_URL:http://staging-dpw-task.cargoes.internal/task-service}
    oms: ${OMS_SERVER_URL:http://oms-staging-ctms.cargoes.com/oms}
    resource: ${RESOURCE_SERVER_URL:http://resource-staging-ctms.cargoes.com/resource}
    document-service: ${DOCUMENT_SERVICE_URL:https://staging-document-service-api.cargoes.com}

kafka:
  security:
    protocol: ${KAFKA_SECURITY_PROTOCOL:SASL_SSL}
  sasl:
    mechanism: ${KAFKA_SASL_MECHANISM:PLAIN}
  consumer:
    shipment-cancellation:
      bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS_SHIPMENT_CANCELLATION:eh-cargoestms-staging-03.servicebus.windows.net:9093}
      topic: ${TOPIC_SHIPMENT_CANCELLATION:ctms-planner-shipment-update}
      username: ${EVENTHUB_USERNAME_SHIPMENT_CANCELLATION:$ConnectionString}
      password: ${CONNECTION_STRING_SHIPMENT_CANCELLATION:Endpoint=sb://eh-cargoestms-staging-03.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=muoByP0n77+kKbt8ljsyrikTmu6SDG6P6+AEhEREkzI=}
      consumer-group-id: ${CONSUMER_GROUP_SHIPMENT_CANCELLATION:ctms-planner-shipment-update-staging}
      container-factory: shipmentCancellationContainerFactory
      enable: ${KAFKA_SHIPMENT_CANCELLATION_CONSUMER_AUTOSTART:true}
      concurrent-consumers: ${KAFKA_SHIPMENT_CANCELLATION_CONSUMER_CONCURRENT:1}
    shipment-task-acknowledgement:
      bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS_SHIPMENT_TASK:eh-cargoestms-staging-04.servicebus.windows.net:9093}
      topic: ${TOPIC_SHIPMENT_TASK:dpw-task-execution-results-staging}
      username: ${EVENTHUB_USERNAME_SHIPMENT_TASK:$ConnectionString}
      password: ${CONNECTION_STRING_SHIPMENT_TASK:Endpoint=sb://eh-cargoestms-staging-04.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=5A7XXKpecp/MGIiuvyprtHZ6Uw1D0T8LA+AEhF/9NFs=}
      consumer-group-id: ${CONSUMER_GROUP_SHIPMENT_TASK:dpw-task-execution-results-staging}
      container-factory: shipmentTaskContainerFactory
      enable: ${KAFKA_SHIPMENT_TASK_CONSUMER_AUTOSTART:true}
      concurrent-consumers: ${KAFKA_SHIPMENT_TASK_CONSUMER_CONCURRENT:1}
    document-upload:
      bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS_DOCUMENT_UPLOAD:eh-cargoestms-dev.servicebus.windows.net:9093}
      concurrent-consumers: ${KAFKA_DOCUMENT_UPLOAD_CONSUMER_CONCURRENT:1}
      username: ${EVENTHUB_USERNAME_DOCUMENT_UPLOAD:$ConnectionString}
      password: ${DOCUMENT_UPLOAD_CONNECTION_STRING:Endpoint=sb://eh-cargoestms-dev.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=yZcfo02+0XIVbQL+IZtbhV5vD65eLwJF7+AEhHvXXZ4=}
      consumer-group-id: ${CONSUMER_GROUP_DOCUMENT_UPLOAD:document-upload-events-consumer-group}
      container-factory: documentUploadContainerFactory
      topic: ${DOCUMENT_UPLOAD_COMPLETE_TOPIC:tms-shipment-document-upload-events}
      file-upload-subscription-name: ${DOCUMENT_SUBSCRIPTION:tms-shipment-service}
      enable: ${KAFKA_DOCUMENT_UPLOAD_CONSUMER_AUTOSTART:false}
  producer:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:eh-cargoestms-dev-03.servicebus.windows.net:9093}
    request-timeout-ms: ${KAFKA_PRODUCER_REQUEST_TIMEOUT_MS:30000}
    metadata-max-age: ${KAFKA_PRODUCER_METADATA_MAX_AGE:180000}
    username: ${EVENTHUB_USERNAME_DOCUMENT_UPLOAD:$ConnectionString}
    password: ${DOCUMENT_UPLOAD_CONNECTION_STRING:Endpoint=sb://eh-cargoestms-dev-03.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=Q8HWxTr52xI9sxsYS62v40eBEM/Rvcj6L+AEhCvZTo0=}
    metadata-max-idle: ${KAFKA_PRODUCER_METADATA_MAX_IDLE:180000}
    connections-max-idle-ms: ${KAFKA_PRODUCER_CONNECTIONS_MAX_IDLE_MS:180000}
    topics:
      ctms-move-shipment-events:
        name: ${SHIPMENT_UPDATE_TOPIC_NAME_MOVE:ctms-move-shipment-events-staging}
        bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS_SHIPMENT:eh-cargoestms-staging-02.servicebus.windows.net:9093}
        username: ${EVENTHUB_USERNAME_SHIPMENT:$ConnectionString}
        password: ${SHIPMENT_CONNECTION_STRING:Endpoint=sb://eh-cargoestms-staging-02.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=pmLS7I0wb69KTkLEVM5WFTXwWcovt77St+AEhO61x68=}
      ctms-move-shipment-cancellation-ack-events:
        name: ${SHIPMENT_CANCELLATION_ACK_TOPIC_NAME_MOVE:ctms-move-shipment-cancellation-events-staging}
        bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS_SHIPMENT_CANCELLATION_ACK:eh-cargoestms-staging-04.servicebus.windows.net:9093}
        username: ${EVENTHUB_USERNAME_SHIPMENT_CANCELLATION_ACK:$ConnectionString}
        password: ${EVENTHUB_PASSWORD_SHIPMENT_CANCELLATION_ACK:Endpoint=sb://eh-cargoestms-staging-04.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=AHX+8PsHFrXRU6Yg9lX708WPIP4aVeRVO+AEhGAicGU=}
      ctms-move-task-events:
        name: ${TASK_UPDATE_TOPIC_NAME_MOVE:ctms-move-task-events-staging}
        bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS_TASK:eh-cargoestms-staging-04.servicebus.windows.net:9093}
        username: ${EVENTHUB_USERNAME_TASK:$ConnectionString}
        password: ${TASK_CONNECTION_STRING:Endpoint=sb://eh-cargoestms-staging-04.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=AHX+8PsHFrXRU6Yg9lX708WPIP4aVeRVO+AEhGAicGU=}
      ctms-move-trip-events:
        name: ${TRIP_UPDATE_TOPIC_NAME_MOVE:ctms-move-trip-events-staging}
        bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS_TRIP:eh-cargoestms-staging-04.servicebus.windows.net:9093}
        username: ${EVENTHUB_USERNAME_TRIP:$ConnectionString}
        password: ${TRIP_CONNECTION_STRING:Endpoint=sb://eh-cargoestms-staging-04.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=AHX+8PsHFrXRU6Yg9lX708WPIP4aVeRVO+AEhGAicGU=}

asb-config:
  connect: ${ASB_DOCUMENT_CONNECT:true}
  topic:
    name: ${ASB_DOCUMENT_TOPIC:doc-ser-staging}
  subscription:
    name: ${ASB_DOCUMENT_SUBSCRIPTION:ctms-move}
  connectionString: ${ASB_DOCUMENT_CONNECTION:Endpoint=sb://document-service-staging.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=AyGogvd7+hINq1s0QsncZNrw9hmy1/0h5+ASbD+xS+4=}
crp:
  baseUrl: ${CRP_BASE_URL:dev-login.cargoes.com}
  clientSecret: ${CRP_SECRET:Fv5yqPWCerjjTXQ40mMHeuNkftdJLXx1}
  url: ${CRP_URL:api-accountsdev.cargoes.com}
  scheme: ${CRP_SCHEME:https}
  clientId: ${CRP_CLIENT_ID:tms}
  realms: ${CRP_REALMS:dtworld}
  certsPath: ${CRP_CERTS_PATH:/protocol/openid-connect/certs}
  accessTokenUri: ${crp.scheme}://${crp.baseUrl}/auth/realms/${crp.realms}/protocol/openid-connect/token
  userAuthorizationUri: ${crp.scheme}://${crp.baseUrl}/auth/realms/${crp.realms}/protocol/openid-connect/auth
  default:
    username: <EMAIL>
    password: P@ssw0rd

runner:
  url: ${RUNNER_API_BASE_URL:https://qa-runner.cargoes.com}
  apiKey: ${RUNNER_API_KEY:3f6fc9b1-23f9-45ae-ae6f-94d5c6fad857}
  tenant-id: ${RUNNER_TENANT_ID:-1}

resource:
  url: ${RESOURCE_SERVICE_URL:http://localhost:8888/resource}

documentService:
  apiKey: ${DOCUMENT_SERVICE_API_KEY:QQSUJ15z712IzGK3gR4tIo2kURtxnXHq1}
  clientId: ${DOCUMENT_SERVICE_CLIENT_ID:8SMQTAGCKOA33JHA1Y2}
  subsName: ${DOCUMENT_SERVICE_SUBS_NAME:move}
  clientSecret: ${DOCUMENT_SERVICE_CLIENT_SECRET:Gd43#*@Utc&BxO&Xk@q#*z1sKp$xhnD3gH6sDoEr}
  organizationId: ${DOCUMENT_SERVICE_ORGANIZATION_ID:12345}
  pages: ${DOCUMENT_PAGES:1}
  locale: ${DOCUMENT_LOCALE:en-US}
  x-api-version: ${DOCUMENT_API_VERSION:v3}
  write-count: ${DOCUMENT_WRITE_COUNT:100}

ctmsutils:
  security:
    enablePreauthorise : ${CTMSUTILS_SECURITY_ENABLE_PREAUTHORISE:false}

unleash:
  enabled: ${ENABLE_UNLEASH:false}
  appName: ${UNLEASH_APP_NAME:ctms-move}
  instanceId: ${UNLEASH_INSTANCE_ID:ctms-move-staging}
  apiUrl: ${UNLEASH_API_URL:http://unleash-staging-tms.cargoes.com/api/}
  apiKey: ${UNLEASH_API_KEY:default}
  environment: ${UNLEASH_ENVIRONMENT:development}

cargoes-flow:
  tracking-service-url: ${TRACKING_SERVICE_URL:https://fn-tmscargoesflow-staging.azurewebsites.net/api}
  tracking-service-path-variable: ${TRACKING_SERVICE_PATH_VARIABLE:6yrwah_l5x8rdtY_a9Dmzkm9YX5Qklwi2o-jUeJXJaJbAzFufz9y2Q==}