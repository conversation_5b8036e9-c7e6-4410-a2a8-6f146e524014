{"CFR": {"task": {"states": [{"state": "CREATED", "isInitial": "true"}, {"state": "COMPLETED", "isInitial": "false"}, {"state": "CLOSED", "isInitial": "false"}, {"state": "DISCARDED", "isInitial": "false"}], "transitions": [{"sourceState": "CREATED", "targetState": "COMPLETED", "event": "TASK_COMPLETED", "actionId": "", "guardId": ""}, {"sourceState": "COMPLETED", "targetState": "CLOSED", "event": "TASK_CLOSED", "actionId": "", "guardId": ""}, {"sourceState": "CREATED", "targetState": "CLOSED", "event": "TASK_CLOSED", "actionId": "", "guardId": ""}, {"sourceState": "CREATED", "targetState": "DISCARDED", "event": "TASK_DISCARDED", "actionId": "", "guardId": ""}]}, "shipment": {"states": [{"state": "ASSIGNED", "isInitial": "true"}, {"state": "ALLOCATED", "isInitial": "false"}, {"state": "IN_TRANSIT", "isInitial": "false"}, {"state": "DELIVERED", "isInitial": "false"}, {"state": "DELIVERED_WITH_EXCEPTION", "isInitial": "false"}, {"state": "CANCELLED", "isInitial": "false"}], "transitions": [{"sourceState": "ASSIGNED", "targetState": "ALLOCATED", "event": "RESOURCE_ALLOCATED", "actionId": "", "guardId": ""}, {"sourceState": "ALLOCATED", "targetState": "IN_TRANSIT", "event": "PICKED_UP", "actionId": "", "guardId": ""}, {"sourceState": "IN_TRANSIT", "targetState": "DELIVERED", "event": "DELIVERED", "actionId": "", "guardId": ""}, {"sourceState": "ASSIGNED", "targetState": "DELIVERED", "event": "DELIVERED", "actionId": "", "guardId": ""}, {"sourceState": "ALLOCATED", "targetState": "DELIVERED", "event": "DELIVERED", "actionId": "", "guardId": ""}, {"sourceState": "IN_TRANSIT", "targetState": "DELIVERED_WITH_EXCEPTION", "event": "DELIVERED_WITH_EXCEPTION", "actionId": "", "guardId": ""}, {"sourceState": "ASSIGNED", "targetState": "DELIVERED_WITH_EXCEPTION", "event": "DELIVERED_WITH_EXCEPTION", "actionId": "", "guardId": ""}, {"sourceState": "ALLOCATED", "targetState": "DELIVERED_WITH_EXCEPTION", "event": "DELIVERED_WITH_EXCEPTION", "actionId": "", "guardId": ""}, {"sourceState": "ASSIGNED", "targetState": "IN_TRANSIT", "event": "PICKED_UP", "actionId": "", "guardId": ""}, {"sourceState": "ASSIGNED", "targetState": "CANCELLED", "event": "CANCELLED", "actionId": "", "guardId": ""}, {"sourceState": "ALLOCATED", "targetState": "CANCELLED", "event": "CANCELLED", "actionId": "", "guardId": ""}]}, "trip": {"states": [{"state": "CREATED", "isInitial": "true"}, {"state": "IN_PROGRESS", "isInitial": "false"}, {"state": "COMPLETED", "isInitial": "false"}, {"state": "COMPLETED_WITH_EXCEPTIONS", "isInitial": "false"}, {"state": "CANCELLED", "isInitial": "false"}, {"state": "CLOSED", "isInitial": "false"}], "transitions": [{"sourceState": "CREATED", "targetState": "IN_PROGRESS", "event": "START_TRIP", "actionId": "", "guardId": ""}, {"sourceState": "IN_PROGRESS", "targetState": "COMPLETED", "event": "END_TRIP", "actionId": "", "guardId": ""}, {"sourceState": "IN_PROGRESS", "targetState": "COMPLETED_WITH_EXCEPTIONS", "event": "END_TRIP_WITH_EXCEPTION", "actionId": "", "guardId": ""}, {"sourceState": "COMPLETED", "targetState": "CLOSED", "event": "CLOSE_TRIP", "actionId": "", "guardId": ""}, {"sourceState": "COMPLETED_WITH_EXCEPTIONS", "targetState": "CLOSED", "event": "CLOSE_TRIP", "actionId": "", "guardId": ""}, {"sourceState": "CREATED", "targetState": "CANCELLED", "event": "CANCEL_TRIP", "actionId": "", "guardId": ""}]}, "transportOrder": {"states": [{"state": "ASSIGNED", "isInitial": "true"}, {"state": "ALLOCATED", "isInitial": "false"}, {"state": "IN_PROGRESS", "isInitial": "false"}, {"state": "EXECUTED", "isInitial": "false"}, {"state": "EXECUTED_WITH_EXCEPTIONS", "isInitial": "false"}, {"state": "CANCELLED", "isInitial": "false"}, {"state": "CLOSED", "isInitial": "false"}], "transitions": [{"sourceState": "ASSIGNED", "targetState": "IN_PROGRESS", "event": "START_TRANSPORT_ORDER_EXECUTION", "actionId": "", "guardId": ""}, {"sourceState": "ALLOCATED", "targetState": "IN_PROGRESS", "event": "START_TRANSPORT_ORDER_EXECUTION", "actionId": "", "guardId": ""}, {"sourceState": "ASSIGNED", "targetState": "ALLOCATED", "event": "RESOURCE_ALLOCATED", "actionId": "", "guardId": ""}, {"sourceState": "IN_PROGRESS", "targetState": "EXECUTED", "event": "TRANSPORT_ORDER_EXECUTION_COMPLETED", "actionId": "", "guardId": ""}, {"sourceState": "IN_PROGRESS", "targetState": "EXECUTED_WITH_EXCEPTIONS", "event": "TRANSPORT_ORDER_EXECUTION_COMPLETED_WITH_EXCEPTION", "actionId": "", "guardId": ""}, {"sourceState": "EXECUTED", "targetState": "CLOSED", "event": "CLOSE_TRANSPORT_ORDER", "actionId": "", "guardId": ""}, {"sourceState": "EXECUTED_WITH_EXCEPTIONS", "targetState": "CLOSED", "event": "CLOSE_TRANSPORT_ORDER", "actionId": "", "guardId": ""}, {"sourceState": "ASSIGNED", "targetState": "CANCELLED", "event": "CANCEL_TRANSPORT_ORDER", "actionId": "", "guardId": ""}]}}}