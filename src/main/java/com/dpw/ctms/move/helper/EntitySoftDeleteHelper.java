package com.dpw.ctms.move.helper;

import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.Stop;
import com.dpw.ctms.move.entity.StopTask;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.TrailerResource;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.entity.VehicleOperatorResource;
import com.dpw.ctms.move.entity.VehicleResource;
import com.dpw.ctms.move.enums.TransportOrderStatus;
import com.dpw.ctms.move.service.IShipmentService;
import com.dpw.ctms.move.service.IStopService;
import com.dpw.ctms.move.service.ITaskService;
import com.dpw.ctms.move.service.ITrailerService;
import com.dpw.ctms.move.service.ITripService;
import com.dpw.ctms.move.service.IVehicleOperatorService;
import com.dpw.ctms.move.service.IVehicleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.HashSet;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@Slf4j
public class EntitySoftDeleteHelper {
    private final ITripService tripService;
    private final IShipmentService shipmentService;
    private final ITaskService taskService;
    private final IStopService stopService;
    private final IVehicleService vehicleService;
    private final ITrailerService trailerService;
    private final IVehicleOperatorService vehicleOperatorService;
    @Transactional
    public void softDeleteEntity(TransportOrder transportOrder) {

        transportOrder.setStatus(TransportOrderStatus.DISCARDED);
        transportOrder.setDeletedAt(System.currentTimeMillis());

        Set<String> tripCodes = transportOrder.getTrips().stream()
                .map(Trip::getCode)
                .collect(Collectors.toSet());

        Set<Trip> existingTrips = tripService.getAllByCodes(tripCodes);
        tripService.discardTrips(existingTrips);

        Set<String> shipmentCodes = transportOrder.getShipments().stream()
                .map(Shipment::getCode)
                .collect(Collectors.toSet());
        Set<Shipment> existingShipments = new HashSet<>(shipmentService.getAllByCodes(shipmentCodes));
        shipmentService.discardShipments(existingShipments);

        Set<Stop> stops = transportOrder.getTrips().stream()
                .filter(Objects::nonNull)
                .flatMap(trip -> Optional.ofNullable(trip.getStops())
                        .stream()
                        .flatMap(Collection::stream))
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        stopService.discardStops(stops);

        Set<String> taskCodes = transportOrder.getTrips().stream()
                .flatMap(trip -> trip.getStops().stream())
                .filter(Objects::nonNull)
                .flatMap(stop -> stop.getStopTasks().stream())
                .filter(Objects::nonNull)
                .map(StopTask::getTask)
                .filter(Objects::nonNull)
                .map(Task::getCode)
                .collect(Collectors.toSet());

        Set<Task> existingTasks = taskService.getAllByCodes(taskCodes);
        taskService.discardTasks(existingTasks);

        Set<String> vehicleCodes = transportOrder.getTrips().stream()
                .map(trip -> trip.getVehicleResource().getCode())
                .collect(Collectors.toSet());

        Set<VehicleResource> existingVehicleResources = vehicleService.getAllByCodes(vehicleCodes);
        vehicleService.discardVehicleResources(existingVehicleResources);

        Set<String> trailerCodes = transportOrder.getTrips().stream()
                .filter(Objects::nonNull)
                .flatMap(trip -> Optional.ofNullable(trip.getTrailerResources())
                        .stream()
                        .flatMap(Collection::stream))
                .filter(Objects::nonNull)
                .map(TrailerResource::getCode)
                .collect(Collectors.toSet());

        Set<TrailerResource> existingTrailerResources = trailerService.getAllByCodes(trailerCodes);
        trailerService.discardTrailerResources(existingTrailerResources);

        Set<String> vehicleOperatorCodes = transportOrder.getTrips().stream()
                .filter(Objects::nonNull)
                .flatMap(trip -> Optional.ofNullable(trip.getVehicleOperatorResources())
                        .stream()
                        .flatMap(Collection::stream))
                .filter(Objects::nonNull)
                .map(VehicleOperatorResource::getCode)
                .collect(Collectors.toSet());

        Set<VehicleOperatorResource> existingVehicleOperatorResources = vehicleOperatorService.getAllByCodes(vehicleOperatorCodes);
        vehicleOperatorService.discardVehicleOperatorResources(existingVehicleOperatorResources);
    }
}
