package com.dpw.ctms.move.helper;

import com.dpw.ctms.move.dto.EntityTypeWithIdsDTO;
import com.dpw.ctms.move.dto.GetDocumentsDto;
import com.dpw.ctms.move.dto.document.DeliveryTaskDocumentDTO;
import com.dpw.ctms.move.entity.Document;
import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.enums.DocumentOperationType;
import com.dpw.ctms.move.enums.DocumentStatus;
import com.dpw.ctms.move.enums.DocumentType;
import com.dpw.ctms.move.mapper.DocumentMapper;
import com.dpw.ctms.move.request.documentEvent.PreSignedUrlEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class DocumentHelper {

    private final DocumentMapper documentMapper;

    public List<Document> filterActivePodUploadDocuments(List<Document> documents) {
        return documents.stream()
                .filter(document -> document.getStatus() == DocumentStatus.ACTIVE &&
                        document.getDocumentType() == DocumentType.POD &&
                        document.getDocumentOperationType() == DocumentOperationType.UPLOAD)
                .toList();
    }

    public List<Document> filterDiscardedPodUploadDocuments(List<Document> documents) {
        return documents.stream()
                .filter(document -> document.getStatus() == DocumentStatus.DISCARDED &&
                        document.getDocumentType() == DocumentType.POD &&
                        document.getDocumentOperationType() == DocumentOperationType.UPLOAD)
                .toList();
    }

    public List<EntityTypeWithIdsDTO> groupDocumentsByEntityType(List<Document> documents) {
        return documents.stream()
                .collect(Collectors.groupingBy(
                        Document::getEntityType,
                        Collectors.mapping(Document::getEntityId, Collectors.toList())
                ))
                .entrySet().stream()
                .map(entry -> EntityTypeWithIdsDTO.builder()
                        .entityType(entry.getKey())
                        .entityIds(new HashSet<>(entry.getValue()))
                        .build())
                .collect(Collectors.toList());
    }

    public <T> List<String> extractAsyncMappingUUID(T eventData) {
        if (eventData instanceof PreSignedUrlEvent) {
            return List.of(((PreSignedUrlEvent) eventData).getFileKey());
        } else if (eventData instanceof DeliveryTaskDocumentDTO) {
            return ((DeliveryTaskDocumentDTO) eventData).getAsyncMappingUUIDs();
        }
        throw new IllegalArgumentException("Unsupported event type: " + eventData.getClass());
    }

    public <T> void updateDocumentByType(Document document, T eventData) {
        if (eventData instanceof PreSignedUrlEvent) {
            documentMapper.updateDocument(document, (PreSignedUrlEvent) eventData);
        } else if (eventData instanceof DeliveryTaskDocumentDTO) {
            documentMapper.updateDocument(document, (DeliveryTaskDocumentDTO) eventData);
        }
    }

    public <T> Document createDocumentByType(T eventData, String asyncMappingUUID) {
        if (eventData instanceof PreSignedUrlEvent) {
            return documentMapper.createDocument((PreSignedUrlEvent) eventData);
        } else if (eventData instanceof DeliveryTaskDocumentDTO) {
            return documentMapper.createDocument((DeliveryTaskDocumentDTO) eventData, asyncMappingUUID);
        }
        throw new IllegalArgumentException("Unsupported event type: " + eventData.getClass());
    }

    public List<Document> discardDocuments(List<Document> documentsToDiscard) {
        if (!ObjectUtils.isEmpty(documentsToDiscard)) {
            documentsToDiscard.forEach(doc -> {
                doc.setStatus(DocumentStatus.DISCARDED);
                log.info("Marking document {} as discarded", doc.getAsyncMappingUUID());
            });
            return documentsToDiscard;
        }
        return List.of();
    }

    public List<Document> filterActivatedDocuments(List<Document> documents) {
        if (!ObjectUtils.isEmpty(documents)) {
            return documents.stream().filter(doc -> doc.getStatus() == DocumentStatus.ACTIVE).toList();
        }
        return List.of();
    }

    public <T> List<Document> processDocuments(T eventData, Map<String, Document> existingDocumentMap) {
        List<String> asyncMappingUUIDs = extractAsyncMappingUUID(eventData);
        
        return asyncMappingUUIDs.stream()
                .map(asyncMappingUUID -> {
                    Optional<Document> documentOptional = Optional.ofNullable(existingDocumentMap.get(asyncMappingUUID));
                    DocumentStatus status = documentOptional.map(Document::getStatus).orElse(null);
                    
                    switch (status) {
                        case ACTIVE:
                            log.info("Document with asyncMappingUUID: {} is already active. No action needed.", asyncMappingUUID);
                            return null;

                        case DISCARDED:
                            log.error("Document is Discarded for asyncMappingUUID: {}. Cannot update the status!", asyncMappingUUID);
                            return null;

                        case INACTIVE:
                            Document document = documentOptional.orElse(new Document());
                            log.info("Updating existing inactive document with asyncMappingUUID: {}", asyncMappingUUID);
                            updateDocumentByType(document, eventData);
                            if (!ObjectUtils.isEmpty(document) && StringUtils.isNotBlank(document.getFileIdentifier()) && StringUtils.isNotBlank(document.getEntityId())) {
                                document.setStatus(DocumentStatus.ACTIVE);
                                log.info("Document with asyncMappingUUID: {} is now active.", asyncMappingUUID);
                                return document;
                            } else {
                                log.warn("Document with asyncMappingUUID: {} cannot be activated - missing fileIdentifier or entityId", asyncMappingUUID);
                                return null;
                            }

                        case null:
                        default:
                            log.info("Creating new document for asyncMappingUUID: {}", asyncMappingUUID);
                            return createDocumentByType(eventData, asyncMappingUUID);
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public List<GetDocumentsDto> buildActivePodDocumentFilters(List<Document> discardedDocuments) {
        if (discardedDocuments.isEmpty()) {
            return List.of();
        }
        
        // Group documents by entity type and collect all entity IDs
        Map<String, List<String>> entityTypeToIds = discardedDocuments.stream()
                .collect(Collectors.groupingBy(
                        Document::getEntityType,
                        Collectors.mapping(Document::getEntityId, Collectors.toList())
                ));
        
        // Create one filter per entity type with all its entity IDs
        return entityTypeToIds.entrySet().stream()
                .map(entry -> GetDocumentsDto.builder()
                        .entityIds(entry.getValue().stream().distinct().collect(Collectors.toList()))
                        .entityType(entry.getKey())
                        .documentType(DocumentType.POD)
                        .operationType(DocumentOperationType.UPLOAD)
                        .status(DocumentStatus.ACTIVE)
                        .build())
                .collect(Collectors.toList());
    }
    
    public List<Document> getDocumentsWithNoActiveEntityDocuments(
            List<Document> discardedDocuments,
            List<Document> activeDocuments,
            DocumentType documentType) {
        
        if (discardedDocuments.isEmpty()) {
            return List.of();
        }
        
        // Filter discarded documents by document type
        List<Document> discardedDocsOfType = discardedDocuments.stream()
                .filter(doc -> doc.getDocumentType() == documentType)
                .toList();
        
        if (discardedDocsOfType.isEmpty()) {
            return List.of();
        }
        
        // Group active documents by entityType -> Set of entityIds (only for the specific document type)
        Map<String, Set<String>> activeEntitiesByType = activeDocuments.stream()
                .filter(doc -> doc.getDocumentType() == documentType)
                .collect(Collectors.groupingBy(
                        Document::getEntityType,
                        Collectors.mapping(Document::getEntityId, Collectors.toSet())
                ));
        
        // Filter out entities that have active documents of this type
        List<Document> documentsToProcess = discardedDocsOfType.stream()
                .filter(doc -> {
                    Set<String> activeEntityIds = activeEntitiesByType.get(doc.getEntityType());
                    boolean hasActiveDocuments = activeEntityIds != null && activeEntityIds.contains(doc.getEntityId());
                    
                    if (hasActiveDocuments) {
                        log.info("Entity {}:{} still has active {} documents, skipping {} status update", 
                                doc.getEntityType(), doc.getEntityId(), documentType, documentType);
                    }
                    
                    return !hasActiveDocuments;
                })
                .toList();
        
        return documentsToProcess;
    }
}