package com.dpw.ctms.move.helper;

import com.dpw.ctms.move.dto.producer.EventRequestDTO;
import com.dpw.ctms.move.dto.producer.ShipmentCancellationEventRequestDTO;
import com.dpw.ctms.move.enums.StateMachineEntityType;
import com.dpw.ctms.move.service.event.IEntityEventManager;
import com.dpw.ctms.move.service.event.IEntityCancellationEventManager;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor
public class EventTriggerHelper {
    private final IEntityEventManager entityEventManager;
    private final IEntityCancellationEventManager shipmentCancellationEventManager;
    public <T> void triggerStatusChangeEvent(T initialState, T finalState, StateMachineEntityType stateMachineEntityType) {
        EventRequestDTO<T> eventRequestDTO = EventRequestDTO.<T>builder()
                .entityType(stateMachineEntityType.name())
                .originalEntity(initialState)
                .updatedEntity(finalState).build();
        entityEventManager.updateStatus(eventRequestDTO);
    }

    public void triggerCancellationEvent(List<String> shipmentCodes, boolean isCancelled, String correlationId) {
        ShipmentCancellationEventRequestDTO shipmentCancellationEventRequestDTO = ShipmentCancellationEventRequestDTO
                .builder()
                .shipmentCodes(shipmentCodes)
                .isCancelled(isCancelled)
                .entityType(StateMachineEntityType.SHIPMENT.name())
                .updatedAt(System.currentTimeMillis())
                .correlationId(correlationId)
                .build();
        shipmentCancellationEventManager.acknowledgeEntityCancellation(shipmentCancellationEventRequestDTO);
    }
}
