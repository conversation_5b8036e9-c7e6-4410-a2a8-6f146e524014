package com.dpw.ctms.move.helper;


import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.integration.adapter.TrackingServiceAdapter;
import com.dpw.ctms.move.integration.dto.oms.OmsConsignmentDto;
import com.dpw.ctms.move.integration.dto.resource.ResourceFacilitiesDto;
import com.dpw.ctms.move.integration.dto.trackingservice.TripTrackingDTO;
import com.dpw.ctms.move.integration.mapper.trackingservice.TrackingServiceRequestMapper;
import com.dpw.ctms.move.integration.request.trackingservice.TripTrackingRequest;
import com.dpw.ctms.move.integration.response.trackingservice.TripTrackingResponse;
import com.dpw.ctms.move.integration.service.IOmsIntegratorService;
import com.dpw.ctms.move.integration.service.IResourceIntegratorService;
import com.dpw.ctms.move.service.ITripService;
import com.dpw.ctms.move.validator.TripValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


import java.util.List;

import static com.dpw.ctms.move.constants.TrackingServiceConstants.CLOSE_TRIP;
import static com.dpw.ctms.move.constants.TrackingServiceConstants.START_TRIP;

@Component
@RequiredArgsConstructor
@Slf4j
public class TrackingServiceIntegrationHelper {
    private final TrackingServiceAdapter trackingServiceAdapter;
    private final TrackingServiceRequestMapper trackingServiceRequestMapper;
    private final TripValidator tripValidator;
    private final ITripService tripService;
    private final IOmsIntegratorService omsIntegratorService;
    private final IResourceIntegratorService resourceIntegratorService;

    public void sendTripTrackingEvent(TripTrackingDTO tripTrackingDTO) {
        Trip currentTrip =  tripService.findTripById(tripTrackingDTO.getInitialTrip().getId());
        if (tripValidator.validateTripStart(tripTrackingDTO.getInitialTrip(), currentTrip)) {
            tripTrackingDTO.setCurrentTrip(currentTrip);
            tripTrackingDTO.setEventType(START_TRIP);
            sendTripTrackingEventInternal(tripTrackingDTO);
        }

        else if (tripValidator.validateTripEnd(tripTrackingDTO.getInitialTrip(), currentTrip)) {
            tripTrackingDTO.setCurrentTrip(currentTrip);
            tripTrackingDTO.setEventType(CLOSE_TRIP);
            sendTripTrackingEventInternal(tripTrackingDTO);
        }
    }
    private void sendTripTrackingEventInternal(TripTrackingDTO tripTrackingDTO) {

        setExternalCustomerOrderReferenceNumbers(tripTrackingDTO);
        setFacilityDetails(tripTrackingDTO);
        TripTrackingRequest tripTrackingRequest = trackingServiceRequestMapper.mapTripTrackingRequest(
                tripTrackingDTO);

        try {
            TripTrackingResponse response = trackingServiceAdapter.trackTrip(tripTrackingRequest);
            log.info("Trip tracking details successfully sent {}", response);
        } catch (Exception e) {
            log.error("Error occurred in trip tracking call:  {}", e.getMessage());
        }
    }
    private void setExternalCustomerOrderReferenceNumbers(TripTrackingDTO tripTrackingDTO) {
        List<String> externalConsignmentIds = tripTrackingDTO.getCurrentTrip().getShipments().stream().map(
                        Shipment::getExternalConsignmentId)
                .toList();

        List<OmsConsignmentDto> consignmentDetailsDTOs = omsIntegratorService.getOmsConsignmentDtos(externalConsignmentIds);
        List<String> customerOrderNumbers = consignmentDetailsDTOs.stream()
                        .map(consignmentDetailsDTO -> consignmentDetailsDTO.getCustomerOrderMetadata().getCustomerOrderNumber())
                        .toList();
        tripTrackingDTO.setExternalCustomerOrderReferenceNumbers(customerOrderNumbers);
    }
    private void setFacilityDetails(TripTrackingDTO tripTrackingDTO) {
        String tripOriginLocationCode = tripTrackingDTO.getCurrentTrip().getExternalOriginLocationCode();
        String tripDestinationLocationCode = tripTrackingDTO.getCurrentTrip().getExternalDestinationLocationCode();
        List<ResourceFacilitiesDto> facilityDetailsDTOs = resourceIntegratorService.getFacilitiesDTOs(List.of(tripOriginLocationCode,
                tripDestinationLocationCode));
        tripTrackingDTO.setFacilityDetails(facilityDetailsDTOs);
    }
}
