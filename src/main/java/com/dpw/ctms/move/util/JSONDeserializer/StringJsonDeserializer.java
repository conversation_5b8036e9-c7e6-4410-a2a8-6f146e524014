package com.dpw.ctms.move.util.JSONDeserializer;

import com.dpw.tmsutils.exception.TMSException;
import com.dpw.tmsutils.utils.ObjectMapperUtil;
import com.dpw.tmsutils.utils.TMSExceptionErrorCode;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;

public class StringJsonDeserializer<T> extends JsonDeserializer<T> {
    private static final ObjectMapper mapper = ObjectMapperUtil.getObjectMapper();
    private final Class<T> clazz;

    public StringJsonDeserializer(Class<T> clazz) {
        this.clazz = clazz;
    }

    @Override
    public T deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        JsonToken currentToken = p.getCurrentToken();
        if (currentToken == JsonToken.VALUE_STRING) {
            String jsonString = p.getValueAsString();
            if (jsonString == null) {
                return null;
            }
            return mapper.readValue(jsonString, clazz);
        } else if (currentToken == JsonToken.START_OBJECT) {
            return mapper.readValue(p, clazz);
        } else if (currentToken == JsonToken.VALUE_NULL) {
            return null;
        } else {
            throw new TMSException(TMSExceptionErrorCode.INVALID_REQUEST.name(),
                    "Unable to deserialize " + currentToken);
        }
    }
}
