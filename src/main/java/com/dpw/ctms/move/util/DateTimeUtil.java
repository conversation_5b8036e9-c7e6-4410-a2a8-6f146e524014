package com.dpw.ctms.move.util;

import com.dpw.ctms.move.config.ConfigService;
import com.dpw.ctms.move.constants.ConfigConstants;
import com.dpw.ctms.move.dto.DateTimeDTO;
import com.dpw.ctms.move.enums.Tenant;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.DateTimeException;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

import static com.dpw.ctms.move.constants.PropertyConstants.DATE_FORMAT;
import static com.dpw.ctms.move.constants.PropertyConstants.DATETIME_FORMAT;

@Slf4j
@Component
@RequiredArgsConstructor
public class DateTimeUtil {
    
    private final ConfigService configService;
    private static final Tenant DEFAULT_TENANT = Tenant.CFR;

    public DateTimeDTO fromEpochMillis(Long epochMillis, String timeZoneId) {
        if (epochMillis == null) return null;

        String formattedDate = formatDateTime(epochMillis, timeZoneId, DateTimeFormatter.ofPattern(DATETIME_FORMAT)) + " " + timeZoneId;

        return DateTimeDTO.builder()
                .epoch(epochMillis)
                .iso(formattedDate)
                .timeZoneId(timeZoneId)
                .build();
    }

    public DateTimeDTO fromEpochMillisToDate(Long epochMillis, String timeZoneId) {
        if (epochMillis == null) return null;

        String formattedDate = formatDateTime(epochMillis, timeZoneId, DateTimeFormatter.ofPattern(DATE_FORMAT)) + " " + timeZoneId;


        return DateTimeDTO.builder()
                .epoch(epochMillis)
                .iso(formattedDate)
                .timeZoneId(timeZoneId)
                .build();
    }

    public String isoOffsetDateTimeString(Long epochMillis, String timeZoneId) {
        if (epochMillis == null) return null;
        ZonedDateTime zonedDateTime = toZonedDateTime(epochMillis, timeZoneId);
        return zonedDateTime.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME);
    }

    /**
     * Resolves timezone ID to ZoneId with comprehensive fallback strategy including configuration-based mapping
     */
    private ZoneId resolveTimeZone(String timeZoneId) {
        if (StringUtils.isEmpty(timeZoneId)) {
            return ZoneOffset.UTC;
        }
        
        return resolveModernTimeZone(timeZoneId)
                .orElseGet(() -> resolveConfigMappedTimeZone(timeZoneId)
                        .orElseGet(() -> fallbackToUtc(timeZoneId)));
    }
    
    /**
     * Attempts to resolve using modern ZoneId (full timezone names like America/New_York)
     */
    private Optional<ZoneId> resolveModernTimeZone(String timeZoneId) {
        try {
            return Optional.of(ZoneId.of(timeZoneId));
        } catch (DateTimeException e) {
            log.debug("Modern timezone resolution failed for: {}", timeZoneId);
            return Optional.empty();
        }
    }
    
    /**
     * Attempts to resolve using configuration-based timezone mapping (timezone code to offset to timezone)
     */
    private Optional<ZoneId> resolveConfigMappedTimeZone(String timeZoneId) {
        try {
            // First try to get offset from timezone code
            JsonNode timezoneCodeConfig = configService.getConfig(ConfigConstants.TIMEZONE_CODE_TO_OFFSET, DEFAULT_TENANT);
            if (timezoneCodeConfig != null && timezoneCodeConfig.has(timeZoneId)) {
                String offset = timezoneCodeConfig.get(timeZoneId).asText();
                log.debug("Mapped timezone code {} to offset {}", timeZoneId, offset);
                
                // Then try to get full timezone name from offset
                JsonNode offsetConfig = configService.getConfig(ConfigConstants.OFFSET_TO_TIMEZONE_MAP, DEFAULT_TENANT);
                if (offsetConfig != null && offsetConfig.has(offset)) {
                    String fullTimezoneName = offsetConfig.get(offset).asText();
                    log.debug("Mapped offset {} to timezone {}", offset, fullTimezoneName);
                    
                    // Try to create ZoneId from full timezone name
                    return Optional.of(ZoneId.of(fullTimezoneName));
                }
            }
            
            return Optional.empty();
        } catch (Exception e) {
            log.debug("Configuration-based timezone resolution failed for: {}", timeZoneId, e);
            return Optional.empty();
        }
    }
    
    /**
     * Final fallback to UTC with warning
     */
    private ZoneId fallbackToUtc(String timeZoneId) {
        log.warn("Unable to resolve timezone: {}, falling back to UTC", timeZoneId);
        return ZoneOffset.UTC;
    }

    private String formatDateTime(Long epochMillis, String timeZoneId, DateTimeFormatter formatter) {
        if (epochMillis == null) return null;
        return toZonedDateTime(epochMillis, timeZoneId).format(formatter);
    }

    private ZonedDateTime toZonedDateTime(Long epochMillis, String timeZoneId) {
        ZoneId zoneId = resolveTimeZone(timeZoneId);
        return Instant.ofEpochMilli(epochMillis).atZone(zoneId);
    }
}