package com.dpw.ctms.move.util;

import com.dpw.ctms.move.entity.Document;
import com.dpw.ctms.move.enums.DocumentStatus;

public class DocumentUtil {

    public static boolean isDocumentActive(Document document) {
        return document != null && document.getStatus() == DocumentStatus.ACTIVE;
    }

    public static boolean isDocumentInactive(Document document) {
        return document != null && document.getStatus() == DocumentStatus.INACTIVE;
    }

    public static boolean isDocumentDiscarded(Document document) {
        return document != null && document.getStatus() == DocumentStatus.DISCARDED;
    }
}