package com.dpw.ctms.move.mapper;

import com.dpw.ctms.move.dto.taskmanager.TaskRegistrationDTO;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.Time;
import com.dpw.ctms.move.integration.request.taskmanager.TaskInstanceRegistrationRequest;
import org.mapstruct.*;

import java.util.List;

import static com.dpw.ctms.move.mapper.TimeMapper.toTimeDTO;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
        unmappedTargetPolicy = ReportingPolicy.WARN,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface TaskInstanceRegistrationRequestMapper {

    default TaskInstanceRegistrationRequest toTaskInstanceRegisterRequest(
            TaskRegistrationDTO taskRegistrationDTO) {
        TaskInstanceRegistrationRequest request = new TaskInstanceRegistrationRequest();
        request.setTasks(toTaskInstanceRegisterDetailsRequestList(taskRegistrationDTO.getTasks()));
        return request;
    }

    List<TaskInstanceRegistrationRequest.TaskInstanceRegisterDetailsRequest> toTaskInstanceRegisterDetailsRequestList(
            List<TaskRegistrationDTO.TaskInstanceRegisterDetailsDTO> tasks);

    @Mappings({
            @Mapping(target = "extTaskTransactionCode", source = "extTaskTransactionCode"),
            @Mapping(target = "extTaskMasterCode", source = "extTaskMasterCode"),
            @Mapping(target = "expectedDateRange", expression = "java(toExpectedDateRange(task.getExpectedDateRange()))"),
            @Mapping(target = "roleAccessRequest", source = "roleAccessRequest")
    })
    TaskInstanceRegistrationRequest.TaskInstanceRegisterDetailsRequest toTaskInstanceRegisterDetailsRequest(
            TaskRegistrationDTO.TaskInstanceRegisterDetailsDTO task);

    @Mappings({
            @Mapping(target = "start", source = "start"),
            @Mapping(target = "end", source = "end")
    })
    TaskInstanceRegistrationRequest.ExpectedDateRange toExpectedDateRange(
            TaskRegistrationDTO.ExpectedDateRange task);

    // Map RoleAccessDTO to RoleAccessRequest
    @Mappings({
            @Mapping(target = "roleAccesses", source = "roleAccesses")
    })
    TaskInstanceRegistrationRequest.RoleAccessRequest toRoleAccessRequest(
            TaskRegistrationDTO.RoleAccessDTO roleAccessDTO);

    // Map individual RoleAccess
    @Mappings({
            @Mapping(target = "role", source = "role"),
            @Mapping(target = "accessTo", source = "accessTo"),
            @Mapping(target = "accessType", source = "accessType")
    })
    TaskInstanceRegistrationRequest.RoleAccess toRoleAccess(
            TaskRegistrationDTO.RoleAccess roleAccess);
    @Mappings({
            @Mapping(target = "extTaskTransactionCode", source = "code"),
            @Mapping(target = "extTaskMasterCode", source = "externalTaskMasterCode"),
            @Mapping(target = "expectedDateRange", expression = "java(toExpectedDateRange(task.getExpectedStartAt(), task.getExpectedEndAt()))"),
//            @Mapping(target = "tenantServiceCode", constant = TaskServiceConstants.DEFAULT_TENANT_SERVICE_CODE)
    })
    TaskRegistrationDTO.TaskInstanceRegisterDetailsDTO toTaskInstanceRegisterDetailsDTO(
            Task task);

//    default TaskInstanceRegistrationRequest.ExpectedDateRange toExpectedDateRange(Long start, Long end) {
//        if (start == null && end == null) return null;
//        TaskInstanceRegistrationRequest.ExpectedDateRange range = new TaskInstanceRegistrationRequest.ExpectedDateRange();
//        range.setStart(start != null ? start : 0L);
//        range.setEnd(end != null ? end : 0L);
//        return range;
//    }
default TaskRegistrationDTO.ExpectedDateRange toExpectedDateRange(Time start, Time end) {
    if (start == null && end == null) return null;

    return TaskRegistrationDTO.ExpectedDateRange.builder()
            .start(toTimeDTO(start))
            .end(toTimeDTO(end))
            .build();
    }
}
