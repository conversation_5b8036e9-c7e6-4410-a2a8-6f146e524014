package com.dpw.ctms.move.mapper;

import com.dpw.ctms.move.dto.TimeDTO;
import com.dpw.ctms.move.entity.*;
import com.dpw.ctms.move.enums.AssignmentType;
import com.dpw.ctms.move.enums.DisplayableStatusEnum;
import com.dpw.ctms.move.enums.ResourceAssignmentType;
import com.dpw.ctms.move.response.EnumLabelValueResponse;
import com.dpw.ctms.move.response.TripViewResponse;
import org.apache.commons.lang3.ObjectUtils;
import org.mapstruct.*;

import static com.dpw.ctms.move.mapper.TimeMapper.toTimeDTO;


@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface TripViewMapper {

    @Mapping(target = "code", source = "code")
    @Mapping(target = "status", source = "status", qualifiedByName = "mapEnumLabelResponse")
    @Mapping(target = "expectedStartAt", source = "expectedStartAt", qualifiedByName = "mapTimeToTimeDTO")
    @Mapping(target = "expectedEndAt", source = "expectedEndAt", qualifiedByName = "mapTimeToTimeDTO")
    @Mapping(target = "resourceDetails", source = ".")
    @Mapping(target = "originStop", expression = "java(mapStopByExternalLocationCode(trip, trip.getExternalOriginLocationCode()))")
    @Mapping(target = "destinationStop", expression = "java(mapStopByExternalLocationCode(trip, trip.getExternalDestinationLocationCode()))")
    @Mapping(target = "stops", source = "stops")
    @Mapping(target = "shipments", source = "shipments")
    @Mapping(target = "transportOrder", source = "transportOrder")
    TripViewResponse toResponse(Trip trip);

    @Mapping(target = "vehicleResource", expression = "java(toVehicleResource(trip.getVehicleResource(), trip.getTransportOrder()))")
    @Mapping(target = "trailerResources", source = "trailerResources")
    @Mapping(target = "vehicleOperatorResources", source = "vehicleOperatorResources")
    TripViewResponse.ResourceDetails toResourceDetails(Trip trip);

    @Mapping(target = "externalResourceId", source = "vehicleResource.externalResourceId")
    @Mapping(target = "resourceAssignmentType", expression = "java(mapResourceAssignmentType(vehicleResource, transportOrder))")
    TripViewResponse.ResourceDetails.VehicleResource toVehicleResource(VehicleResource vehicleResource, TransportOrder transportOrder);

    @Mapping(target = "externalResourceId", source = "externalResourceId")
    TripViewResponse.ResourceDetails.ExternalResource toTrailerResource(TrailerResource trailerResource);

    @Mapping(target = "externalResourceId", source = "externalResourceId")
    TripViewResponse.ResourceDetails.ExternalResource toVehicleOperatorResource(VehicleOperatorResource vehicleOperatorResource);

    @Mapping(target = "code", source = "code")
    @Mapping(target = "status", source = "status", qualifiedByName = "mapEnumLabelResponse")
    @Mapping(target = "expectedPickupAt", source = "expectedPickupAt", qualifiedByName = "mapTimeToTimeDTO")
    @Mapping(target = "expectedDeliveryAt", source = "expectedDeliveryAt", qualifiedByName = "mapTimeToTimeDTO")
    @Mapping(target = "actualPickupAt", source = "actualPickupAt", qualifiedByName = "mapTimeToTimeDTO")
    @Mapping(target = "actualDeliveryAt", source = "actualDeliveryAt", qualifiedByName = "mapTimeToTimeDTO")
    @Mapping(target = "externalConsignmentId", source = "externalConsignmentId")
    @Mapping(target = "externalCustomerOrderId", source = "externalCustomerOrderId")
    @Mapping(target = "volume", source = "volume")
    @Mapping(target = "volumeUom", source = "volumeUom")
    @Mapping(target = "weight", source = "weight")
    @Mapping(target = "weightUom", source = "weightUom")
    @Mapping(target = "originStopCode", source = "originStop.code")
    @Mapping(target = "destinationStopCode", source = "destinationStop.code")
    TripViewResponse.ShipmentDetails toShipmentDetails(Shipment shipment);

    @Mapping(target = "code", source = "code")
    @Mapping(target = "externalLocationCode", source = "externalLocationCode")
    TripViewResponse.StopDetails toStopDetails(Stop stop);

    @Mapping(target = "code", source = "code")
    @Mapping(target = "id", source = "id")
    @Mapping(target = "status", source = "status", qualifiedByName = "mapEnumLabelResponse")
    TripViewResponse.TransportOrderDetails toTransportOrderDetails(TransportOrder transportOrder);

    @Named("mapEnumLabelResponse")
    default EnumLabelValueResponse mapEnumLabelResponse(DisplayableStatusEnum statusEnum) {
        if (statusEnum == null) {
            return null;
        }
        return new EnumLabelValueResponse(statusEnum.getDisplayName(), statusEnum.name());
    }

    default TripViewResponse.StopDetails mapStopByExternalLocationCode(Trip trip, String externalLocationCode) {
        if (externalLocationCode == null || trip.getStops() == null) {
            return null;
        }
        
        return trip.getStops().stream()
                .filter(stop -> externalLocationCode.equals(stop.getExternalLocationCode()))
                .findFirst()
                .map(this::toStopDetails)
                .orElse(null);
    }

    @Named("mapTimeToTimeDTO")
    default TimeDTO mapTimeToTimeDTO(Time time) {
        return toTimeDTO(time);
    }

    default EnumLabelValueResponse mapResourceAssignmentType(VehicleResource vehicleResource, TransportOrder transportOrder) {
        if (ObjectUtils.isEmpty(vehicleResource) || ObjectUtils.isEmpty(vehicleResource.getResourceAssignmentDetails())
                || ObjectUtils.isEmpty(vehicleResource.getResourceAssignmentDetails().getResourceAssignmentType())) {
            // Use Transport Order's assignment type when vehicle resource assignment type is null
            if (ObjectUtils.isNotEmpty(transportOrder) && ObjectUtils.isNotEmpty(transportOrder.getAssignmentType())) {
                ResourceAssignmentType resourceAssignmentType = convertToResourceAssignmentType(transportOrder.getAssignmentType());
                return mapEnumLabelResponse(resourceAssignmentType);
            }
            // Default to null if no assignment type is available
            return null;
        }
        return mapEnumLabelResponse(vehicleResource.getResourceAssignmentDetails().getResourceAssignmentType());
    }

    default ResourceAssignmentType convertToResourceAssignmentType(AssignmentType assignmentType) {
        if (assignmentType == null) {
            return null;
        }
        return assignmentType == AssignmentType.EXTERNAL
                ? ResourceAssignmentType.VENDOR
                : ResourceAssignmentType.COST_CENTER;
    }

}
