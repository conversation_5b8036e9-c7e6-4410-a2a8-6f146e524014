package com.dpw.ctms.move.mapper;

import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.Stop;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.enums.DisplayableStatusEnum;
import com.dpw.ctms.move.response.EnumLabelValueResponse;
import com.dpw.ctms.move.response.ShipmentViewResponse;
import org.mapstruct.*;

import static com.dpw.ctms.move.mapper.TimeMapper.toTimeDTO;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface ShipmentViewMapper {

    @Mapping(target = "shipmentCode", source = "code")
    @Mapping(target = "status", source = "status", qualifiedByName = "mapStatusInfo")
    @Mapping(target = "originStop", source = "originStop", qualifiedByName = "mapStop")
    @Mapping(target = "destinationStop", source = "destinationStop", qualifiedByName = "mapStop")
    @Mapping(target = "customerOrder", source = ".", qualifiedByName = "mapCustomerOrder")
    @Mapping(target = "transportOrder", source = "transportOrder", qualifiedByName = "mapTransportOrder")
    @Mapping(target = "trip", source = "trip", qualifiedByName = "mapTrip")
    @Mapping(target = "expectedTimes", source = ".", qualifiedByName = "mapExpectedTimes")
    @Mapping(target = "actualTimes", source = ".", qualifiedByName = "mapActualTimes")
    @Mapping(target = "volume", source = "volume")
    @Mapping(target = "volumeUom", source = "volumeUom")
    @Mapping(target = "weight", source = "weight")
    @Mapping(target = "weightUom", source = "weightUom")
    ShipmentViewResponse toResponse(Shipment shipment);

    @Named("mapStatusInfo")
    default EnumLabelValueResponse mapStatusInfo(Enum<?> status) {
        if (status == null) {
            return null;
        }

        if (status instanceof DisplayableStatusEnum displayableStatus) {
            return new EnumLabelValueResponse(displayableStatus.getDisplayName(), status.name());
        }

        return new EnumLabelValueResponse(status.name(), status.name());
    }

    @Named("mapStop")
    default ShipmentViewResponse.Stop mapStop(Stop stop) {
        if (stop == null) {
            return null;
        }

        return ShipmentViewResponse.Stop.builder()
                .code(stop.getCode())
                .externalLocationCode(stop.getExternalLocationCode())
                .status(mapStatusInfo(stop.getStatus()))
                .build();
    }

    @Named("mapCustomerOrder")
    default ShipmentViewResponse.CustomerOrder mapCustomerOrder(Shipment shipment) {
        return ShipmentViewResponse.CustomerOrder.builder()
                .customerOrderId(shipment.getExternalCustomerOrderId())
                .consignmentId(shipment.getExternalConsignmentId())
                .build();
    }

    @Named("mapTransportOrder")
    default ShipmentViewResponse.TransportOrder mapTransportOrder(TransportOrder transportOrder) {
        if (transportOrder == null) {
            return null;
        }
        return ShipmentViewResponse.TransportOrder.builder()
                .transportOrderCode(transportOrder.getCode())
                .build();
    }

    @Named("mapTrip")
    default ShipmentViewResponse.Trip mapTrip(Trip trip) {
        if (trip == null) {
            return null;
        }
        return ShipmentViewResponse.Trip.builder()
                .tripCode(trip.getCode())
                .build();
    }

    @Named("mapExpectedTimes")
    default ShipmentViewResponse.TimeRange mapExpectedTimes(Shipment shipment) {
        return ShipmentViewResponse.TimeRange.builder()
                .startAt(toTimeDTO(shipment.getExpectedPickupAt()))
                .endAt(toTimeDTO(shipment.getExpectedDeliveryAt()))
                .build();
    }

    @Named("mapActualTimes")
    default ShipmentViewResponse.TimeRange mapActualTimes(Shipment shipment) {
        return ShipmentViewResponse.TimeRange.builder()
                .startAt(toTimeDTO(shipment.getActualPickupAt()))
                .endAt(toTimeDTO(shipment.getActualDeliveryAt()))
                .build();
    }
}
