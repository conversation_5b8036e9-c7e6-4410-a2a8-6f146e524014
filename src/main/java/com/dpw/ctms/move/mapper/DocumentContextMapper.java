package com.dpw.ctms.move.mapper;

import com.dpw.ctms.move.dto.ConsignmentDetailsDTO;
import com.dpw.ctms.move.dto.CustomerOrderMetaDataDTO;
import com.dpw.ctms.move.dto.DateTimeDTO;
import com.dpw.ctms.move.dto.FacilityDetailsDTO;
import com.dpw.ctms.move.dto.ProductDetailsDTO;
import com.dpw.ctms.move.dto.TimeDTO;
import com.dpw.ctms.move.integration.dto.oms.OmsConsignmentDto;
import com.dpw.ctms.move.integration.dto.resource.ResourceFacilitiesDto;
import com.dpw.ctms.move.integration.dto.resource.ResourceProductDetailsDto;
import com.dpw.ctms.move.integration.dto.resource.ResourceUomDto;
import com.dpw.ctms.move.util.DateTimeUtil;
import com.dpw.ctms.move.constants.ProductPropertyConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@Slf4j
public class DocumentContextMapper {

    private final DateTimeUtil dateTimeUtil;

    public ConsignmentDetailsDTO mapToConsignmentDetailsDTO(OmsConsignmentDto omsConsignmentDto, 
                                                           Map<String, ResourceProductDetailsDto> productMap,
                                                           Map<String, ResourceUomDto> uomMap) {
        if (ObjectUtils.isEmpty(omsConsignmentDto)) {
            return null;
        }

        return ConsignmentDetailsDTO.builder()
                .consignmentId(String.valueOf(omsConsignmentDto.getId()))
                .consignmentCode(omsConsignmentDto.getCode())
                .lineItemId(omsConsignmentDto.getLineItemId())
                .lineItemCode(omsConsignmentDto.getLineItemCode())
                .customerOrderId(omsConsignmentDto.getCustomerOrderId())
                .customerOrderCode(omsConsignmentDto.getCustomerOrderCode())
                .customerOrderMetaData(mapCustomerOrderMetaData(omsConsignmentDto.getCustomerOrderMetadata()))
                .specialInstructions(!ObjectUtils.isEmpty(omsConsignmentDto.getLineItemMetadata()) ? 
                        omsConsignmentDto.getLineItemMetadata().getSpecialInstructions() : null)
                .customerId(!ObjectUtils.isEmpty(omsConsignmentDto.getCustomer()) ? 
                        omsConsignmentDto.getCustomer().getId() : null)
                .productDetailsDTO(mapProductDetails(omsConsignmentDto, productMap, uomMap))
                .originFacilityId(!ObjectUtils.isEmpty(omsConsignmentDto.getOrigin()) ? 
                        omsConsignmentDto.getOrigin().getId() : null)
                .destinationFacilityId(!ObjectUtils.isEmpty(omsConsignmentDto.getDestination()) ? 
                        omsConsignmentDto.getDestination().getId() : null)
                .expectedPickupTime(mapTimeDTO(omsConsignmentDto.getExpectedPickupTime()))
                .expectedDeliveryTime(mapTimeDTO(omsConsignmentDto.getExpectedDeliveryTime()))
                .status(!ObjectUtils.isEmpty(omsConsignmentDto.getStatus()) ? 
                        omsConsignmentDto.getStatus().getValue() : null)
                .movementType(!ObjectUtils.isEmpty(omsConsignmentDto.getMovementType()) ? 
                        omsConsignmentDto.getMovementType().getValue() : null)
                .build();
    }

    private ProductDetailsDTO mapProductDetails(OmsConsignmentDto omsConsignmentDto, 
                                               Map<String, ResourceProductDetailsDto> productMap,
                                               Map<String, ResourceUomDto> uomMap) {

        if (ObjectUtils.isEmpty(omsConsignmentDto.getProducts()) || omsConsignmentDto.getProducts().isEmpty()) {
            return null;
        }

        // Get the first product from OMS
        OmsConsignmentDto.Product omsProduct = omsConsignmentDto.getProducts().get(0);
        if (ObjectUtils.isEmpty(omsProduct)) {
            return null;
        }

        // Get corresponding resource product details
        ResourceProductDetailsDto resourceProduct = productMap.get(String.valueOf(omsProduct.getResourceId()));

        // Get properties from OMS and enrich with UOM names from UOM service
        Map<String, ProductDetailsDTO.PropertyDetails> omsProperties = mapOmsProperties(omsProduct.getProperties(), uomMap);
        
        // Calculate and add volume if missing
        calculateAndAddVolumeIfMissing(omsProperties);

        // Build ProductDetailsDTO with OMS data and resource details if available
        ProductDetailsDTO.ProductDetailsDTOBuilder builder = ProductDetailsDTO.builder()
                .id(omsProduct.getId())
                .resourceId(omsProduct.getResourceId())
                .properties(omsProperties);

        if (!ObjectUtils.isEmpty(resourceProduct)) {
            builder.name(resourceProduct.getName())
                   .code(resourceProduct.getCode())
                   .unNumber(resourceProduct.getUnNumber())
                   .description(resourceProduct.getDescription())
                   .isContainerFillingRuleApplied(resourceProduct.getIsContainerFillingRuleApplied())
                   .filledPercentage(resourceProduct.getFilledPercentage())
                   .isAttentionNeeded(resourceProduct.getIsAttentionNeeded())
                   .productCategoryDetails(mapProductCategoryDetails(resourceProduct.getProductCategoryDetails()))
                   .status(mapStatusDetails(resourceProduct.getStatus()));
        }

        return builder.build();
    }

    private ProductDetailsDTO.ProductCategoryDetails mapProductCategoryDetails(
            ResourceProductDetailsDto.ProductCategoryDetails categoryDetails) {
        if (ObjectUtils.isEmpty(categoryDetails)) {
            return null;
        }
        return ProductDetailsDTO.ProductCategoryDetails.builder()
                .id(categoryDetails.getId())
                .code(categoryDetails.getCode())
                .name(categoryDetails.getName())
                .build();
    }

    private ProductDetailsDTO.StatusDetails mapStatusDetails(ResourceProductDetailsDto.StatusDetails status) {
        if (ObjectUtils.isEmpty(status)) {
            return null;
        }
        return ProductDetailsDTO.StatusDetails.builder()
                .label(status.getLabel())
                .value(status.getValue())
                .build();
    }

    private Map<String, ProductDetailsDTO.PropertyDetails> mapResourceProperties(
            List<ResourceProductDetailsDto.PropertyDetails> properties) {
        if (ObjectUtils.isEmpty(properties)) {
            return new HashMap<>();
        }

        return properties.stream()
                .filter(property -> !ObjectUtils.isEmpty(property.getPropertyNameValue()))
                .collect(Collectors.toMap(
                        ResourceProductDetailsDto.PropertyDetails::getPropertyNameValue,
                        this::mapResourceProperty,
                        (existing, replacement) -> existing
                ));
    }

    private ProductDetailsDTO.PropertyDetails mapResourceProperty(ResourceProductDetailsDto.PropertyDetails property) {
        return ProductDetailsDTO.PropertyDetails.builder()
                .name(property.getPropertyNameLabel())
                .value(!ObjectUtils.isEmpty(property.getPropertyValue()) ? 
                        Double.valueOf(property.getPropertyValue()) : null)
                .resourceUomId(property.getUomId())
                .resourceUomName(property.getUomName())
                .productCode(property.getProductCode())
                .build();
    }

    private Map<String, ProductDetailsDTO.PropertyDetails> mapOmsProperties(
            List<OmsConsignmentDto.Property> properties, Map<String, ResourceUomDto> uomMap) {
        if (ObjectUtils.isEmpty(properties)) {
            return new HashMap<>();
        }

        return properties.stream()
                .filter(property -> !ObjectUtils.isEmpty(property.getPropertyNameValue()))
                .collect(Collectors.toMap(
                        OmsConsignmentDto.Property::getPropertyNameValue,
                        property -> mapOmsProperty(property, uomMap),
                        (existing, replacement) -> existing
                ));
    }

    private ProductDetailsDTO.PropertyDetails mapOmsProperty(OmsConsignmentDto.Property property, Map<String, ResourceUomDto> uomMap) {
        ResourceUomDto uom = uomMap.get(String.valueOf(property.getUnitOfMeasurementId()));
        return ProductDetailsDTO.PropertyDetails.builder()
                .name(property.getPropertyNameLabel())
                .value(property.getPropertyValue())
                .resourceUomId(property.getUnitOfMeasurementId())
                .resourceUomName(!ObjectUtils.isEmpty(uom) ? uom.getName() : null)
                .build();
    }

    public FacilityDetailsDTO mapToFacilityDetailsDTO(ResourceFacilitiesDto resourceFacilitiesDto) {
        if (ObjectUtils.isEmpty(resourceFacilitiesDto)) {
            return null;
        }
        return FacilityDetailsDTO.builder()
                .id(resourceFacilitiesDto.getId())
                .code(resourceFacilitiesDto.getCode())
                .name(resourceFacilitiesDto.getName())
                .ownershipType(!ObjectUtils.isEmpty(resourceFacilitiesDto.getOwnershipType()) ? 
                        resourceFacilitiesDto.getOwnershipType().getValue() : null)
                .instructions(resourceFacilitiesDto.getInstructions())
                .pointOfContactDetails(mapPointOfContactDetails(resourceFacilitiesDto.getPointOfContactDetails()))
                .addressDetails(mapAddressDetails(resourceFacilitiesDto.getAddressDetails()))
                .build();
    }

    private FacilityDetailsDTO.PointOfContactDetails mapPointOfContactDetails(
            ResourceFacilitiesDto.PointOfContactDetails pocDetails) {
        if (ObjectUtils.isEmpty(pocDetails)) {
            return null;
        }
        return FacilityDetailsDTO.PointOfContactDetails.builder()
                .firstName(pocDetails.getFirstName())
                .lastName(pocDetails.getLastName())
                .emailId(pocDetails.getEmailId())
                .phoneNumber(pocDetails.getPhoneNumber())
                .countryCode(pocDetails.getCountryCode())
                .build();
    }

    private FacilityDetailsDTO.AddressDetails mapAddressDetails(
            ResourceFacilitiesDto.AddressDetails addressDetails) {
        if (ObjectUtils.isEmpty(addressDetails)) {
            return null;
        }
        return FacilityDetailsDTO.AddressDetails.builder()
                .addressLine(addressDetails.getAddressLine())
                .postalCode(addressDetails.getPostalCode())
                .suburbName(!ObjectUtils.isEmpty(addressDetails.getSuburbDetails()) ? 
                        addressDetails.getSuburbDetails().getName() : null)
                .cityName(!ObjectUtils.isEmpty(addressDetails.getCityDetails()) ? 
                        addressDetails.getCityDetails().getName() : null)
                .provinceName(!ObjectUtils.isEmpty(addressDetails.getProvinceDetails()) ? 
                        addressDetails.getProvinceDetails().getName() : null)
                .countryName(!ObjectUtils.isEmpty(addressDetails.getCountryDetails()) ? 
                        addressDetails.getCountryDetails().getName() : null)
                .build();
    }

    private CustomerOrderMetaDataDTO mapCustomerOrderMetaData(OmsConsignmentDto.CustomerOrderMetadata metadata) {
        if (ObjectUtils.isEmpty(metadata)) {
            return null;
        }
        return CustomerOrderMetaDataDTO.builder()
                .customerOrderNumber(metadata.getCustomerOrderNumber())
                .internalReferenceNumber(metadata.getInternalReferenceNumber())
                .build();
    }

    private DateTimeDTO mapTimeDTO(TimeDTO timeDTO) {
        if (ObjectUtils.isEmpty(timeDTO) || ObjectUtils.isEmpty(timeDTO.getEpoch())) {
            return null;
        }
        return dateTimeUtil.fromEpochMillis(timeDTO.getEpoch(), timeDTO.getTimezone());
    }

    private void calculateAndAddVolumeIfMissing(Map<String, ProductDetailsDTO.PropertyDetails> propertiesMap) {
        if (propertiesMap == null) {
            return;
        }

        // Check if VOLUME already exists
        if (propertiesMap.containsKey(ProductPropertyConstants.VOLUME)) {
            return;
        }

        // Check if all dimensions are present
        ProductDetailsDTO.PropertyDetails lengthProperty = propertiesMap.get(ProductPropertyConstants.LENGTH);
        ProductDetailsDTO.PropertyDetails breadthProperty = propertiesMap.get(ProductPropertyConstants.BREADTH);
        ProductDetailsDTO.PropertyDetails heightProperty = propertiesMap.get(ProductPropertyConstants.HEIGHT);

        if (lengthProperty != null && lengthProperty.getValue() != null &&
            breadthProperty != null && breadthProperty.getValue() != null &&
            heightProperty != null && heightProperty.getValue() != null) {
            
            // Calculate volume
            double volume = lengthProperty.getValue() * breadthProperty.getValue() * heightProperty.getValue();
            
            // Add volume property with the same UOM as length
            ProductDetailsDTO.PropertyDetails volumeProperty = ProductDetailsDTO.PropertyDetails.builder()
                    .name(ProductPropertyConstants.VOLUME)
                    .value(volume)
                    .resourceUomId(lengthProperty.getResourceUomId())
                    .resourceUomName(lengthProperty.getResourceUomName())
                    .build();
            
            propertiesMap.put(ProductPropertyConstants.VOLUME, volumeProperty);
        }
    }
}