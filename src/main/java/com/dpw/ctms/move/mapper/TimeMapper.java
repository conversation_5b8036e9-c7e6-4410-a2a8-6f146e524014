package com.dpw.ctms.move.mapper;

import com.dpw.ctms.move.dto.TimeDTO;
import com.dpw.ctms.move.entity.Time;
import org.apache.commons.lang3.ObjectUtils;

import static com.dpw.ctms.move.constants.PropertyConstants.DEFAULT_TIME_ZONE_ID;

public class TimeMapper {

    public static TimeDTO toTimeDTO(Time time) {
        if (ObjectUtils.isEmpty(time)) {return  null;}
        return TimeDTO.builder()
                .epoch(time.getEpoch())
                .timezone(time.getTimezone())
                .build();
    }

    public static Time toTime(TimeDTO timeDTO) {
        if (ObjectUtils.isEmpty(timeDTO)) {return null;}
        if (ObjectUtils.isEmpty(timeDTO.getTimezone()) && ObjectUtils.isNotEmpty(timeDTO.getEpoch()))
            timeDTO.setTimezone(DEFAULT_TIME_ZONE_ID);
        return new Time(timeDTO.getEpoch(), timeDTO.getTimezone());
    }
}
