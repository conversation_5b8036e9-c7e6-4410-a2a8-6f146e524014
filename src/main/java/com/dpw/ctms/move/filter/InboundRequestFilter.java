package com.dpw.ctms.move.filter;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.MDC;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.UUID;

import static com.dpw.ctms.move.constants.MoveConstants.*;
import static com.dpw.ctms.move.constants.RequestFilterConstants.COMPANY_REFERENCE_HEADER;
import static com.dpw.ctms.move.constants.RequestFilterConstants.CURRENT_CLIENT_TYPE_HEADER;
import static com.dpw.ctms.move.constants.RequestFilterConstants.CURRENT_LOCATION_HEADER;
import static com.dpw.ctms.move.constants.RequestFilterConstants.TRACE_ID_HEADER;
import static com.dpw.ctms.move.constants.RequestFilterConstants.TRANSACTION_ID_HEADER;
import static com.dpw.ctms.move.constants.RequestFilterConstants.USER_ID_HEADER;

@Component
@Order(1)
public class InboundRequestFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {

        HttpServletRequest httpRequest = (HttpServletRequest) request;

        String traceId = getOrGenerate(httpRequest.getHeader(TRACE_ID_HEADER));
        String transactionId = getOrGenerate(httpRequest.getHeader(TRANSACTION_ID_HEADER));
        String userId = httpRequest.getHeader(USER_ID_HEADER);
        String companyReference = httpRequest.getHeader(COMPANY_REFERENCE_HEADER);
        String currentLocation = httpRequest.getHeader(CURRENT_LOCATION_HEADER);
        String currentClientType = httpRequest.getHeader(CURRENT_CLIENT_TYPE_HEADER);

        MDC.put(TRACE_ID, traceId);
        MDC.put(TRANSACTION_ID, transactionId);
        putIfNotNull(USER_ID, userId);
        putIfNotNull(COMPANY_REFERENCE, companyReference);
        putIfNotNull(CURRENT_LOCATION, currentLocation);
        putIfNotNull(CURRENT_CLIENT_TYPE_HEADER, currentClientType);
        try {
            chain.doFilter(request, response);
        } finally {
            MDC.clear();
        }
    }

    private String getOrGenerate(String incomingId) {
        return (incomingId != null && !incomingId.isBlank())
                ? incomingId
                : UUID.randomUUID().toString();
    }

    private void putIfNotNull(String key, String value) {
        if (value != null) {
            MDC.put(key, value);
        }
    }
}

