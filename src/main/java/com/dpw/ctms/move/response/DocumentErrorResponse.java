package com.dpw.ctms.move.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentErrorResponse {
    private List<FileErrorDetails> failedDocumentDetails;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FileErrorDetails {
        private String error;
        private String asyncMappingUUID;
        private String fileIdentifier;
        private String fileName;
        private Integer fileSize;
        private String fileType;
    }
}