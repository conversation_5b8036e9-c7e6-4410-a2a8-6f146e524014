package com.dpw.ctms.move.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Response class for the exceptions/types API
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExceptionTypesResponse {

    private List<ExceptionTypeData> exceptions;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExceptionTypeData {
        private ExceptionTypeInfo type;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExceptionTypeInfo {
        private String label;
        private String value;
        private List<ExceptionSubtypeInfo> subtypes;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExceptionSubtypeInfo {
        private String label;
        private String value;
    }
}
