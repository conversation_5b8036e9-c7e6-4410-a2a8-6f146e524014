package com.dpw.ctms.move.response;

import com.dpw.ctms.move.entity.Time;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
@Builder
public class TripTasksDetailsResponse {
    private String code;
    private String externalTaskRegistrationCode;
    private String externalTaskMasterCode;
    private Integer sequence;
    private EnumLabelValueResponse status;
    private String externalConsignmentId;
    private String externalCustomerOrderId;
    private String shipmentCode;
    private Time expectedStartAt;
    private Time expectedEndAt;
    private Time actualStartAt;
    private Time actualEndAt;
    private Long updatedAt;
}
