package com.dpw.ctms.move.response;

import com.dpw.ctms.move.dto.TimeDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TripViewResponse {
    private String code;
    private EnumLabelValueResponse status;
    private TimeDTO expectedStartAt;
    private TimeDTO expectedEndAt;
    private ResourceDetails resourceDetails;
    StopDetails originStop;
    StopDetails destinationStop;
    private List<StopDetails> stops;
    private List<ShipmentDetails> shipments;
    private TransportOrderDetails transportOrder;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ShipmentDetails {
        private String code;
        private EnumLabelValueResponse status;
        private TimeDTO expectedPickupAt;
        private TimeDTO expectedDeliveryAt;
        private TimeDTO actualPickupAt;
        private TimeDTO actualDeliveryAt;
        private String externalConsignmentId;
        private String externalCustomerOrderId;
        private Double volume;
        private String volumeUom;
        private Double weight;
        private String weightUom;
        private String originStopCode;
        private String destinationStopCode;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ResourceDetails {
        private VehicleResource vehicleResource;
        private List<ExternalResource> trailerResources;
        private List<ExternalResource> vehicleOperatorResources;


        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class VehicleResource {
            private String externalResourceId;
            private EnumLabelValueResponse resourceAssignmentType;
        }

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class ExternalResource {
            private String externalResourceId;
        }

    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StopDetails {
        private String code;
        private String externalLocationCode;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TransportOrderDetails {
        private String code;
        private Long id;
        private EnumLabelValueResponse status;
    }


} 