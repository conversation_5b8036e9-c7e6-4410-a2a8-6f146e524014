package com.dpw.ctms.move.response;

import com.dpw.ctms.move.dto.TimeDTO;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ShipmentViewResponse {

    String shipmentCode;
    EnumLabelValueResponse status;
    Stop originStop;
    Stop destinationStop;
    CustomerOrder customerOrder;
    TransportOrder transportOrder;
    Trip trip;
    TimeRange expectedTimes;
    TimeRange actualTimes;
    BigDecimal volume;
    String volumeUom;
    BigDecimal weight;
    String weightUom;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Stop {
        private String code;
        private String externalLocationCode;
        private EnumLabelValueResponse status;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CustomerOrder {
        String customerOrderId;
        String consignmentId;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TransportOrder {
        String transportOrderCode;

    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Trip {
        String tripCode;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class TimeRange {
        private TimeDTO startAt;
        private TimeDTO endAt;
    }
}
