package com.dpw.ctms.move.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransportOrderDetailsResponse {
    private String createdBy;
    private Long createdAt;
    private EnumLabelValueResponse status;
    private List<TripDetails> trips;
    private AssignmentDetails assignmentDetails;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class TripDetails {
        private String tripCode;
    }
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class AssignmentDetails {
        private EnumLabelValueResponse assignmentType;
        private String assigneeIdentifier;
    }
}