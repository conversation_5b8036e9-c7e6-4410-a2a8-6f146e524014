package com.dpw.ctms.move.entity;


import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.databind.JsonNode;
import io.hypersistence.utils.hibernate.type.json.JsonBinaryType;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.Type;
import org.hibernate.envers.Audited;
import org.hibernate.type.SqlTypes;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "trailer_resource")
@EqualsAndHashCode(callSuper = true)
@Audited
public class TrailerResource extends BaseEntity {

    @Column(name = "code", unique = true)
    private String code;

    @Column(name = "external_resource_id")
    private String externalResourceId;

    @Type(JsonBinaryType.class)
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "resource_assignment_details", columnDefinition = "jsonb")
    private ResourceAssignmentDetails resourceAssignmentDetails;

    @Column(name = "deleted_at")
    private Long deletedAt;

    @Type(JsonBinaryType.class)
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "details", columnDefinition = "jsonb")
    private JsonNode details;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "trip_id")
    @ToString.Exclude
    @EqualsAndHashCode.Exclude
    @JsonBackReference("trip-trailer-resource")
    private Trip trip;
}
