package com.dpw.ctms.move.entity;

import com.dpw.ctms.move.enums.ExceptionType;
import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import com.fasterxml.jackson.databind.JsonNode;
import io.hypersistence.utils.hibernate.type.json.JsonBinaryType;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.Type;
import org.hibernate.envers.Audited;
import org.hibernate.type.SqlTypes;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "exception")
@EqualsAndHashCode(callSuper = true)
@Audited
public class Exception extends BaseEntity {

    @Column(name = "code", unique = true)
    private String code;

    @Column(name = "type")
    @Enumerated(EnumType.STRING)
    private ExceptionType type;

    @Column(name = "raised_by")
    private String raisedBy;

    @Column(name = "raised_at")
    private Long raisedAt;

    @Column(name = "external_task_definition_id")
    private String externalTaskDefinitionId;

    @Column(name = "external_task_instance_id")
    private String externalTaskInstanceId;

    @Type(JsonBinaryType.class)
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "details", columnDefinition = "jsonb")
    private JsonNode details;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "trip_id")
    @ToString.Exclude
    @EqualsAndHashCode.Exclude
    @JsonBackReference("trip-exception")
    private Trip trip;

    @OneToOne(mappedBy = "exception", cascade = CascadeType.ALL)
    @JsonManagedReference("exception-task")
    private ExceptionTaskMapping exceptionTaskMapping;
}
