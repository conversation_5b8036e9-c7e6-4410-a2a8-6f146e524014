package com.dpw.ctms.move.entity;

import com.fasterxml.jackson.annotation.JsonBackReference;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.envers.Audited;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "exception_task_mapping")
@EqualsAndHashCode(callSuper = true)
@Audited
public class ExceptionTaskMapping extends BaseEntity {

    @OneToOne
    @JoinColumn(name = "exception_id")
    @JsonBackReference("exception-task")
    private Exception exception;

    @ManyToOne
    @JoinColumn(name = "task_id")
    @JsonBackReference("task-exception")
    private Task task;
}