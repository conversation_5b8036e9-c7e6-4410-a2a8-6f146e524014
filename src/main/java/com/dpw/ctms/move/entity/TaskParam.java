package com.dpw.ctms.move.entity;

import com.dpw.ctms.move.dto.ParamValueBaseDTO;
import com.dpw.ctms.move.dto.ParamValueShipmentDTO;
import com.dpw.ctms.move.dto.ParamValueStopDTO;
import com.dpw.ctms.move.dto.ParamValueVehicleOperatorDTO;
import com.dpw.ctms.move.enums.TaskParamType;
import com.dpw.tmsutils.exception.TMSException;
import com.dpw.tmsutils.utils.ObjectMapperUtil;
import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.databind.JsonNode;
import io.hypersistence.utils.hibernate.type.json.JsonBinaryType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.Type;
import org.hibernate.envers.Audited;
import org.hibernate.type.SqlTypes;

import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.INVALID_DATA;

/**
 * TODO Have paramValue as code for any type of param (STOP, SHIPMENT etc and other details can go as part of details json
 **/
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "task_param")
@EqualsAndHashCode(callSuper = true)
@Audited
public class TaskParam extends BaseEntity{
    @Column(name = "param_name")
    @Enumerated(EnumType.STRING)
    private TaskParamType paramName;

    @Type(JsonBinaryType.class)
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "param_value", columnDefinition = "jsonb")
    private JsonNode paramValue;

    @Column(name = "deleted_at")
    private Long deletedAt;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "task_id", referencedColumnName = "id")
    @ToString.Exclude
    @EqualsAndHashCode.Exclude
    @JsonBackReference("task-param")
    private Task task;

    public ParamValueBaseDTO getParamValue() {
        return switch (paramName) {
            case SHIPMENT -> ObjectMapperUtil.getObjectMapper().convertValue(paramValue, ParamValueShipmentDTO.class);
            case STOP -> ObjectMapperUtil.getObjectMapper().convertValue(paramValue, ParamValueStopDTO.class);
            case VEHICLE_OPERATOR -> ObjectMapperUtil.getObjectMapper().convertValue(paramValue, ParamValueVehicleOperatorDTO.class);
            default -> throw new TMSException(INVALID_DATA.name(), "Invalid param name");
        };
    }
}
