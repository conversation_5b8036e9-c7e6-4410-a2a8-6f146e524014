package com.dpw.ctms.move.entity;

import com.dpw.ctms.move.enums.TaskStatus;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import com.fasterxml.jackson.databind.JsonNode;
import io.hypersistence.utils.hibernate.type.json.JsonBinaryType;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.Type;
import org.hibernate.envers.Audited;
import org.hibernate.type.SqlTypes;

import java.util.List;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "task")
@EqualsAndHashCode(callSuper = true)
@Audited
public class Task extends BaseEntity {

    @Column(name = "code", unique = true)
    private String code;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private TaskStatus status;

    @Column(name = "sequence")
    private Integer sequence;

    @AttributeOverrides({
            @AttributeOverride(name = "epoch", column = @Column(name = "expected_start_at")),
            @AttributeOverride(name = "timezone", column = @Column(name = "expected_start_at_timezone"))
    })
    private Time expectedStartAt;

    @AttributeOverrides({
            @AttributeOverride(name = "epoch", column = @Column(name = "expected_end_at")),
            @AttributeOverride(name = "timezone", column = @Column(name = "expected_end_at_timezone"))
    })
    private Time expectedEndAt;

    @AttributeOverrides({
            @AttributeOverride(name = "epoch", column = @Column(name = "actual_start_at")),
            @AttributeOverride(name = "timezone", column = @Column(name = "actual_start_at_timezone"))
    })
    private Time actualStartAt;

    @AttributeOverrides({
            @AttributeOverride(name = "epoch", column = @Column(name = "actual_end_at")),
            @AttributeOverride(name = "timezone", column = @Column(name = "actual_end_at_timezone"))
    })
    private Time actualEndAt;

    private String externalTaskRegistrationCode;

    @Column(name = "external_task_master_code")
    private String externalTaskMasterCode;

    @Column(name = "deleted_at")
    private Long deletedAt;

    @Column(name = "mandatory")
    private Boolean mandatory;

    @Type(JsonBinaryType.class)
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "details", columnDefinition = "jsonb")
    private JsonNode details;

    @OneToOne(mappedBy = "task")
    @JsonManagedReference("task-stop")
    private StopTask stopTask;

    @OneToOne(mappedBy = "task")
    @JsonManagedReference("task-shipment")
    private ShipmentTask shipmentTask;

    @OneToMany(mappedBy = "task", cascade = CascadeType.ALL)
    @JsonManagedReference("task-param")
    private List<TaskParam> taskParams;

    @OneToMany(mappedBy = "task", cascade = CascadeType.ALL)
    @JsonManagedReference("task-exception")
    private List<ExceptionTaskMapping> exceptionTaskMappings;
}