package com.dpw.ctms.move.entity;

import com.dpw.ctms.move.enums.DocumentType;
import com.dpw.ctms.move.enums.DocumentStatus;
import com.dpw.ctms.move.enums.DocumentOperationType;
import com.dpw.ctms.move.request.documentEvent.FileMetadata;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.hibernate.envers.Audited;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "document")
@EqualsAndHashCode(callSuper = true)
@Audited
public class Document extends BaseEntity{

    @Column(name = "entity_id")
    private String entityId;

    @Column(name = "entity_type")
    private String entityType;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private DocumentStatus status;

    @Column(name = "document_type")
    @Enumerated(EnumType.STRING)
    private DocumentType documentType;

    @Column(name = "document_operation_type")
    @Enumerated(EnumType.STRING)
    private DocumentOperationType documentOperationType = DocumentOperationType.UPLOAD;

    @Column(name = "client_identifier")
    private String clientIdentifier;

    @Column(name = "async_mapping_uuid")
    private String asyncMappingUUID;

    @Column(name = "file_identifier")
    private String fileIdentifier;

    @Column(name = "file_name")
    private String fileName;

    @Column(name = "file_size")
    private Integer fileSize;

    @Column(name = "file_type")
    private String fileType;

    @Column(name = "presigned_download_url")
    private String presignedDownloadUrl;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "file_metadata", columnDefinition = "jsonb")
    private FileMetadata fileMetadata;

    @Column(name = "checksum")
    private String checksum;

}
