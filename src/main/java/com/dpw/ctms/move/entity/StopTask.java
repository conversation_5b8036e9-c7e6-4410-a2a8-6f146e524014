package com.dpw.ctms.move.entity;

import com.dpw.ctms.move.enums.StopTaskEvent;
import com.fasterxml.jackson.annotation.JsonBackReference;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.envers.Audited;

import java.io.Serializable;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "stop_task")
@EqualsAndHashCode(callSuper = true)
@Audited
public class StopTask extends BaseEntity {
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "stop_id")
    @ToString.Exclude
    @EqualsAndHashCode.Exclude
    @JsonBackReference("stop-task")
    private Stop stop;

    @OneToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "task_id")
    @ToString.Exclude
    @EqualsAndHashCode.Exclude
    @JsonBackReference("task-stop")
    private Task task;

    @Column(name = "task_event")
    @Enumerated(EnumType.STRING)
    private StopTaskEvent taskEvent;
}
