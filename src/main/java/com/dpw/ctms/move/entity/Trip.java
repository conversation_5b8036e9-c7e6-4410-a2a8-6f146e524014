package com.dpw.ctms.move.entity;

import com.dpw.ctms.move.enums.TripStatus;
import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import com.fasterxml.jackson.databind.JsonNode;
import io.hypersistence.utils.hibernate.type.json.JsonBinaryType;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.Type;
import org.hibernate.envers.Audited;
import org.hibernate.type.SqlTypes;

import java.util.Set;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "trip")
@EqualsAndHashCode(callSuper = true)
@Audited
public class Trip extends BaseEntity {

    @Column(name = "code", unique = true)
    private String code;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private TripStatus status;

    @Column(name = "external_origin_location_code")
    private String externalOriginLocationCode;

    @Column(name = "external_destination_location_code")
    private String externalDestinationLocationCode;

    @AttributeOverrides({
            @AttributeOverride(name = "epoch", column = @Column(name = "expected_start_at")),
            @AttributeOverride(name = "timezone", column = @Column(name = "expected_start_at_timezone"))
    })
    private Time expectedStartAt;

    @AttributeOverrides({
            @AttributeOverride(name = "epoch", column = @Column(name = "expected_end_at")),
            @AttributeOverride(name = "timezone", column = @Column(name = "expected_end_at_timezone"))
    })
    private Time expectedEndAt;

    @AttributeOverrides({
            @AttributeOverride(name = "epoch", column = @Column(name = "actual_start_at")),
            @AttributeOverride(name = "timezone", column = @Column(name = "actual_start_at_timezone"))
    })
    private Time actualStartAt;

    @AttributeOverrides({
            @AttributeOverride(name = "epoch", column = @Column(name = "actual_end_at")),
            @AttributeOverride(name = "timezone", column = @Column(name = "actual_end_at_timezone"))
    })
    private Time actualEndAt;

    @Column(name = "deleted_at")
    private Long deletedAt;

    @Type(JsonBinaryType.class)
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "details", columnDefinition = "jsonb")
    private JsonNode details;

    @OneToMany(mappedBy = "trip", cascade = CascadeType.ALL)
    @JsonManagedReference("trip-shipment")
    private Set<Shipment> shipments;

    @OneToMany(mappedBy = "trip", cascade = CascadeType.ALL)
    @JsonManagedReference("trip-stop")
    private Set<Stop> stops;

    @OneToOne(mappedBy = "trip", cascade = CascadeType.ALL)
    @JsonManagedReference("trip-vehicle-resource")
    private VehicleResource vehicleResource;

    @OneToMany(mappedBy = "trip", cascade = CascadeType.ALL)
    @JsonManagedReference("trip-trailer-resource")
    private Set<TrailerResource> trailerResources;

    @OneToMany(mappedBy = "trip", cascade = CascadeType.ALL)
    @JsonManagedReference("trip-vehicle-operator-resource")
    private Set<VehicleOperatorResource> vehicleOperatorResources;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "transport_order_id")
    @ToString.Exclude
    @EqualsAndHashCode.Exclude
    @JsonBackReference("transport-order-trip")
    private TransportOrder transportOrder;

    @OneToMany(mappedBy = "trip", cascade = CascadeType.ALL)
    @JsonManagedReference("trip-exception")
    private Set<Exception> exceptions;
}