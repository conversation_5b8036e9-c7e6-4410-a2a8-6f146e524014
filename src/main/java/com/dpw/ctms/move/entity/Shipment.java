package com.dpw.ctms.move.entity;

import com.dpw.ctms.move.enums.ShipmentStatus;
import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import com.fasterxml.jackson.databind.JsonNode;
import io.hypersistence.utils.hibernate.type.json.JsonBinaryType;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.Type;
import org.hibernate.envers.Audited;
import org.hibernate.type.SqlTypes;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "shipment")
@EqualsAndHashCode(callSuper = true)
@Audited
public class Shipment extends BaseEntity {

    @Column(name = "code")
    private String code;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private ShipmentStatus status;

    @Column(name = "external_current_location_code")
    private String externalCurrentLocationCode;

    @AttributeOverrides({
            @AttributeOverride(name = "epoch", column = @Column(name = "expected_pickup_at")),
            @AttributeOverride(name = "timezone", column = @Column(name = "expected_pick_at_timezone"))
    })
    private Time expectedPickupAt;

    @AttributeOverrides({
            @AttributeOverride(name = "epoch", column = @Column(name = "expected_delivery_at")),
            @AttributeOverride(name = "timezone", column = @Column(name = "expected_delivery_at_timezone"))
    })
    private Time expectedDeliveryAt;

    @AttributeOverrides({
            @AttributeOverride(name = "epoch", column = @Column(name = "actual_pickup_at")),
            @AttributeOverride(name = "timezone", column = @Column(name = "actual_pick_at_timezone"))
    })
    private Time actualPickupAt;

    @AttributeOverrides({
            @AttributeOverride(name = "epoch", column = @Column(name = "actual_delivery_at")),
            @AttributeOverride(name = "timezone", column = @Column(name = "actual_delivery_at_timezone"))
    })
    private Time actualDeliveryAt;

    @Column(name = "external_consignment_id")
    private String externalConsignmentId;

    @Column(name = "external_customer_order_id")
    private String externalCustomerOrderId;

    @Column(name = "volume")
    private BigDecimal volume;

    @Column(name = "volume_uom")
    private String volumeUom;

    @Column(name = "weight")
    private BigDecimal weight;

    @Column(name = "weight_uom")
    private String weightUom;

    @Column(name = "deleted_at")
    private Long deletedAt;

    @Type(JsonBinaryType.class)
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "loading_details", columnDefinition = "jsonb")
    private JsonNode loadingDetails;

    @Type(JsonBinaryType.class)
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "unloading_details", columnDefinition = "jsonb")
    private JsonNode unloadingDetails;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "trip_id")
    @ToString.Exclude
    @EqualsAndHashCode.Exclude
    @JsonBackReference("trip-shipment")
    private Trip trip;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "transport_order_id")
    @ToString.Exclude
    @EqualsAndHashCode.Exclude
    @JsonBackReference("transport-order-shipment")
    private TransportOrder transportOrder;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "origin_stop_id")
    @ToString.Exclude
    @EqualsAndHashCode.Exclude
    @JsonBackReference("origin-stop-shipment")
    private Stop originStop;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "destination_stop_id")
    @ToString.Exclude
    @EqualsAndHashCode.Exclude
    @JsonBackReference("destination-stop-shipment")
    private Stop destinationStop;

    @OneToMany(mappedBy = "shipment", cascade = CascadeType.ALL)
    @Builder.Default
    @JsonManagedReference("shipment-task")
    private List<ShipmentTask> shipmentTasks = new ArrayList<>();

    @Column(name = "is_document_attached")
    @Builder.Default
    private Boolean isDocumentAttached = false;
}