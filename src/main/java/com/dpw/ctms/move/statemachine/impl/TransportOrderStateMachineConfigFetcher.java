package com.dpw.ctms.move.statemachine.impl;

import com.dpw.ctms.move.dto.StateTransitionHolderDTO;
import com.dpw.ctms.move.integration.service.IConfigServiceIntegrator;
import com.dpw.ctms.move.statemachine.IStateMachineConfigFetcher;
import com.dpw.tmsutils.exception.TMSException;
import com.dpw.tmsutils.utils.ObjectMapperUtil;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.dpw.ctms.move.constants.ConfigConstants.TO_STATE_MACHINE_CONFIG;
import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.INVALID_CONFIG;

@Service
@RequiredArgsConstructor
@Slf4j
public class TransportOrderStateMachineConfigFetcher implements IStateMachineConfigFetcher {

    private final IConfigServiceIntegrator configServiceIntegrator;

    @Override
    public StateTransitionHolderDTO fetchStateMachineConfig() {
        JsonNode stateTransitionHolderJsonNode = configServiceIntegrator.fetchConfig(TO_STATE_MACHINE_CONFIG);
        if (stateTransitionHolderJsonNode == null) {
            throw new TMSException(INVALID_CONFIG.name(), "Invalid transport order state machine config");
        }
        try {
            return ObjectMapperUtil.getObjectMapper().treeToValue(stateTransitionHolderJsonNode, StateTransitionHolderDTO.class);
        } catch (Exception e) {
            log.error("Error fetching transport order state machine config {}", e.getMessage());
            throw new TMSException(INVALID_CONFIG.name(), "Invalid transport order state machine config");
        }
    }
}
