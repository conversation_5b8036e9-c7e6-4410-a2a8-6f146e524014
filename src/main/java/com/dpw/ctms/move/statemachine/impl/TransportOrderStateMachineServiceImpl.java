package com.dpw.ctms.move.statemachine.impl;

import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.enums.StateMachineEntityType;
import com.dpw.ctms.move.enums.TransportOrderStatus;
import com.dpw.ctms.move.service.ITransportOrderService;
import org.springframework.stereotype.Service;

@Service
public class TransportOrderStateMachineServiceImpl extends AbstractStateMachineService<TransportOrder>{

    private final ITransportOrderService transportOrderService;

    protected TransportOrderStateMachineServiceImpl(StateMachineFactoryService stateMachineFactoryService,
                                              ITransportOrderService transportOrderService) {
        super(stateMachineFactoryService);
        this.transportOrderService = transportOrderService;
    }

    @Override
    protected StateMachineEntityType getStateMachineEntityType() {
        return StateMachineEntityType.TRANSPORT_ORDER;
    }

    @Override
    protected TransportOrder findEntityById(Long id) {
        return transportOrderService.findTransportOrderById(id);
    }

    @Override
    protected String extractState(TransportOrder entity) {
        return entity.getStatus().name();
    }

    @Override
    protected void updateState(TransportOrder entity, String newState) {
        entity.setStatus(TransportOrderStatus.valueOf(newState));
    }

    @Override
    protected void saveEntity(TransportOrder entity) {
        transportOrderService.saveTransportOrder(entity);
    }
}
