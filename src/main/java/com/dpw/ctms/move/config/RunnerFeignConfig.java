package com.dpw.ctms.move.config;

import feign.RequestInterceptor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;

import static com.dpw.ctms.move.constants.RunnerServiceConstants.Headers.*;

// This class is used only by RunnerClient - won't be scanned as @Configuration
public class RunnerFeignConfig {

    @Value("${runner.apiKey:default-test-key}")
    private String apiKey;
    
    @Value("${runner.tenant-id:default-test-tenant}")
    private String tenantId;

    // This @Bean is scoped to this configuration class only, not globally
    @Bean
    public RequestInterceptor runnerRequestInterceptor() {
        return requestTemplate -> {
            requestTemplate.header(ACCEPT, DEFAULT_ACCEPT);
            requestTemplate.header(CONTENT_TYPE, DEFAULT_CONTENT_TYPE);
            requestTemplate.header(SOURCE, DEFAULT_SOURCE);
            requestTemplate.header(X_API_KEY, apiKey);
            requestTemplate.header(TENANT_ID, tenantId);
        };
    }
}