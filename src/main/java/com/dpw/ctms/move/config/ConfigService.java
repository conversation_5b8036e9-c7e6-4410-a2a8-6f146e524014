package com.dpw.ctms.move.config;

import com.dpw.ctms.move.constants.ConfigConstants;
import com.dpw.ctms.move.constants.UnleashConstant;
import com.dpw.ctms.move.enums.Tenant;
import com.dpw.ctms.move.util.JsonUtils;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

import static com.dpw.ctms.move.constants.ConfigConstants.STATE_MACHINE_STATES_KEY;
import static com.dpw.ctms.move.constants.ConfigConstants.STATE_MACHINE_TRANSITIONS_KEY;
import static com.dpw.ctms.move.constants.ConfigConstants.TRACKING_SERVICE_INTEGRATION_TOGGLE_KEY;

@Component
@RequiredArgsConstructor
@Slf4j
public class ConfigService {

    private final UnleashConfig unleashConfig;

    /**
     * Retrieves configuration for the given key and tenant.
     * First attempts to fetch from Unleash, falls back to hardcoded JSON if Unleash fails.
     *
     * @param key Configuration key (e.g., BOL_CONFIG)
     * @param tenant Tenant enum (used for logging and potential future tenant-specific fallbacks)
     * @return JsonNode containing the configuration, or null if not found
     */
    public JsonNode getConfig(String key, Tenant tenant) {
        log.info("Fetching configuration for key: {} and tenant: {}", key, tenant);

        // Try to get configuration from Unleash first
        JsonNode unleashConfig = getConfigFromUnleash(key, tenant);
        if (isValidConfig(unleashConfig, key)) {
            log.info("Successfully retrieved configuration from Unleash for key: {}", key);
            return unleashConfig;
        }

        // Fall back to hardcoded configuration
        log.info("Falling back to hardcoded configuration for key: {} and tenant: {}", key, tenant);
        return getFallbackConfig(key);
    }

    /**
     * Attempts to retrieve configuration from Unleash feature flags.
     */
    private JsonNode getConfigFromUnleash(String key, Tenant tenant) {
        try {
            String featureFlagName = getFeatureFlagName(key);
            if (featureFlagName == null) {
                log.info("No feature flag mapping found for config key: {}", key);
                return null;
            }

            JsonNode config = unleashConfig.getMoveConfig(featureFlagName);
            if (config != null) {
                log.info("Retrieved configuration from Unleash for key: {}", key);
            } else {
                log.info("No configuration found in Unleash for key: {}", key);
            }
            return config;

        } catch (Exception e) {
            log.error("Error retrieving configuration from Unleash for key: {} and tenant: {}", key, tenant, e);
            return null;
        }
    }

    /**
     * Maps configuration keys to their corresponding to Unleash feature flag names.
     */
    private String getFeatureFlagName(String configKey) {
        switch (configKey) {
            case ConfigConstants.BOL_CONFIG:
                return UnleashConstant.FeatureFlags.MOVE_BOL_DOCUMENT_CONFIG;
            case ConfigConstants.TIMEZONE_CODE_TO_OFFSET:
                return UnleashConstant.FeatureFlags.TIMEZONE_CODE_TO_OFFSET;
            case ConfigConstants.OFFSET_TO_TIMEZONE_MAP:
                return UnleashConstant.FeatureFlags.OFFSET_TO_TIMEZONE_MAP;
            case ConfigConstants.VEHICLE_OPERATOR_ROLE_CONFIG:
                return UnleashConstant.FeatureFlags.MOVE_VEHICLE_OPERATOR_ROLE_CONFIG;
            case ConfigConstants.SHIPMENT_STATE_MACHINE_CONFIG:
                return UnleashConstant.FeatureFlags.MOVE_SHIPMENT_STATE_MACHINE_CONFIG;
            case ConfigConstants.TRIP_STATE_MACHINE_CONFIG:
                return UnleashConstant.FeatureFlags.MOVE_TRIP_STATE_MACHINE_CONFIG;
            case ConfigConstants.TASK_STATE_MACHINE_CONFIG:
                return UnleashConstant.FeatureFlags.MOVE_TASK_STATE_MACHINE_CONFIG;
            case ConfigConstants.TO_STATE_MACHINE_CONFIG:
                return UnleashConstant.FeatureFlags.MOVE_TO_STATE_MACHINE_CONFIG;
            case ConfigConstants.TASK_MANDATORY_CHECK_CHANNEL:
                return UnleashConstant.FeatureFlags.MOVE_TASK_MANDATORY_CHECK_CHANNEL;
            case ConfigConstants.TASK_CODE_MAPPING:
                return UnleashConstant.FeatureFlags.MOVE_TASK_CODE_MAPPING;
            case ConfigConstants.ENABLE_RUNNER_API_CALL:
                return UnleashConstant.FeatureFlags.MOVE_ENABLE_RUNNER_API_CALL;
            case ConfigConstants.TRACKING_SERVICE_INTEGRATION_TOGGLE_CONFIG:
                return UnleashConstant.FeatureFlags.MOVE_TRACKING_SERVICE_INTEGRATION_TOGGLE_CONFIG;
            default:
                return null;
        }
    }

    /**
     * Validates that the configuration has the required structure.
     */
    private boolean isValidConfig(JsonNode config, String key) {
        if (config == null || config.isNull()) {
            return false;
        }

        // Validate based on config type
        switch (key) {
            case ConfigConstants.BOL_CONFIG:
                return isValidBolConfig(config);
            case ConfigConstants.VEHICLE_OPERATOR_ROLE_CONFIG:
                return isValidVehicleOperatorRoleConfig(config);
            case ConfigConstants.TIMEZONE_CODE_TO_OFFSET:
            case ConfigConstants.OFFSET_TO_TIMEZONE_MAP:
                return isValidTimezoneConfig(config);
            case ConfigConstants.SHIPMENT_STATE_MACHINE_CONFIG:
            case ConfigConstants.TRIP_STATE_MACHINE_CONFIG:
            case ConfigConstants.TO_STATE_MACHINE_CONFIG:
            case ConfigConstants.TASK_STATE_MACHINE_CONFIG:
                return isValidStateMachineConfig(key, config);
            case ConfigConstants.ENABLE_RUNNER_API_CALL:
                return isValidEnableRunnerApiCall(config);
            case ConfigConstants.TRACKING_SERVICE_INTEGRATION_TOGGLE_CONFIG:
                return isValidTrackingServiceIntegrationToggleConfig(config);
            default:
                // For unknown config types, just check if it's not null
                return true;
        }
    }

    /**
     * Validates BOL configuration structure.
     */
    private boolean isValidBolConfig(JsonNode config) {
        try {
            // Check required fields exist
            if (!config.has(ConfigConstants.TEMPLATE_ID_FIELD) ||
                !config.has(ConfigConstants.JOLT_CONFIG_FIELD)) {
                log.info("BOL config missing required fields: templateId or joltConfig");
                return false;
            }

            // Validate templateId is not empty
            JsonNode templateId = config.get(ConfigConstants.TEMPLATE_ID_FIELD);
            if (templateId.isNull() || templateId.asText().trim().isEmpty()) {
                log.info("BOL config has empty templateId");
                return false;
            }

            // Validate joltConfig is an array
            JsonNode joltConfig = config.get(ConfigConstants.JOLT_CONFIG_FIELD);
            if (!joltConfig.isArray()) {
                log.info("BOL config joltConfig is not an array");
                return false;
            }

            log.info("BOL configuration validation passed");
            return true;

        } catch (Exception e) {
            log.error("Error validating BOL configuration", e);
            return false;
        }
    }

    /**
     * Validates timezone configuration structure.
     */
    private boolean isValidTimezoneConfig(JsonNode config) {
        try {
            // Check if config is an object
            if (!config.isObject()) {
                log.info("Timezone config is not an object");
                return false;
            }

            // Check if config has at least one mapping
            if (config.size() == 0) {
                log.info("Timezone config is empty");
                return false;
            }

            // Validate that all values are strings
            config.fields().forEachRemaining(entry -> {
                if (!entry.getValue().isTextual()) {
                    log.info("Timezone config has non-string value for key: {}", entry.getKey());
                }
            });

            log.info("Timezone configuration validation passed");
            return true;

        } catch (Exception e) {
            log.error("Error validating timezone configuration", e);
            return false;
        }
    }

    /**
     * Validates timezone configuration structure.
     */
    private boolean isValidEnableRunnerApiCall(JsonNode config) {
        try {
            // Check if config is an object
            if (!config.has(ConfigConstants.ENABLE_RUNNER_API_CALL_KEY)) {
                log.info("Enable Runner api call  config missing required fields: templateId or joltConfig");
                return false;
            }
            log.info("Enable Runner api call configuration validation passed");
            return true;

        } catch (Exception e) {
            log.error("Enable Runner api call configuration configuration", e);
            return false;
        }
    }
    private boolean isValidVehicleOperatorRoleConfig(JsonNode config) {
        try {
            // Check required fields exist
            if (!config.has(ConfigConstants.VEHICLE_OPERATOR_ROLE_FIELD)) {
                log.info("Vehicle Operator Role config missing required fields");
                return false;
            }

            JsonNode vehicleOperatorRoleConfig = config.get(ConfigConstants.VEHICLE_OPERATOR_ROLE_FIELD);
            if (vehicleOperatorRoleConfig.isNull() || vehicleOperatorRoleConfig.asText().trim().isEmpty()) {
                log.info("Vehicle Operator Role config has no roles defined in config service");
                return false;
            }

            log.info("Vehicle Operator Role config configuration validation passed");
            return true;

        } catch (Exception e) {
            log.error("Error validating Vehicle Operator Role configuration {}", e.getMessage());
            return false;
        }
    }

    private boolean isValidStateMachineConfig(String key, JsonNode config) {
        try {
            if (!config.has(STATE_MACHINE_STATES_KEY) || !config.has(STATE_MACHINE_TRANSITIONS_KEY)) {
                log.info("{} config missing required fields", key);
                return false;
            }

            JsonNode states = config.get(STATE_MACHINE_STATES_KEY);
            if (!states.isArray() || states.isEmpty()) {
                log.info("{} has no valid 'states' array in config", key);
                return false;
            }

            JsonNode transitions = config.get(STATE_MACHINE_TRANSITIONS_KEY);
            if (!transitions.isArray() || transitions.isEmpty()) {
                log.info("{} has no valid 'transitions' array in config", key);
                return false;
            }

            log.info("{} state machine config validations passed", key);
            return true;
        } catch (Exception e) {
            log.error("Error validating {} state machine config", key, e);
            return false;
        }
    }


    boolean isValidTrackingServiceIntegrationToggleConfig(JsonNode config) {
        try {
            if (!config.has(TRACKING_SERVICE_INTEGRATION_TOGGLE_KEY)) {
                log.info("{} config missing required fields", config);
                return false;
            }
            JsonNode isEnabledJsonNode = config.get(TRACKING_SERVICE_INTEGRATION_TOGGLE_KEY);
            if (isEnabledJsonNode.isNull() || isEnabledJsonNode.asText().trim().isEmpty()) {
                log.info("Tracking Service Integration toggle is explicitly null");
                return false;
            }

            log.info("Tracking Service Integration toggle config configuration validation passed");
            return true;
        } catch (Exception e) {
            log.error("Error validating tracking Service Integration toggle config {}" , e.getMessage());
            return false;
        }
    }

    /**
     * Returns hardcoded fallback configuration for the given key.
     * This ensures backward compatibility when Unleash is unavailable.
     */
    private JsonNode getFallbackConfig(String key) {
        Map<String, JsonNode> configMap = new HashMap<>();

        try {
            // BOL Configuration - keeping the original hardcoded JSON as fallback
            configMap.put(ConfigConstants.BOL_CONFIG, JsonUtils.toJsonNodeFromString(
                    "{\"templateId\":\"df9cef86987e7d48\",\"joltConfig\":[{\"operation\":\"shift\",\"spec\":{\"shipmentIds\":{\"0\":\"shipmentNumber\"},\"tripDetails\":{\"actualStartAt\":{\"iso\":\"actualDeparture\"},\"actualEndAt\":{\"iso\":\"actualArrival\"},\"externalOriginLocationCode\":\"originCode\",\"externalDestinationLocationCode\":\"destCode\"},\"consignmentDetailsMap\":{\"*\":{\"specialInstructions\":\"goodsDescription\",\"customerOrderMetaData\":{\"internalReferenceNumber\":\"purchaseOrderNo\",\"customerOrderNumber\":\"orderNo\"},\"expectedPickupTime\":{\"iso\":\"etd\"},\"expectedDeliveryTime\":{\"iso\":\"eta\"},\"productDetailsDTO\":{\"properties\":{\"WEIGHT\":{\"value\":\"packagingAndDimension.weight\",\"resourceUomName\":\"packagingAndDimension.weightUom\"},\"VOLUME\":{\"value\":\"packagingAndDimension.volume\",\"resourceUomName\":\"packagingAndDimension.volumeUom\"}}}}},\"facilityDetailsMap\":\"facilityMap\"}},{\"operation\":\"shift\",\"spec\":{\"shipmentNumber\":\"shipmentNumber\",\"actualDeparture\":\"actualDeparture\",\"actualArrival\":\"actualArrival\",\"orderNo\":\"orderNo\",\"purchaseOrderNo\":\"purchaseOrderNo\",\"goodsDescription\":\"goodsDescription\",\"etd\":\"etd\",\"eta\":\"eta\",\"packagingAndDimension\":\"packagingAndDimension\",\"originCode\":{\"*\":{\"@(2,facilityMap.&.name)\":\"shipFrom.name\",\"@(2,facilityMap.&.addressDetails.addressLine)\":\"shipFrom.addressLine\",\"@(2,facilityMap.&.pointOfContactDetails.email)\":\"shipFrom.emailId\"}},\"destCode\":{\"*\":{\"@(2,facilityMap.&.name)\":\"shipTo.name\",\"@(2,facilityMap.&.addressDetails.addressLine)\":\"shipTo.addressLine\",\"@(2,facilityMap.&.pointOfContactDetails.email)\":\"shipTo.emailId\"}}}},{\"operation\":\"default\",\"spec\":{\"shipmentNumber\":\"N/A\",\"actualDeparture\":\"N/A\",\"actualArrival\":\"N/A\",\"orderNo\":\"N/A\",\"purchaseOrderNo\":\"N/A\",\"goodsDescription\":\"N/A\",\"etd\":\"N/A\",\"eta\":\"N/A\",\"packagingAndDimension\":{\"weight\":\"N/A\",\"weightUom\":\"N/A\",\"volume\":\"N/A\",\"volumeUom\":\"N/A\"},\"shipFrom\":{\"name\":\"N/A\",\"addressLine\":\"N/A\",\"emailId\":\"N/A\"},\"shipTo\":{\"name\":\"N/A\",\"addressLine\":\"N/A\",\"emailId\":\"N/A\"}}},{\"operation\":\"remove\",\"spec\":{\"originCode\":\"\",\"destCode\":\"\",\"facilityMap\":\"\"}}]}"
            ));

            // Timezone Code to Offset Mapping Configuration
            configMap.put(ConfigConstants.TIMEZONE_CODE_TO_OFFSET, JsonUtils.toJsonNodeFromString(
                "{\"IDLW\":\"UTC-1200\",\"MIST\":\"UTC-1100\",\"HST\":\"UTC-1000\",\"AKST\":\"UTC-0900\",\"PT\":\"UTC-0800\",\"PST\":\"UTC-0800\",\"MST\":\"UTC-0700\",\"MSTM\":\"UTC-0700\",\"PDT\":\"UTC-0700\",\"CAST\":\"UTC-0600\",\"MDT\":\"UTC-0600\",\"CSTG\":\"UTC-0600\",\"CSTS\":\"UTC-0600\",\"ACT\":\"UTC-0500\",\"CDT\":\"UTC-0500\",\"ESTI\":\"UTC-0500\",\"CAT\":\"UTC-0400\",\"BOT\":\"UTC-0400\",\"AMT\":\"UTC-0400\",\"CLT\":\"UTC-0400\",\"NSTN\":\"UTC-0330\",\"BRT\":\"UTC-0300\",\"ART\":\"UTC-0300\",\"WGT\":\"UTC-0300\",\"UYT\":\"UTC-0300\",\"MADT\":\"UTC-0200\",\"CVST\":\"UTC-0100\",\"AZOT\":\"UTC-0100\",\"MRT\":\"UTC+0000\",\"GMT\":\"UTC+0000\",\"CET\":\"UTC+0100\",\"BEST\":\"UTC+0100\",\"SST\":\"UTC+0100\",\"CETS\":\"UTC+0100\",\"WAT\":\"UTC+0100\",\"AST\":\"UTC+0200\",\"GTB\":\"UTC+0200\",\"LST\":\"UTC+0200\",\"CST\":\"UTC+0200\",\"ZMT\":\"UTC+0200\",\"FLE\":\"UTC+0200\",\"ISTJ\":\"UTC+0200\",\"MKST\":\"UTC+0200\",\"WIST\":\"UTC+0200\",\"QST\":\"UTC+0300\",\"RST\":\"UTC+0300\",\"KST\":\"UTC+0300\",\"TbST\":\"UTC+0300\",\"IRST\":\"UTC+0330\",\"UAST\":\"UTC+0400\",\"BAST\":\"UTC+0400\",\"YST\":\"UTC+0400\",\"AFST\":\"UTC+0430\",\"YEST\":\"UTC+0500\",\"PKST\":\"UTC+0500\",\"SLT\":\"UTC+0530\",\"IST\":\"UTC+0530\",\"NST\":\"UTC+0545\",\"ALST\":\"UTC+0600\",\"BST\":\"UTC+0600\",\"MYST\":\"UTC+0630\",\"TST\":\"UTC+0700\",\"KRST\":\"UTC+0700\",\"PRST\":\"UTC+0800\",\"SGT\":\"UTC+0800\",\"UST\":\"UTC+0800\",\"ASTP\":\"UTC+0800\",\"TIST\":\"UTC+0800\",\"JST\":\"UTC+0900\",\"SKST\":\"UTC+0900\",\"YAST\":\"UTC+0900\",\"AUST\":\"UTC+0930\",\"DST\":\"UTC+0930\",\"ASTB\":\"UTC+1000\",\"MSTC\":\"UTC+1000\",\"AHST\":\"UTC+1000\",\"GST\":\"UTC+1000\",\"VST\":\"UTC+1000\",\"NSTS\":\"UTC+1100\",\"NZST\":\"UTC+1200\",\"FJST\":\"UTC+1200\",\"NUST\":\"UTC+1300\"}"
            ));

            // Offset to Timezone Mapping Configuration
            configMap.put(ConfigConstants.OFFSET_TO_TIMEZONE_MAP, JsonUtils.toJsonNodeFromString(
                "{\"UTC-1200\":\"Etc/GMT+12\",\"UTC-1100\":\"Pacific/Midway\",\"UTC-1000\":\"Pacific/Honolulu\",\"UTC-0930\":\"Pacific/Marquesas\",\"UTC-0900\":\"America/Anchorage\",\"UTC-0800\":\"America/Los_Angeles\",\"UTC-0700\":\"America/Denver\",\"UTC-0600\":\"America/Chicago\",\"UTC-0500\":\"America/New_York\",\"UTC-0400\":\"America/Caracas\",\"UTC-0330\":\"America/St_Johns\",\"UTC-0300\":\"America/Argentina/Buenos_Aires\",\"UTC-0200\":\"Etc/GMT+2\",\"UTC-0100\":\"Atlantic/Azores\",\"UTC+0000\":\"UTC\",\"UTC+0100\":\"Europe/Berlin\",\"UTC+0200\":\"Africa/Johannesburg\",\"UTC+0300\":\"Europe/Moscow\",\"UTC+0330\":\"Asia/Tehran\",\"UTC+0400\":\"Asia/Dubai\",\"UTC+0430\":\"Asia/Kabul\",\"UTC+0500\":\"Asia/Karachi\",\"UTC+0530\":\"Asia/Kolkata\",\"UTC+0545\":\"Asia/Kathmandu\",\"UTC+0600\":\"Asia/Dhaka\",\"UTC+0630\":\"Asia/Yangon\",\"UTC+0700\":\"Asia/Bangkok\",\"UTC+0800\":\"Asia/Singapore\",\"UTC+0830\":\"Asia/Pyongyang\",\"UTC+0845\":\"Australia/Eucla\",\"UTC+0900\":\"Asia/Tokyo\",\"UTC+0930\":\"Australia/Darwin\",\"UTC+1000\":\"Australia/Sydney\",\"UTC+1030\":\"Australia/Lord_Howe\",\"UTC+1100\":\"Pacific/Noumea\",\"UTC+1200\":\"Pacific/Fiji\",\"UTC+1245\":\"Pacific/Chatham\",\"UTC+1300\":\"Pacific/Tongatapu\",\"UTC+1400\":\"Pacific/Kiritimati\"}"
            ));

            // Shipment state machine config
            configMap.put(ConfigConstants.SHIPMENT_STATE_MACHINE_CONFIG, JsonUtils.toJsonNodeFromString(
                "{\"states\":[{\"state\":\"ASSIGNED\",\"isInitial\":\"true\"},{\"state\":\"ALLOCATED\",\"isInitial\":\"false\"},{\"state\":\"IN_TRANSIT\",\"isInitial\":\"false\"},{\"state\":\"DELIVERED\",\"isInitial\":\"false\"},{\"state\":\"DELIVERED_WITH_EXCEPTION\",\"isInitial\":\"false\"},{\"state\":\"CANCELLED\",\"isInitial\":\"false\"}],\"transitions\":[{\"sourceState\":\"ASSIGNED\",\"targetState\":\"ALLOCATED\",\"event\":\"RESOURCE_ALLOCATED\",\"actionId\":\"\",\"guardId\":\"\"},{\"sourceState\":\"ALLOCATED\",\"targetState\":\"IN_TRANSIT\",\"event\":\"PICKED_UP\",\"actionId\":\"\",\"guardId\":\"\"},{\"sourceState\":\"IN_TRANSIT\",\"targetState\":\"DELIVERED\",\"event\":\"DELIVERED\",\"actionId\":\"\",\"guardId\":\"\"},{\"sourceState\":\"ASSIGNED\",\"targetState\":\"DELIVERED\",\"event\":\"DELIVERED\",\"actionId\":\"\",\"guardId\":\"\"},{\"sourceState\":\"ALLOCATED\",\"targetState\":\"DELIVERED\",\"event\":\"DELIVERED\",\"actionId\":\"\",\"guardId\":\"\"},{\"sourceState\":\"IN_TRANSIT\",\"targetState\":\"DELIVERED_WITH_EXCEPTION\",\"event\":\"DELIVERED_WITH_EXCEPTION\",\"actionId\":\"\",\"guardId\":\"\"},{\"sourceState\":\"ASSIGNED\",\"targetState\":\"DELIVERED_WITH_EXCEPTION\",\"event\":\"DELIVERED_WITH_EXCEPTION\",\"actionId\":\"\",\"guardId\":\"\"},{\"sourceState\":\"ALLOCATED\",\"targetState\":\"DELIVERED_WITH_EXCEPTION\",\"event\":\"DELIVERED_WITH_EXCEPTION\",\"actionId\":\"\",\"guardId\":\"\"},{\"sourceState\":\"ASSIGNED\",\"targetState\":\"IN_TRANSIT\",\"event\":\"PICKED_UP\",\"actionId\":\"\",\"guardId\":\"\"},{\"sourceState\":\"ASSIGNED\",\"targetState\":\"CANCELLED\",\"event\":\"CANCELLED\",\"actionId\":\"\",\"guardId\":\"\"},{\"sourceState\":\"ALLOCATED\",\"targetState\":\"CANCELLED\",\"event\":\"CANCELLED\",\"actionId\":\"\",\"guardId\":\"\"}]}"
            ));

            configMap.put(ConfigConstants.TRIP_STATE_MACHINE_CONFIG, JsonUtils.toJsonNodeFromString(
                    "{\"states\":[{\"state\":\"CREATED\",\"isInitial\":\"true\"},{\"state\":\"IN_PROGRESS\",\"isInitial\":\"false\"},{\"state\":\"COMPLETED\",\"isInitial\":\"false\"},{\"state\":\"COMPLETED_WITH_EXCEPTIONS\",\"isInitial\":\"false\"},{\"state\":\"CANCELLED\",\"isInitial\":\"false\"},{\"state\":\"CLOSED\",\"isInitial\":\"false\"}],\"transitions\":[{\"sourceState\":\"CREATED\",\"targetState\":\"IN_PROGRESS\",\"event\":\"START_TRIP\",\"actionId\":\"\",\"guardId\":\"\"},{\"sourceState\":\"IN_PROGRESS\",\"targetState\":\"COMPLETED\",\"event\":\"END_TRIP\",\"actionId\":\"\",\"guardId\":\"\"},{\"sourceState\":\"IN_PROGRESS\",\"targetState\":\"COMPLETED_WITH_EXCEPTIONS\",\"event\":\"END_TRIP_WITH_EXCEPTION\",\"actionId\":\"\",\"guardId\":\"\"},{\"sourceState\":\"COMPLETED\",\"targetState\":\"CLOSED\",\"event\":\"CLOSE_TRIP\",\"actionId\":\"\",\"guardId\":\"\"},{\"sourceState\":\"COMPLETED_WITH_EXCEPTIONS\",\"targetState\":\"CLOSED\",\"event\":\"CLOSE_TRIP\",\"actionId\":\"\",\"guardId\":\"\"},{\"sourceState\":\"CREATED\",\"targetState\":\"CANCELLED\",\"event\":\"CANCEL_TRIP\",\"actionId\":\"\",\"guardId\":\"\"},{\"sourceState\":\"IN_PROGRESS\",\"targetState\":\"CANCELLED\",\"event\":\"CANCEL_TRIP\",\"actionId\":\"\",\"guardId\":\"\"}]}"
            ));

            configMap.put(ConfigConstants.TASK_STATE_MACHINE_CONFIG, JsonUtils.toJsonNodeFromString(
                "{\"states\":[{\"state\":\"CREATED\",\"isInitial\":\"true\"},{\"state\":\"COMPLETED\",\"isInitial\":\"false\"},{\"state\":\"CLOSED\",\"isInitial\":\"false\"},{\"state\":\"DISCARDED\",\"isInitial\":\"false\"}],\"transitions\":[{\"sourceState\":\"CREATED\",\"targetState\":\"COMPLETED\",\"event\":\"TASK_COMPLETED\",\"actionId\":\"\",\"guardId\":\"\"},{\"sourceState\":\"COMPLETED\",\"targetState\":\"CLOSED\",\"event\":\"TASK_CLOSED\",\"actionId\":\"\",\"guardId\":\"\"},{\"sourceState\":\"CREATED\",\"targetState\":\"CLOSED\",\"event\":\"TASK_CLOSED\",\"actionId\":\"\",\"guardId\":\"\"},{\"sourceState\":\"CREATED\",\"targetState\":\"DISCARDED\",\"event\":\"TASK_DISCARDED\",\"actionId\":\"\",\"guardId\":\"\"}]}"
            ));

            configMap.put(ConfigConstants.TO_STATE_MACHINE_CONFIG, JsonUtils.toJsonNodeFromString(
                    "{\"states\":[{\"state\":\"ASSIGNED\",\"isInitial\":\"true\"},{\"state\":\"ALLOCATED\",\"isInitial\":\"false\"},{\"state\":\"IN_PROGRESS\",\"isInitial\":\"false\"},{\"state\":\"EXECUTED\",\"isInitial\":\"false\"},{\"state\":\"EXECUTED_WITH_EXCEPTIONS\",\"isInitial\":\"false\"},{\"state\":\"CANCELLED\",\"isInitial\":\"false\"},{\"state\":\"CLOSED\",\"isInitial\":\"false\"}],\"transitions\":[{\"sourceState\":\"ASSIGNED\",\"targetState\":\"IN_PROGRESS\",\"event\":\"START_TRANSPORT_ORDER_EXECUTION\",\"actionId\":\"\",\"guardId\":\"\"},{\"sourceState\":\"ALLOCATED\",\"targetState\":\"IN_PROGRESS\",\"event\":\"START_TRANSPORT_ORDER_EXECUTION\",\"actionId\":\"\",\"guardId\":\"\"},{\"sourceState\":\"ASSIGNED\",\"targetState\":\"ALLOCATED\",\"event\":\"RESOURCE_ALLOCATED\",\"actionId\":\"\",\"guardId\":\"\"},{\"sourceState\":\"IN_PROGRESS\",\"targetState\":\"EXECUTED\",\"event\":\"TRANSPORT_ORDER_EXECUTION_COMPLETED\",\"actionId\":\"\",\"guardId\":\"\"},{\"sourceState\":\"IN_PROGRESS\",\"targetState\":\"EXECUTED_WITH_EXCEPTIONS\",\"event\":\"TRANSPORT_ORDER_EXECUTION_COMPLETED_WITH_EXCEPTION\",\"actionId\":\"\",\"guardId\":\"\"},{\"sourceState\":\"EXECUTED\",\"targetState\":\"CLOSED\",\"event\":\"CLOSE_TRANSPORT_ORDER\",\"actionId\":\"\",\"guardId\":\"\"},{\"sourceState\":\"EXECUTED_WITH_EXCEPTIONS\",\"targetState\":\"CLOSED\",\"event\":\"CLOSE_TRANSPORT_ORDER\",\"actionId\":\"\",\"guardId\":\"\"},{\"sourceState\":\"ASSIGNED\",\"targetState\":\"CANCELLED\",\"event\":\"CANCEL_TRANSPORT_ORDER\",\"actionId\":\"\",\"guardId\":\"\"},{\"sourceState\":\"ALLOCATED\",\"targetState\":\"CANCELLED\",\"event\":\"CANCEL_TRANSPORT_ORDER\",\"actionId\":\"\",\"guardId\":\"\"},{\"sourceState\":\"IN_PROGRESS\",\"targetState\":\"CANCELLED\",\"event\":\"CANCEL_TRANSPORT_ORDER\",\"actionId\":\"\",\"guardId\":\"\"}]}"
            ));

            configMap.put(ConfigConstants.VEHICLE_OPERATOR_ROLE_CONFIG, JsonUtils.toJsonNodeFromString(
                "{\"role\": \"CFRDRIVER\"}"
            ));

            configMap.put(ConfigConstants.TASK_MANDATORY_CHECK_CHANNEL, JsonUtils.toJsonNodeFromString(
                    "{\"mandatoryCheckChannels\":[\"WEB\"]}"
            ));

            configMap.put(ConfigConstants.TASK_CODE_MAPPING, JsonUtils.toJsonNodeFromString(
                    "{\"CONSIGNMENT_PICKUP_CFR\":\"PICKUP\",\"CONSIGNMENT_DELIVERY_CFR\":\"DELIVERY\",\"DRIVER_JOURNEY_START_CFR\":\"GENERAL\",\"DRIVER_JOURNEY_END_CFR\":\"GENERAL\"}"
            ));

            configMap.put(ConfigConstants.ENABLE_RUNNER_API_CALL, JsonUtils.toJsonNodeFromString(
                    "{\"enable\": \"false\"}"
            ));

            configMap.put(ConfigConstants.TRACKING_SERVICE_INTEGRATION_TOGGLE_CONFIG, JsonUtils.toJsonNodeFromString(
              "{\"isEnabled\": \"false\"}"
            ));

            JsonNode fallbackConfig = configMap.get(key);
            if (fallbackConfig != null) {
                log.info("Using fallback configuration for key: {}", key);
            } else {
                log.info("No fallback configuration available for key: {}", key);
            }

            return fallbackConfig;

        } catch (Exception e) {
            log.error("Error creating fallback configuration for key: {}", key, e);
            return null;
        }
    }

}
