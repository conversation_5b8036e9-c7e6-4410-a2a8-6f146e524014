package com.dpw.ctms.move.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.zalando.logbook.HttpRequest;
import org.zalando.logbook.Logbook;

import java.util.Arrays;
import java.util.List;
import java.util.function.Predicate;

@Configuration
public class LogbookConfig {

    private static final List<String> EXCLUDED_PATHS = Arrays.asList(
            "/move/health",
            "/move/metrics",
            "/move/actuator"
    );

    @Bean
    public Logbook logbook() {
        return Logbook.builder()
                .condition(excludePaths())
                .build();
    }

    private Predicate<HttpRequest> excludePaths() {
        return request -> EXCLUDED_PATHS.stream()
                .noneMatch(path -> request.getPath().startsWith(path));
    }
}