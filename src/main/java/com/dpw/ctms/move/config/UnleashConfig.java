package com.dpw.ctms.move.config;

import com.dpw.ctms.move.constants.UnleashConstant;
import com.dpw.tmsutils.threadlocal.TenantContext;
import com.dpw.tmsutils.utils.TMSConfigUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

import lombok.extern.slf4j.Slf4j;

import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

@Component
@Configuration
@Slf4j
public class UnleashConfig {

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Retrieves configuration from Unleash feature flags for the current tenant.
     *
     * @param featureName The name of the feature flag to retrieve
     * @return JsonNode containing the configuration, or null if not found or error occurs
     */
    public JsonNode getMoveConfig(String featureName) {
        String tenantId = TenantContext.getCurrentTenant();

        if (tenantId == null || tenantId.trim().isEmpty()) {
            log.info("No tenant context available for feature flag: {}", featureName);
            return null;
        }

        log.info("Fetching feature flag: {} for tenant: {}", featureName, tenantId);

        try {
            ObjectNode contextNode = objectMapper.createObjectNode();
            contextNode.put(UnleashConstant.TENANT, tenantId);

            JsonNode configuration = TMSConfigUtils.getFlagValue(featureName, contextNode, JsonNode.class);

            if (configuration != null) {
                log.info("Successfully retrieved feature flag: {} for tenant: {}", featureName, tenantId);
            } else {
                log.info("Feature flag: {} returned null for tenant: {}", featureName, tenantId);
            }

            return configuration;

        } catch (Exception e) {
            log.error("Error fetching feature flag: {} for tenant: {}. Error: {}",
                     featureName, tenantId, e.getMessage(), e);
            return null;
        }
    }

}
