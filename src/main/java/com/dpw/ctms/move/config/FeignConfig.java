package com.dpw.ctms.move.config;

import com.dpw.ctmsutils.interceptor.ApimFeignInterceptorWithUserAndServiceContext;
import com.dpw.ctmsutils.service.OAuth2TokenService;
import feign.Logger;
import feign.RequestInterceptor;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration for Feign clients
 */
@Configuration
@RequiredArgsConstructor
public class FeignConfig {

    private final OAuth2TokenService oAuth2TokenService;

    /**
     * Configure Feign logging level
     * @return Feign logging level
     */
    @Bean
    public Logger.Level feignLoggerLevel() {
        return Logger.Level.FULL;
    }

    @Bean
    public RequestInterceptor apimFeignInterceptorWithUserAndServiceContext() {
        return new ApimFeignInterceptorWithUserAndServiceContext().authRequestInterceptor(oAuth2TokenService);
    }
}
