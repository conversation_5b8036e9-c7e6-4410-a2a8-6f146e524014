package com.dpw.ctms.move.kafka.consumer.cfr;

import com.dpw.ctms.move.dto.ParamValueShipmentDTO;
import com.dpw.ctms.move.dto.consumer.cfr.DeliveryTaskMessageDTO;
import com.dpw.ctms.move.dto.document.DeliveryTaskDocumentDTO;
import com.dpw.ctms.move.dto.consumer.IntegratorTaskMessageRequestDTO;
import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.enums.*;
import com.dpw.ctms.move.kafka.consumer.TaskActionHandler;
import com.dpw.ctms.move.service.IDocumentsService;
import com.dpw.ctms.move.service.IShipmentService;
import com.dpw.ctms.move.service.ITaskService;
import com.dpw.ctms.move.service.ITripService;
import com.dpw.ctms.move.statemachine.registry.StateMachineServiceRegistry;
import com.dpw.ctms.move.util.EnumUtils;
import com.dpw.tmsutils.threadlocal.TenantContext;
import com.dpw.tmsutils.utils.ObjectMapperUtil;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import static com.dpw.ctms.move.mapper.TimeMapper.toTime;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class DeliveryTaskHandler implements TaskActionHandler<DeliveryTaskMessageDTO.PercolatedRecordDTO> {

    private final ITaskService taskService;
    private final IShipmentService shipmentService;
    private final ITripService tripService;
    private final StateMachineServiceRegistry stateMachineServiceRegistry;
    private final TaskPercolationHelperUtil taskPercolationHelperUtil;
    private final IDocumentsService documentService;

    @Override
    @Transactional
    public void handle(IntegratorTaskMessageRequestDTO<DeliveryTaskMessageDTO.PercolatedRecordDTO> message) {
        String taskCode = message.getMessage().getTaskDetails().getTaskTransactionCode();
        TaskStatus taskStatus = EnumUtils.getEnumFromString(TaskStatus.class,
                message.getMessage().getTaskDetails().getStatus(), true);
        Task task = taskService.findTaskByCode(taskCode);
        ParamValueShipmentDTO shipmentDTO = taskPercolationHelperUtil
                .extractShipmentDTOFromTask(task, taskCode);
        Shipment shipment = shipmentService.findShipmentByCode(shipmentDTO.getCode());
        Trip trip = shipment.getTrip();

        Shipment initialShipment = ObjectMapperUtil.getObjectMapper().convertValue(shipment, Shipment.class);
        Task initialTask = ObjectMapperUtil.getObjectMapper().convertValue(task, Task.class);
        Trip initialTrip = ObjectMapperUtil.getObjectMapper().convertValue(trip, Trip.class);

        //update task status and details
        taskPercolationHelperUtil.updateTask(task, taskStatus, message);
        DeliveryTaskMessageDTO.PercolatedRecordDTO percolatedRecords = ObjectMapperUtil.getObjectMapper().convertValue(
                message.getMessage().getPercolatedRecords(), DeliveryTaskMessageDTO.PercolatedRecordDTO.class);
        updateShipment(shipment, percolatedRecords, taskStatus);
        updateTrip(shipment.getTrip(), percolatedRecords, taskStatus);
        updateDocument(task, percolatedRecords, taskStatus);
        taskPercolationHelperUtil.updateTransportOrder(shipment.getTransportOrder());

        taskPercolationHelperUtil.triggerEvent(initialTask, initialShipment, initialTrip);
    }

    private void updateShipment(Shipment shipment, DeliveryTaskMessageDTO.PercolatedRecordDTO percolatedRecords,
                                TaskStatus taskStatus) {
        if (taskStatus == TaskStatus.CLOSED) {
            shipment.setActualDeliveryAt(toTime(percolatedRecords.getUnloadingCompletionTime()));
            shipment = shipmentService.saveShipment(shipment);
        }

        // Updating shipment status to DELIVERED either from IN_TRANSIT
        stateMachineServiceRegistry.getService(StateMachineEntityType.SHIPMENT)
                .handleEvent(TenantContext.getCurrentTenant(), ShipmentLifecycleEvent.DELIVERED.name(),
                        shipment.getId());
    }


    private void updateTrip(Trip trip, DeliveryTaskMessageDTO.PercolatedRecordDTO params, TaskStatus status) {

        if (status == TaskStatus.CLOSED) {
            trip.setActualEndAt(toTime(params.getUnloadingCompletionTime()));
            trip = tripService.saveTrip(trip);
        }

        taskPercolationHelperUtil.updateTripStatus(trip);
    }

    private void updateDocument(Task task, DeliveryTaskMessageDTO.PercolatedRecordDTO params, TaskStatus status) {

        if (status == TaskStatus.CLOSED) {
            List<String> bolIdentifier = params.getBolIdentifier();
            log.info("BolIdentifier: {}", bolIdentifier);
            
            DeliveryTaskDocumentDTO deliveryTaskDocumentDTO = DeliveryTaskDocumentDTO.builder()
                    .entityId(task.getCode())
                    .entityType(EntityType.TASK.name())
                    .asyncMappingUUIDs(bolIdentifier != null ? bolIdentifier : List.of())
                    .operationType(DocumentOperationType.UPLOAD)
                    .documentType(DocumentType.POD)
                    .build();

            documentService.findAndUpdate(deliveryTaskDocumentDTO);
        }
    }
}
