package com.dpw.ctms.move.kafka.consumer.cfr;

import com.dpw.ctms.move.dto.consumer.IntegratorTaskMessageRequestDTO;
import com.dpw.ctms.move.dto.consumer.cfr.GeneralTaskMessageDTO;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.enums.TaskStatus;
import com.dpw.ctms.move.kafka.consumer.TaskActionHandler;
import com.dpw.ctms.move.service.ITaskService;
import com.dpw.ctms.move.util.EnumUtils;
import com.dpw.tmsutils.utils.ObjectMapperUtil;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class GeneralTaskHandler implements TaskActionHandler<GeneralTaskMessageDTO.PercolatedRecordDTO> {

    private final ITaskService taskService;
    private final TaskPercolationHelperUtil taskPercolationHelperUtil;

    @Override
    @Transactional
    public void handle(IntegratorTaskMessageRequestDTO<GeneralTaskMessageDTO.PercolatedRecordDTO> message) {
        String taskCode = message.getMessage().getTaskDetails().getTaskTransactionCode();
        TaskStatus taskStatus = EnumUtils.getEnumFromString(TaskStatus.class,
                message.getMessage().getTaskDetails().getStatus(), true);
        Task task = taskService.findTaskByCode(taskCode);
        Trip trip = task.getStopTask().getStop().getTrip();

        Task initialTask = ObjectMapperUtil.getObjectMapper().convertValue(task, Task.class);
        Trip intialTrip = ObjectMapperUtil.getObjectMapper().convertValue(trip, Trip.class);

        taskPercolationHelperUtil.updateTask(task, taskStatus, message);
        taskPercolationHelperUtil.updateTripStatus(trip);
        taskPercolationHelperUtil.updateTransportOrder(trip.getTransportOrder());

        taskPercolationHelperUtil.triggerEvent(initialTask, null, intialTrip);
    }
}
