package com.dpw.ctms.move.kafka.consumer;

import com.dpw.ctms.move.dto.consumer.IntegratorMessageRequestDTO;
import com.dpw.ctms.move.dto.consumer.ShipmentCancellationMessageRequestDTO;
import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.helper.EventTriggerHelper;
import com.dpw.ctms.move.service.IShipmentCancellationCoordinatorService;
import com.dpw.ctms.move.service.IShipmentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.List;

import static com.dpw.ctms.move.constants.KafkaEventActionConstants.SHIPMENT_CANCELLATION_REQUEST_EVENT_ACTION;

@Component
@RequiredArgsConstructor
@Slf4j
public class ShipmentCancellationRequestHandler implements ShipmentEventHandler{
    private final IShipmentService shipmentService;
    private final IShipmentCancellationCoordinatorService shipmentCancellationCoordinatorService;
    private final EventTriggerHelper eventTriggerHelper;

    @Override
    @Transactional
    public void handle(IntegratorMessageRequestDTO<?> message) {
        ShipmentCancellationMessageRequestDTO item =
                (ShipmentCancellationMessageRequestDTO) message.getMessage().getItem();
        List<String> shipmentCodes = item.getShipmentCodes();
        List<Shipment> shipments = shipmentService.getAllByCodes(new HashSet<>(shipmentCodes));
        boolean isCancelled = false;
        try {
            isCancelled = shipmentCancellationCoordinatorService.cancelShipmentsAndRelatedEntities(shipments);
        }
        catch (Exception e) {
            log.error("Error while cancelling shipments having codes: {} and exception: {}", shipmentCodes, e.getMessage());
        }
        eventTriggerHelper.triggerCancellationEvent(shipmentCodes, isCancelled, message.getTransactionContext().getCorrelationId());
    }
    @Override
    public String getSupportedAction() {
        return SHIPMENT_CANCELLATION_REQUEST_EVENT_ACTION;
    }
}
