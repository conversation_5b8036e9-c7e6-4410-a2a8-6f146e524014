package com.dpw.ctms.move.kafka.consumer;

import com.dpw.ctms.move.kafka.consumer.cfr.DeliveryTaskHandler;
import com.dpw.ctms.move.kafka.consumer.cfr.GeneralTaskHandler;
import com.dpw.ctms.move.kafka.consumer.cfr.PickupTaskHandler;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class TaskActionHandlerRegistry {

    private final Map<String, TaskActionHandler> handlerMap;
    private static final String PICKUP_TASK = "PICKUP";
    private static final String DELIVERY_TASK = "DELIVERY";
    private static final String GENERAL_TASK = "GENERAL";

    public TaskActionHandlerRegistry(
            PickupTaskHandler pickupTaskHandler,
            DeliveryTaskHandler deliveryTaskHandler,
            GeneralTaskHandler generalTaskHandler) {
        this.handlerMap = Map.of(PICKUP_TASK, pickupTaskHandler,
                DELIVERY_TASK, deliveryTaskHandler,
                GENERAL_TASK, generalTaskHandler);
    }

    public TaskActionHandler getHandler(String action) {
        return handlerMap.get(action);
    }
}
