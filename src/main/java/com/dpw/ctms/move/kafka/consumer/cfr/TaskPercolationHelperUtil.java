package com.dpw.ctms.move.kafka.consumer.cfr;

import com.dpw.ctms.move.dto.ParamValueShipmentDTO;
import com.dpw.ctms.move.dto.consumer.IntegratorTaskMessageRequestDTO;
import com.dpw.ctms.move.entity.*;
import com.dpw.ctms.move.enums.*;
import com.dpw.ctms.move.helper.EventTriggerHelper;
import com.dpw.ctms.move.helper.TrackingServiceIntegrationHelper;
import com.dpw.ctms.move.integration.dto.trackingservice.TrackingServiceIntegrationToggleDTO;
import com.dpw.ctms.move.integration.dto.trackingservice.TripTrackingDTO;
import com.dpw.ctms.move.integration.service.IConfigServiceIntegrator;
import com.dpw.ctms.move.service.IShipmentService;
import com.dpw.ctms.move.service.ITaskService;
import com.dpw.ctms.move.service.ITripService;
import com.dpw.ctms.move.statemachine.registry.StateMachineServiceRegistry;
import com.dpw.tmsutils.threadlocal.TenantContext;
import com.dpw.tmsutils.utils.ObjectMapperUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.stream.Collectors;

import static com.dpw.ctms.move.constants.ConfigConstants.TRACKING_SERVICE_INTEGRATION_TOGGLE_CONFIG;
import static com.dpw.ctms.move.mapper.TimeMapper.toTime;


@Component
@RequiredArgsConstructor
@Slf4j
public class TaskPercolationHelperUtil {

    private final StateMachineServiceRegistry stateMachineServiceRegistry;
    private final ITaskService taskService;
    private final IShipmentService shipmentService;
    private final ITripService tripService;
    private final EventTriggerHelper eventTriggerHelper;
    private final TrackingServiceIntegrationHelper trackingServiceIntegrationHelper;
    private final IConfigServiceIntegrator configServiceIntegrator;


    public void updateTripStatus(Trip trip) {

        stateMachineServiceRegistry.getService(StateMachineEntityType.TRIP)
                .handleEvent(TenantContext.getCurrentTenant(), TripLifecycleEvent.START_TRIP.name(), trip.getId());

        Set<Task> tripTasks = trip.getStops().stream()
                .flatMap(stop -> stop.getStopTasks().stream().map(StopTask::getTask))
                .collect(Collectors.toSet());

        // Update trip to completed if all tasks are completed or closed
        // even if all tasks are closed it will move to closed in next step
        boolean tripTasksCompleted = tripTasks.stream()
                .filter(task -> task.getStatus() != TaskStatus.DISCARDED)
                .filter(task -> Boolean.TRUE.equals(task.getMandatory()))
                .allMatch(task -> task.getStatus() == TaskStatus.COMPLETED
                        || task.getStatus() == TaskStatus.CLOSED);
        if (tripTasksCompleted)
            stateMachineServiceRegistry.getService(StateMachineEntityType.TRIP)
                    .handleEvent(TenantContext.getCurrentTenant(), TripLifecycleEvent.END_TRIP.name(), trip.getId());

        // Update trip to closed if all tasks are closed
        boolean tripTasksClosed = tripTasks.stream()
                .filter(task -> task.getStatus() != TaskStatus.DISCARDED)
                .filter(task -> Boolean.TRUE.equals(task.getMandatory()))
                .allMatch(task -> task.getStatus() == TaskStatus.CLOSED);
        if (tripTasksClosed)
            stateMachineServiceRegistry.getService(StateMachineEntityType.TRIP)
                    .handleEvent(TenantContext.getCurrentTenant(), TripLifecycleEvent.CLOSE_TRIP.name(), trip.getId());
    }

    public void updateTask(Task task, TaskStatus taskStatus,
                           IntegratorTaskMessageRequestDTO<?> message) {
        if (ObjectUtils.isNotEmpty(message.getMessage().getTaskDetails().getActualTimeRange()) &&
                ObjectUtils.isNotEmpty(message.getMessage().getTaskDetails().getActualTimeRange().getFrom())) {
            task.setActualStartAt(toTime(message.getMessage().getTaskDetails().getActualTimeRange().getFrom()));
        }
        if (ObjectUtils.isNotEmpty(message.getMessage().getTaskDetails().getActualTimeRange()) &&
                ObjectUtils.isNotEmpty(message.getMessage().getTaskDetails().getActualTimeRange().getTo())) {
            task.setActualEndAt(toTime(message.getMessage().getTaskDetails().getActualTimeRange().getTo()));
        }
        if (taskStatus.equals(TaskStatus.CLOSED)) {
            task.setDetails(ObjectMapperUtil.getObjectMapper()
                    .convertValue(message.getMessage().getPercolatedRecords(), JsonNode.class));
        }
        taskService.saveTask(task);
        TaskLifecycleEvent taskLifecycleEvent = (taskStatus.equals(TaskStatus.CLOSED) ?
                TaskLifecycleEvent.TASK_CLOSED : TaskLifecycleEvent.TASK_COMPLETED);
        stateMachineServiceRegistry.getService(StateMachineEntityType.TASK)
                .handleEvent(TenantContext.getCurrentTenant(), taskLifecycleEvent.name(), task.getId());
    }

    public void updateTransportOrder(TransportOrder transportOrder) {

        if (ObjectUtils.isEmpty(transportOrder)) return;

        stateMachineServiceRegistry.getService(StateMachineEntityType.TRANSPORT_ORDER)
                .handleEvent(TenantContext.getCurrentTenant(),
                        TransportOrderLifecycleEvent.START_TRANSPORT_ORDER_EXECUTION.name(),
                        transportOrder.getId());

        Set<Trip> trips = transportOrder.getTrips();

        boolean tripsCompleted = trips.stream()
                .filter(trip -> trip.getStatus() != TripStatus.CANCELLED)
                .allMatch(trip -> trip.getStatus() == TripStatus.COMPLETED
                        || trip.getStatus() == TripStatus.CLOSED);
        if (tripsCompleted) {
            stateMachineServiceRegistry.getService(StateMachineEntityType.TRANSPORT_ORDER)
                    .handleEvent(TenantContext.getCurrentTenant(),
                            TransportOrderLifecycleEvent.TRANSPORT_ORDER_EXECUTION_COMPLETED.name(),
                            transportOrder.getId());
        }

        boolean tripsClosed = trips.stream()
                .filter(trip -> trip.getStatus() != TripStatus.CANCELLED)
                .allMatch(trip -> trip.getStatus() == TripStatus.CLOSED);
        if (tripsClosed) {
            stateMachineServiceRegistry.getService(StateMachineEntityType.TRANSPORT_ORDER)
                    .handleEvent(TenantContext.getCurrentTenant(),
                            TransportOrderLifecycleEvent.CLOSE_TRANSPORT_ORDER.name(),
                            transportOrder.getId());
        }
    }

    public ParamValueShipmentDTO extractShipmentDTOFromTask(Task task, String taskCode) {
        ParamValueShipmentDTO shipmentDTO = task.getTaskParams().stream()
                .filter(t -> StringUtils.equalsAnyIgnoreCase(t.getParamName().name(), ("SHIPMENT")))
                .map(TaskParam::getParamValue)
                .map(val -> ObjectMapperUtil.getObjectMapper().convertValue(val, ParamValueShipmentDTO.class))
                .findFirst()
                .orElse(null);

        if (shipmentDTO == null) {
            log.error("No shipment found for task code {}", taskCode);
            throw new com.dpw.tmsutils.exception.TMSException(
                    com.dpw.tmsutils.utils.TMSExceptionErrorCode.INVALID_DATA.name(),
                    String.format("No shipment found for task code %s", taskCode));
        }
        return shipmentDTO;
    }

    public void triggerEvent(Task initialTask, Shipment initialShipment, Trip intialTrip) {
        if (ObjectUtils.isNotEmpty(initialTask)) {
            Task finalTask = taskService.findTaskById(initialTask.getId());
            eventTriggerHelper.triggerStatusChangeEvent(initialTask, finalTask, StateMachineEntityType.TASK);
        }

        if (ObjectUtils.isNotEmpty(initialShipment)) {
            Shipment finalShipment = shipmentService.findShipmentById(initialShipment.getId());
            eventTriggerHelper.triggerStatusChangeEvent(initialShipment, finalShipment, StateMachineEntityType.SHIPMENT);
        }

        if (ObjectUtils.isNotEmpty(intialTrip)) {
            Trip finalTrip = tripService.findTripById(intialTrip.getId());
            eventTriggerHelper.triggerStatusChangeEvent(intialTrip, finalTrip, StateMachineEntityType.TRIP);
        }
        if (ObjectUtils.isNotEmpty(intialTrip) && ObjectUtils.isNotEmpty(initialTask)) {
            if (configServiceIntegrator.fetchConfig(TRACKING_SERVICE_INTEGRATION_TOGGLE_CONFIG, new TypeReference<TrackingServiceIntegrationToggleDTO>() {}).getIsEnabled()) {
                Task finalTask = taskService.findTaskById(initialTask.getId());
                TripTrackingDTO tripTrackingDTO = TripTrackingDTO.builder()
                        .initialTrip(intialTrip)
                        .task(finalTask)
                        .build();
                trackingServiceIntegrationHelper.sendTripTrackingEvent(tripTrackingDTO);
            }
        }
    }
}
