package com.dpw.ctms.move.dto;

import com.dpw.ctms.move.entity.Document;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.List;

/**
 * DTO for document activation event that triggers post-transaction processing
 */
@Getter
@RequiredArgsConstructor
@Builder
public class DocumentUpdatedEventDTO {
    private final List<Document> activatedDocuments;
    private final List<Document> discardedDocuments;
}