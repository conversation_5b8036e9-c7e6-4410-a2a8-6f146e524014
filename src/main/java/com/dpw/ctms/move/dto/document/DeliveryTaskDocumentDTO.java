package com.dpw.ctms.move.dto.document;

import com.dpw.ctms.move.enums.DocumentOperationType;
import com.dpw.ctms.move.enums.DocumentType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryTaskDocumentDTO {
    private List<String> asyncMappingUUIDs;
    private String entityId;
    private String entityType;
    private DocumentOperationType operationType;
    private DocumentType documentType;
}