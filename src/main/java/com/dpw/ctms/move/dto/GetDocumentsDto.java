package com.dpw.ctms.move.dto;

import com.dpw.ctms.move.enums.DocumentOperationType;
import com.dpw.ctms.move.enums.DocumentStatus;
import com.dpw.ctms.move.enums.DocumentType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetDocumentsDto {
    private List<String> entityIds;
    private String entityType;
    private DocumentType documentType;
    private DocumentOperationType operationType;
    private DocumentStatus status;
}