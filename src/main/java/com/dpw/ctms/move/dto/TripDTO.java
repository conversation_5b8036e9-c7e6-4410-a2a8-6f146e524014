package com.dpw.ctms.move.dto;

import com.dpw.ctms.move.enums.TripStatus;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TripDTO {
    private String code;
    private TripStatus status;
    private String externalOriginLocationCode;
    private String externalDestinationLocationCode;
    private TimeDTO expectedStartAt;
    private TimeDTO expectedEndAt;
    private List<StopDTO> stops;
    private List<ShipmentDTO> shipments;
    private List<TaskDTO> tasks;
    private VehicleResourceDTO vehicleResource;
    private List<TrailerResourceDTO> trailerResources;
    private List<VehicleOperatorResourceDTO> vehicleOperatorResources;
    private JsonNode details;
}

