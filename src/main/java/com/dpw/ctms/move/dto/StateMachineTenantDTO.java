package com.dpw.ctms.move.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class StateMachineTenantDTO {
    private StateTransitionHolderDTO task;
    private StateTransitionHolderDTO shipment;
    private StateTransitionHolderDTO trip;
    private StateTransitionHolderDTO transportOrder;
}
