package com.dpw.ctms.move.dto.producer;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ShipmentCancellationEventRequestDTO {
    private String entityType;
    private List<String> shipmentCodes;
    private Boolean isCancelled;
    private String comments;
    private String updatedBy;
    private Long updatedAt;
    private String correlationId;
}
