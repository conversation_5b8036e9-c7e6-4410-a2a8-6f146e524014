package com.dpw.ctms.move.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class ProductDetailsDTO {
    Long id;
    String name;
    Long resourceId;
    String code;
    String unNumber;
    ProductCategoryDetails productCategoryDetails;
    StatusDetails status;
    String description;
    Boolean isContainerFillingRuleApplied;
    Double filledPercentage;
    Boolean isAttentionNeeded;
    Map<String,PropertyDetails> properties;
    Long quantity;
    Boolean isHazardous;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class PropertyDetails {
        String name;
        Double value;
        Long resourceUomId;
        String resourceUomName;
        String productCode;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ProductCategoryDetails {
        Long id;
        String code;
        String name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class StatusDetails {
        String label;
        String value;
    }
}
