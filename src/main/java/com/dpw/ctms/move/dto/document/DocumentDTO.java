package com.dpw.ctms.move.dto.document;

import com.dpw.ctms.move.enums.DocumentOperationType;
import com.dpw.ctms.move.enums.DocumentStatus;
import com.dpw.ctms.move.enums.DocumentType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentDTO {
    private String entityId;
    private String entityType;
    private DocumentType documentType;
    private DocumentStatus status;
    private DocumentOperationType documentOperationType;
    private String checksum;
    private String fileIdentifier;
}

