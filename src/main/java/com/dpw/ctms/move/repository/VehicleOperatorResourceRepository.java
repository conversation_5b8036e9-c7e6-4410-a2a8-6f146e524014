package com.dpw.ctms.move.repository;

import com.dpw.ctms.move.entity.VehicleOperatorResource;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;
import java.util.Set;

import static com.dpw.ctms.move.specification.ResourceSpecifications.vehicleOperatorResourceCodeIn;
import static com.dpw.ctms.move.specification.ResourceSpecifications.vehicleOperatorResourceNotDeleted;

public interface VehicleOperatorResourceRepository extends JpaRepository<VehicleOperatorResource, Long>, JpaSpecificationExecutor<VehicleOperatorResource> {
    default List<VehicleOperatorResource> findAllByCodeInAndDeletedAtIsNull(Set<String> codes) {
        return findAll(vehicleOperatorResourceCodeIn(codes).and(vehicleOperatorResourceNotDeleted()));
    }
}
