package com.dpw.ctms.move.repository;

import com.dpw.ctms.move.entity.Stop;
import com.dpw.ctms.move.specification.StopSpecifications;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

public interface StopRepository extends JpaRepository<Stop, Long>, JpaSpecificationExecutor<Stop> {
    default Page<Stop> findAllByTripCodeAndStatusNotInOrNullAndTripDeletedAtIsNull(String tripCode, List<String> statusList, Pageable pageable) {
        return findAll(
                StopSpecifications.tripCodeEquals(tripCode)
                        .and(StopSpecifications.tripNotDeleted())
                        .and(StopSpecifications.statusNotInOrNull(statusList)),
                pageable
        );
    }

    default long countByTripCodeAndStatusNotInAndTripDeletedAtIsNull(String tripCode, List<String> statusList) {
        return count(
                StopSpecifications.tripCodeEquals(tripCode)
                        .and(StopSpecifications.tripNotDeleted())
                        .and(StopSpecifications.statusNotIn(statusList))
        );
    }
}
