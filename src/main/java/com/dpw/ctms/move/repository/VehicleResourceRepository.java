package com.dpw.ctms.move.repository;

import com.dpw.ctms.move.entity.VehicleResource;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;
import java.util.Set;

import static com.dpw.ctms.move.specification.ResourceSpecifications.vehicleResourceCodeIn;
import static com.dpw.ctms.move.specification.ResourceSpecifications.vehicleResourceNotDeleted;

public interface VehicleResourceRepository extends JpaRepository<VehicleResource, Long>, JpaSpecificationExecutor<VehicleResource> {
    default List<VehicleResource> findAllByCodeInAndDeletedAtIsNull(Set<String> codes) {
        return findAll(vehicleResourceCodeIn(codes).and(vehicleResourceNotDeleted()));
    }
}
