package com.dpw.ctms.move.repository;

import com.dpw.ctms.move.entity.Trip;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import static com.dpw.ctms.move.specification.TripSpecifications.codeIn;
import static com.dpw.ctms.move.specification.TripSpecifications.hasCode;
import static com.dpw.ctms.move.specification.TripSpecifications.notDeleted;

@Repository
public interface TripRepository extends JpaRepository<Trip, Long>, JpaSpecificationExecutor<Trip> {
    default Optional<Trip> findByCodeAndDeletedAtIsNull(String code) {
        return findOne(hasCode(code).and(notDeleted()));
    }

    default List<Trip> findAllByCodeInAndDeletedAtIsNull(Set<String> codes) {
        return findAll(codeIn(codes).and(notDeleted()));
    }
}