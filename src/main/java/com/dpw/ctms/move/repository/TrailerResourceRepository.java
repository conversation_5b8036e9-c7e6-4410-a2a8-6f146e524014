package com.dpw.ctms.move.repository;

import com.dpw.ctms.move.entity.TrailerResource;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;
import java.util.Set;

import static com.dpw.ctms.move.specification.ResourceSpecifications.trailerResourceCodeIn;
import static com.dpw.ctms.move.specification.ResourceSpecifications.trailerResourceNotDeleted;

public interface TrailerResourceRepository extends JpaRepository<TrailerResource, Long>, JpaSpecificationExecutor<TrailerResource> {
    default List<TrailerResource> findAllByCodeInAndDeletedAtIsNull(Set<String> codes) {
        return findAll(trailerResourceCodeIn(codes).and(trailerResourceNotDeleted()));
    }
}
