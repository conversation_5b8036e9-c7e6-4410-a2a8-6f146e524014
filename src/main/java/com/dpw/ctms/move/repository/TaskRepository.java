package com.dpw.ctms.move.repository;

import com.dpw.ctms.move.entity.Task;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import static com.dpw.ctms.move.specification.TaskSpecifications.codeIn;
import static com.dpw.ctms.move.specification.TaskSpecifications.hasCode;
import static com.dpw.ctms.move.specification.TaskSpecifications.notDeleted;

@Repository
public interface TaskRepository extends JpaRepository<Task, Long>, JpaSpecificationExecutor<Task> {

    default Optional<Task> findByCodeAndDeletedAtIsNull(String code) {
        return findOne(hasCode(code).and(notDeleted()));
    }

    default List<Task> findAllByCodeInAndDeletedAtIsNull(Set<String> codes) {
        return findAll(codeIn(codes).and(notDeleted()));
    }
}