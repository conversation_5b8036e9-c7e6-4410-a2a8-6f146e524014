package com.dpw.ctms.move.controller.impl;

import com.dpw.ctms.move.controller.StaticApi;
import com.dpw.ctms.move.request.StaticDataRequest;
import com.dpw.ctms.move.response.ExceptionTypesResponse;
import com.dpw.ctms.move.response.StaticDataResponse;
import com.dpw.ctms.move.service.IStaticService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class StaticApiController implements StaticApi {

    private final IStaticService staticService;

    @Override
    public ResponseEntity<StaticDataResponse> getStaticData(StaticDataRequest request) {
        StaticDataResponse response = staticService.getStaticData(request);
        return ResponseEntity.ok(response);
    }

    @Override
    public ResponseEntity<ExceptionTypesResponse> getExceptionTypes() {
        ExceptionTypesResponse response = staticService.getExceptionTypes();
        return ResponseEntity.ok(response);
    }
}
