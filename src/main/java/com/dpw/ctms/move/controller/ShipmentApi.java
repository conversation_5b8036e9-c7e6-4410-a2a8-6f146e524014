package com.dpw.ctms.move.controller;

import com.dpw.ctms.move.request.ShipmentListingRequest;
import com.dpw.ctms.move.response.ListResponse;
import com.dpw.ctms.move.response.ShipmentListingResponse;
import com.dpw.ctms.move.response.ShipmentViewResponse;
import com.dpw.tmsutils.annotation.ApiLog;
import com.dpw.tmsutils.schemaobjects.TMSErrorResponseSo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RequestMapping("/v1/shipments")
public interface ShipmentApi {

    @PostMapping(value = "/list", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @PreAuthorize("@accessChecker.hasAnyPrivilegeOrRoot('CTMS_MOV_SHP_VW')")
    @Operation(summary = "Shipment listing")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully listed the shipments")
    })
    @ApiLog
    ResponseEntity<ListResponse<ShipmentListingResponse>> listShipments(
            @Valid
            @RequestBody
            @Parameter(name = "ShipmentListingRequest", required = true, description = "Request to list shipments")
            ShipmentListingRequest shipmentListingRequest);

    @GetMapping(value = "/{shipmentCode}/view", produces = MediaType.APPLICATION_JSON_VALUE)
    @PreAuthorize("@accessChecker.hasAnyPrivilegeOrRoot('CTMS_MOV_SHP_VW')")
    @Operation(summary = "Get shipment view details")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully fetched shipment view details"),
            @ApiResponse(responseCode = "422", description = "Unable to fetch shipment view details", content = {
                    @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = TMSErrorResponseSo.class)
                    )
            })
    })
    @ApiLog
    ResponseEntity<ShipmentViewResponse> getShipmentView(
            @PathVariable("shipmentCode") @Parameter(name = "shipmentCode", required = true, description = "Shipment code")
            @NotNull String shipmentCode);
}
