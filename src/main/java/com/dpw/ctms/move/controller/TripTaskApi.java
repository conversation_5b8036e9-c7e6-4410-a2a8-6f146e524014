package com.dpw.ctms.move.controller;

import com.dpw.ctms.move.request.FilterRequest;
import com.dpw.ctms.move.request.TripTaskListingRequest;
import com.dpw.ctms.move.response.ListResponse;
import com.dpw.ctms.move.response.TripFacilityTasksDetailsResponse;
import com.dpw.tmsutils.schemaobjects.TMSErrorResponseSo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

@RequestMapping("/v1/trips")
public interface TripTaskApi {
    @PostMapping(value = "/{tripCode}/tasks/get-by-facility", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @PreAuthorize("@accessChecker.hasAnyPrivilegeOrRoot('CTMS_MOV_TRP_VW', 'CTMS_MOV_SHP_VW', 'CTMS_MOV_TO_VW', 'CTMS_MOV_CO_VW')")
    @Operation(summary = "Fetches trip tasks by facility")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully fetched trip tasks by facility"),
            @ApiResponse(responseCode = "422", description = "Unable to fetch the trip task by facility", content = {
                    @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = TMSErrorResponseSo.class)
                    )
            })
    })
    ResponseEntity<ListResponse<TripFacilityTasksDetailsResponse>> getTripTasksByFacility(
            @PathVariable("tripCode") @Parameter(name = "tripCode", required = true, description = "Trip code")
            @NotNull String tripCode,
            @RequestBody
            @Parameter(name = "tripTaskListingRequest", required = true, description = "Request json to fetch paginated facility tasks")
            @Valid FilterRequest<TripTaskListingRequest> tripTaskListingRequest);
}
