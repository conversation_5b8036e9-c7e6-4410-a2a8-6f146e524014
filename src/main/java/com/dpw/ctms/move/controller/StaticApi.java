package com.dpw.ctms.move.controller;

import com.dpw.ctms.move.request.StaticDataRequest;
import com.dpw.ctms.move.response.ExceptionTypesResponse;
import com.dpw.ctms.move.response.StaticDataResponse;
import com.dpw.tmsutils.annotation.ApiLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.Valid;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

@RequestMapping("/v1")
public interface StaticApi {

    @PostMapping(value = "/static", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @Operation(
            summary = "Get static data for entity types"
    )
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "Successfully retrieved static data for requested entity types"
            ),
            @ApiResponse(
                    responseCode = "400",
                    description = "Invalid request format or validation errors"
            )
    })
    @ApiLog
    ResponseEntity<StaticDataResponse> getStaticData(
            @Valid
            @RequestBody
            @Parameter(
                    name = "StaticDataRequest",
                    required = true,
                    description = "Request containing list of entity types to retrieve static data for"
            )
            StaticDataRequest request
    );

    @GetMapping(value = "/static/exceptions/types", produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(
            summary = "Get all exception types with their subtypes",
            description = "Returns a hierarchical list of exception types and their corresponding subtypes"
    )
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "Successfully retrieved exception types and subtypes"
            ),
            @ApiResponse(
                    responseCode = "500",
                    description = "Internal server error"
            )
    })
    @ApiLog
    ResponseEntity<ExceptionTypesResponse> getExceptionTypes();
}
