package com.dpw.ctms.move.controller;

import com.dpw.ctms.move.request.EntityDocumentRequest;
import com.dpw.ctms.move.request.EntityDocumentListRequest;
import com.dpw.ctms.move.request.DocumentErrorRequest;
import com.dpw.ctms.move.response.PreSignedUrlResponse;
import com.dpw.ctms.move.response.DocumentDownloadResponse;
import com.dpw.ctms.move.response.DocumentErrorResponse;
import com.dpw.ctms.move.response.EntityDocumentResponse;
import com.dpw.ctms.move.response.FileDownloadPreSignedUrlResponse;
import com.dpw.tmsutils.annotation.ApiLog;
import com.dpw.tmsutils.schemaobjects.TMSErrorResponseSo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

@RequestMapping("/v1/documents")
public interface DocumentsApi {

    @PostMapping(value = "/trip/{tripCode}/download/bol", produces = MediaType.APPLICATION_JSON_VALUE)
    @PreAuthorize("@accessChecker.hasAnyPrivilegeOrRoot('CTMS_MOV_TRP_VW', 'CTMS_MOV_SHP_VW', 'CTMS_MOV_TO_VW', 'CTMS_MOV_CO_VW')")
    @Operation(summary = "Get trip bol download response")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully download response trip bol document"),
            @ApiResponse(responseCode = "422", description = "Unable to download trip bol document", content = {
                    @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = TMSErrorResponseSo.class)
                    )
            })
    })
    @ApiLog
    ResponseEntity<DocumentDownloadResponse> downloadTripBolDocument(
            @PathVariable("tripCode") @Parameter(name = "tripCode", required = true, description = "Trip code")
            @NotNull String tripCode);

    @GetMapping(value = "/presigned-url", produces = MediaType.APPLICATION_JSON_VALUE)
    @PreAuthorize("@accessChecker.hasAnyPrivilegeOrRoot('CTMS_MOV_TRP_VW', 'CTMS_MOV_SHP_VW', 'CTMS_MOV_TO_VW', 'CTMS_MOV_CO_VW')")
    @Operation(summary = "Get pre-signed URL for document upload")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully generated pre-signed URL"),
            @ApiResponse(responseCode = "422", description = "Unable to generate pre-signed URL", content = {
                    @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = TMSErrorResponseSo.class)
                    )
            })
    })
    @ApiLog
    ResponseEntity<PreSignedUrlResponse> getPreSignedUrl();

    @PostMapping(value = "/errors", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @PreAuthorize("@accessChecker.hasAnyPrivilegeOrRoot('CTMS_MOV_TRP_VW', 'CTMS_MOV_SHP_VW', 'CTMS_MOV_TO_VW', 'CTMS_MOV_CO_VW')")
    @Operation(summary = "Get document errors for given file identifiers")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved document errors"),
            @ApiResponse(responseCode = "400", description = "Invalid request", content = {
                    @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = TMSErrorResponseSo.class)
                    )
            })
    })
    @ApiLog
    ResponseEntity<DocumentErrorResponse> getDocumentErrors(
            @org.springframework.web.bind.annotation.RequestBody 
            @Valid
            @Parameter(description = "Request containing file identifiers to check for errors", required = true)
            DocumentErrorRequest request);

    @PostMapping(value = "/list", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @PreAuthorize("@accessChecker.hasAnyPrivilegeOrRoot('CTMS_MOV_TRP_VW', 'CTMS_MOV_SHP_VW', 'CTMS_MOV_TO_VW', 'CTMS_MOV_CO_VW')")
    @Operation(summary = "Get document details by entity code and type")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved document details"),
            @ApiResponse(responseCode = "400", description = "Invalid request", content = {
                    @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = TMSErrorResponseSo.class)
                    )
            })
    })
    @ApiLog
    ResponseEntity<EntityDocumentResponse> getDocumentsByEntity(
            @org.springframework.web.bind.annotation.RequestBody
            @Valid
            @Parameter(description = "Request containing list of entity document requests", required = true)
            EntityDocumentListRequest request);


    @GetMapping(value = "/download-urls", produces = MediaType.APPLICATION_JSON_VALUE)
    @PreAuthorize("@accessChecker.hasAnyPrivilegeOrRoot('CTMS_MOV_TRP_VW', 'CTMS_MOV_SHP_VW', 'CTMS_MOV_TO_VW', 'CTMS_MOV_CO_VW')")
    @Operation(summary = "Get presigned download URLs for file identifiers")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved presigned URLs"),
            @ApiResponse(responseCode = "400", description = "Invalid request", content = {
                    @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = TMSErrorResponseSo.class)
                    )
            })
    })
    @ApiLog
    ResponseEntity<FileDownloadPreSignedUrlResponse> getFileDownloadUrls(
            @org.springframework.web.bind.annotation.RequestParam(name = "externalDocumentIdentifiers")
            @Parameter(description = "List of external document identifiers", required = true)
            List<String> externalDocumentIdentifiers);
}
