package com.dpw.ctms.move.validator;

import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.enums.TripStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class TripValidator {
    public boolean validateTripStart(Trip initialTrip, Trip currentTrip) {
        /* Make these statuses configurable if statuses change with BU */
        return initialTrip.getStatus().equals(TripStatus.CREATED) && currentTrip.getStatus().equals(TripStatus.IN_PROGRESS);
    }

    public boolean validateTripEnd(Trip initialTrip, Trip currentTrip) {
        return initialTrip.getStatus().equals(TripStatus.IN_PROGRESS) && currentTrip.getStatus().equals(TripStatus.COMPLETED);
    }
}
