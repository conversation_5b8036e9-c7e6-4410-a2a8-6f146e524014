package com.dpw.ctms.move.validator;

import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.StopTask;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.TrailerResource;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.entity.VehicleOperatorResource;
import com.dpw.ctms.move.entity.VehicleResource;
import com.dpw.ctms.move.enums.TransportOrderStatus;
import com.dpw.ctms.move.repository.TransportOrderRepository;
import com.dpw.ctms.move.service.IShipmentService;
import com.dpw.ctms.move.service.ITaskService;
import com.dpw.ctms.move.service.ITrailerService;
import com.dpw.ctms.move.service.ITripService;
import com.dpw.ctms.move.service.IVehicleOperatorService;
import com.dpw.ctms.move.service.IVehicleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.HashSet;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@Slf4j
public class TransportOrderValidator {
    private final ITripService tripService;
    private final IShipmentService shipmentService;
    private final ITaskService taskService;
    private final IVehicleService vehicleService;
    private final ITrailerService trailerService;
    private final IVehicleOperatorService vehicleOperatorService;
    private final TransportOrderRepository transportOrderRepository;
    public boolean validateTransportOrderFTLCreation(TransportOrder transportOrder) {

        Optional<TransportOrder> existingTransportOrder = transportOrderRepository.findByCodeAndDeletedAtIsNull(
                transportOrder.getCode()
        );

        if (existingTransportOrder.isPresent()) {
            log.error("Transport order with code {} already exists and is not soft deleted", transportOrder.getCode());
            return false;
        }

        Set<String> tripCodes = transportOrder.getTrips().stream()
                .map(Trip::getCode)
                .collect(Collectors.toSet());

        Set<Trip> existingTrips = tripService.getAllByCodes(tripCodes);
        Set<String> activeTripCodes = existingTrips.stream()
                .map(Trip::getCode)
                .collect(Collectors.toSet());

        if (!activeTripCodes.isEmpty()) {
            log.error("Trips with codes {} already exist and are not soft-deleted", activeTripCodes);
            return false;
        }

        Set<String> shipmentCodes = transportOrder.getShipments().stream()
                .map(Shipment::getCode)
                .collect(Collectors.toSet());

        Set<Shipment> existingShipments = new HashSet<>(shipmentService.getAllByCodes(shipmentCodes));

        Set<String> activeShipmentCodes = existingShipments.stream()
                .map(Shipment::getCode)
                .collect(Collectors.toSet());

        if (!activeShipmentCodes.isEmpty()) {
            log.error("Shipments with codes {} already exist and are not soft-deleted", activeShipmentCodes);
            return false;
        }

        /* Currently only stop level tasks are considered. if other tasks like trip or TO are added
        * a check for that needs to be added as well */

        Set<String> taskCodes = transportOrder.getTrips().stream()
                .flatMap(trip -> trip.getStops().stream())
                .filter(Objects::nonNull)
                .flatMap(stop -> stop.getStopTasks().stream())
                .filter(Objects::nonNull)
                .map(StopTask::getTask)
                .filter(Objects::nonNull)
                .map(Task::getCode)
                .collect(Collectors.toSet());

        Set<Task> existingTasks = taskService.getAllByCodes(taskCodes);

        Set<String> activeTaskCodes = existingTasks.stream()
                .map(Task::getCode)
                .collect(Collectors.toSet());

        if (!activeTaskCodes.isEmpty()) {
            log.error("Tasks with codes {} already exist and are not soft-deleted", activeTaskCodes);
            return false;
        }

        Set<String> vehicleCodes = transportOrder.getTrips().stream()
                .map(Trip::getVehicleResource)
                .filter(Objects::nonNull)
                .map(VehicleResource::getCode)
                .collect(Collectors.toSet());

        Set<VehicleResource> existingVehicleResources = vehicleService.getAllByCodes(vehicleCodes);

        Set<String> activeVehicleCodes = existingVehicleResources.stream()
                .map(VehicleResource::getCode)
                .collect(Collectors.toSet());

        if (!activeVehicleCodes.isEmpty()) {
            log.error("Vehicle resources with codes {} already exist and are not soft-deleted", activeVehicleCodes);
            return false;
        }

        Set<String> trailerCodes = transportOrder.getTrips().stream()
                .filter(Objects::nonNull)
                .flatMap(trip -> Optional.ofNullable(trip.getTrailerResources())
                        .stream()
                        .flatMap(Collection::stream))
                .filter(Objects::nonNull)
                .map(TrailerResource::getCode)
                .collect(Collectors.toSet());

        Set<TrailerResource> existingTrailerResources = trailerService.getAllByCodes(trailerCodes);

        Set<String> activeTrailerCodes = existingTrailerResources.stream()
                .map(TrailerResource::getCode)
                .collect(Collectors.toSet());

        if (!activeTrailerCodes.isEmpty()) {
            log.error("Trailer resources with codes {} already exist and are not soft-deleted", activeTrailerCodes);
            return false;
        }

        Set<String> vehicleOperatorCodes = transportOrder.getTrips().stream()
                .filter(Objects::nonNull)
                .flatMap(trip -> Optional.ofNullable(trip.getVehicleOperatorResources())
                        .stream()
                        .flatMap(Collection::stream))
                .filter(Objects::nonNull)
                .map(VehicleOperatorResource::getCode)
                .collect(Collectors.toSet());

        Set<VehicleOperatorResource> existingVehicleOperatorResources =
                vehicleOperatorService.getAllByCodes(vehicleOperatorCodes);

        Set<String> activeVehicleOperatorCodes = existingVehicleOperatorResources.stream()
                .map(VehicleOperatorResource::getCode)
                .collect(Collectors.toSet());

        if (!activeVehicleOperatorCodes.isEmpty()) {
            log.error("Vehicle operator resources with codes {} already exist and are not soft-deleted", activeVehicleOperatorCodes);
            return false;
        }
        return true;
    }

    public boolean validateTransportOrderFTLUpdate(TransportOrder transportOrder) {
        return transportOrder.getStatus().equals(TransportOrderStatus.ASSIGNED) ||
                transportOrder.getStatus().equals(TransportOrderStatus.ALLOCATED);
    }
}
