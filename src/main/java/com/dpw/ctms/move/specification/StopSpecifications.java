package com.dpw.ctms.move.specification;

import com.dpw.ctms.move.constants.StopFieldConstants;
import com.dpw.ctms.move.constants.TripFieldConstants;
import com.dpw.ctms.move.entity.Stop;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.data.jpa.domain.Specification;

import java.util.List;

import static com.dpw.ctms.move.constants.StopFieldConstants.STATUS;
import static com.dpw.ctms.move.constants.TripFieldConstants.CODE;
import static com.dpw.ctms.move.constants.TripFieldConstants.DELETED_AT;
import static com.dpw.ctms.move.constants.TripFieldConstants.TRIP;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class StopSpecifications {
    public static Specification<Stop> tripCodeEquals(String tripCode) {
        return (root, query, cb) -> cb.equal(root.get(TRIP).get(CODE), tripCode);
    }

    public static Specification<Stop> tripNotDeleted() {
        return (root, query, cb) -> cb.isNull(root.get(TRIP).get(DELETED_AT));
    }

    public static Specification<Stop> statusNotInOrNull(List<String> statusList) {
        return (root, query, cb) -> cb.or(
                cb.isNull(root.get(STATUS)),
                cb.not(root.get(STATUS).in(statusList))
        );
    }

    public static Specification<Stop> statusNotIn(List<String> statusList) {
        return (root, query, cb) -> cb.not(root.get(STATUS).in(statusList));
    }
}
