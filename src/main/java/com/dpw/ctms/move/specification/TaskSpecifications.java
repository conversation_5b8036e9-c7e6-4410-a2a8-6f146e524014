package com.dpw.ctms.move.specification;

import com.dpw.ctms.move.constants.TaskFieldConstants;
import com.dpw.ctms.move.entity.Task;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.data.jpa.domain.Specification;

import jakarta.persistence.criteria.JoinType;
import java.util.Set;

import static com.dpw.ctms.move.constants.TaskFieldConstants.CODE;
import static com.dpw.ctms.move.constants.TaskFieldConstants.DELETED_AT;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class TaskSpecifications {
    public static Specification<Task> notDeleted() {
        return (root, query, builder) -> builder.isNull(root.get(DELETED_AT));
    }

    public static Specification<Task> hasCode(String code) {
        return (root, query, builder) -> builder.equal(root.get(CODE), code);
    }

    public static Specification<Task> codeIn(Set<String> codes) {
        return (root, query, builder) -> root.get(CODE).in(codes);
    }
}
