package com.dpw.ctms.move.specification;

import com.dpw.ctms.move.entity.TrailerResource;
import com.dpw.ctms.move.entity.VehicleOperatorResource;
import com.dpw.ctms.move.entity.VehicleResource;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.data.jpa.domain.Specification;

import java.util.Set;

import static com.dpw.ctms.move.constants.TripFieldConstants.CODE;
import static com.dpw.ctms.move.constants.TripFieldConstants.DELETED_AT;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ResourceSpecifications {
    public static Specification<VehicleResource> vehicleResourceNotDeleted() {
        return (root, query, builder) -> builder.isNull(root.get(DELETED_AT));
    }

    public static Specification<VehicleResource> vehicleResourceCodeIn(Set<String> codes) {
        return (root, query, builder) -> root.get(CODE).in(codes);
    }

    public static Specification<TrailerResource> trailerResourceNotDeleted() {
        return (root, query, builder) -> builder.isNull(root.get(DELETED_AT));
    }

    public static Specification<TrailerResource> trailerResourceCodeIn(Set<String> codes) {
        return (root, query, builder) -> root.get(CODE).in(codes);
    }

    public static Specification<VehicleOperatorResource> vehicleOperatorResourceNotDeleted() {
        return (root, query, builder) -> builder.isNull(root.get(DELETED_AT));
    }

    public static Specification<VehicleOperatorResource> vehicleOperatorResourceCodeIn(Set<String> codes) {
        return (root, query, builder) -> root.get(CODE).in(codes);
    }
}
