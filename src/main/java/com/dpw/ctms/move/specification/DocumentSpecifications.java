package com.dpw.ctms.move.specification;

import com.dpw.ctms.move.constants.DocumentFieldConstants;
import com.dpw.ctms.move.dto.GetDocumentsDto;
import com.dpw.ctms.move.entity.Document;
import com.dpw.ctms.move.enums.DocumentOperationType;
import com.dpw.ctms.move.enums.DocumentStatus;
import com.dpw.ctms.move.enums.DocumentType;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;

import java.util.List;

/**
 * Specifications for Document entity queries using JPA Criteria API.
 * This class contains reusable query specifications for Document entities.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class DocumentSpecifications {

    /**
     * Specification for finding BOL documents with specific criteria
     */
    public static Specification<Document> bolDocumentSpec(
            String entityId, String entityType, DocumentStatus status,
            DocumentOperationType operationType, String checksum, DocumentType documentType) {
        return (root, query, builder) -> {
            return builder.and(
                    builder.equal(root.get(DocumentFieldConstants.ENTITY_ID), entityId),
                    builder.equal(root.get(DocumentFieldConstants.ENTITY_TYPE), entityType),
                    builder.equal(root.get(DocumentFieldConstants.STATUS), status),
                    builder.equal(root.get(DocumentFieldConstants.DOCUMENT_OPERATION_TYPE), operationType),
                    builder.equal(root.get(DocumentFieldConstants.CHECKSUM), checksum),
                    builder.equal(root.get(DocumentFieldConstants.DOCUMENT_TYPE), documentType)
            );
        };
    }

    /**
     * Specification for finding active entity documents
     */
    public static Specification<Document> activeEntityDocumentsSpec(
            String entityId, String entityType, DocumentOperationType operationType, DocumentStatus status) {
        return (root, query, builder) -> {
            return builder.and(
                    builder.equal(root.get(DocumentFieldConstants.ENTITY_ID), entityId),
                    builder.equal(root.get(DocumentFieldConstants.ENTITY_TYPE), entityType),
                    builder.equal(root.get(DocumentFieldConstants.DOCUMENT_OPERATION_TYPE), operationType),
                    builder.equal(root.get(DocumentFieldConstants.STATUS), status)
            );
        };
    }

    /**
     * Specification for finding document by async mapping UUID
     */
    public static Specification<Document> byAsyncMappingUUIDSpec(String asyncMappingUUID) {
        return (root, query, builder) -> {
            return builder.equal(root.get(DocumentFieldConstants.ASYNC_MAPPING_UUID), asyncMappingUUID);
        };
    }

    /**
     * Specification for finding documents by list of async mapping UUIDs
     */
    public static Specification<Document> byAsyncMappingUUIDsInSpec(List<String> asyncMappingUUIDs) {
        return (root, query, builder) -> {
            return root.get(DocumentFieldConstants.ASYNC_MAPPING_UUID).in(asyncMappingUUIDs);
        };
    }

    /**
     * Specification for finding documents based on GetDocumentsDto filter criteria
     */
    public static Specification<Document> byDocumentFilter(GetDocumentsDto filter) {
        Specification<Document> spec = Specification.where(null);
        
        // Entity type
        if (StringUtils.isNotEmpty(filter.getEntityType())) {
            spec = spec.and((root, query, builder) -> 
                builder.equal(root.get(DocumentFieldConstants.ENTITY_TYPE), filter.getEntityType()));

            if (ObjectUtils.isNotEmpty(filter.getEntityIds())) {
                spec = spec.and((root, query, builder) -> 
                    root.get(DocumentFieldConstants.ENTITY_ID).in(filter.getEntityIds()));
            }
        }
        
        // Document type
        if (ObjectUtils.isNotEmpty(filter.getDocumentType())) {
            spec = spec.and((root, query, builder) -> 
                builder.equal(root.get(DocumentFieldConstants.DOCUMENT_TYPE), filter.getDocumentType()));
        }
        
        // Operation type
        if (ObjectUtils.isNotEmpty(filter.getOperationType())) {
            spec = spec.and((root, query, builder) -> 
                builder.equal(root.get(DocumentFieldConstants.DOCUMENT_OPERATION_TYPE), filter.getOperationType()));
        }
        
        // Status
        if (ObjectUtils.isNotEmpty(filter.getStatus())) {
            spec = spec.and((root, query, builder) -> 
                builder.equal(root.get(DocumentFieldConstants.STATUS), filter.getStatus()));
        }
        
        return spec;
    }
}