package com.dpw.ctms.move.specification;

import com.dpw.ctms.move.entity.TransportOrder;
import jakarta.persistence.criteria.JoinType;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.data.jpa.domain.Specification;

import static com.dpw.ctms.move.constants.TransportOrderFieldConstants.CODE;
import static com.dpw.ctms.move.constants.TransportOrderFieldConstants.DELETED_AT;
import static com.dpw.ctms.move.constants.TransportOrderFieldConstants.SHIPMENTS;
import static com.dpw.ctms.move.constants.TransportOrderFieldConstants.TRIPS;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class TransportOrderSpecifications {
    public static Specification<TransportOrder> notDeleted() {
        return (root, query, builder) -> builder.isNull(root.get(DELETED_AT));
    }

    public static Specification<TransportOrder> hasCode(String code) {
        return (root, query, builder) -> builder.equal(root.get(CODE), code);
    }

    public static Specification<TransportOrder> fetchTripsAndShipments() {
        return (root, query, builder) -> {
            if (TransportOrder.class.equals(query.getResultType())) {
                root.fetch(TRIPS, JoinType.LEFT);
                root.fetch(SHIPMENTS, JoinType.LEFT);
                query.distinct(true);
            }
            return builder.conjunction();
        };
    }
}
