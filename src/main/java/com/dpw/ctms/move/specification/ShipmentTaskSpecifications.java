package com.dpw.ctms.move.specification;

import com.dpw.ctms.move.entity.ShipmentTask;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.List;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ShipmentTaskSpecifications {

    private static final String TASK = "task";
    private static final String SHIPMENT = "shipment";
    private static final String ID = "id";
    private static final String DELETED_AT = "deletedAt";

    public static Specification<ShipmentTask> hasTaskId(Long taskId) {
        return (root, query, builder) -> builder.equal(root.get(TASK).get(ID), taskId);
    }

    public static Specification<ShipmentTask> hasTaskIds(List<Long> taskIds) {
        return ((root, query, builder) -> root.join(TASK).get(ID).in(taskIds) );
    }


    public static Specification<ShipmentTask> notDeleted() {
        return (root, query, builder) -> builder.and(
                builder.isNull(root.get(TASK).get(DELETED_AT)),
                builder.isNull(root.get(SHIPMENT).get(DELETED_AT))
        );
    }
}