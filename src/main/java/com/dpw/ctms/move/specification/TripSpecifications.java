package com.dpw.ctms.move.specification;

import com.dpw.ctms.move.entity.Trip;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.data.jpa.domain.Specification;

import java.util.Set;

import static com.dpw.ctms.move.constants.TripFieldConstants.CODE;
import static com.dpw.ctms.move.constants.TripFieldConstants.DELETED_AT;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class TripSpecifications {
    public static Specification<Trip> notDeleted() {
        return (root, query, builder) -> builder.isNull(root.get(DELETED_AT));
    }

    public static Specification<Trip> hasCode(String code) {
        return (root, query, builder) -> builder.equal(root.get(CODE), code);
    }

    public static Specification<Trip> codeIn(Set<String> codes) {
        return (root, query, builder) -> root.get(CODE).in(codes);
    }
}
