package com.dpw.ctms.move.enums;

/**
 * Enum representing specific subtypes of exceptions
 */
public enum ExceptionSubtype implements DisplayableStatusEnum {
    // Pickup Exception subtypes
    LOST("Lost", ExceptionTypeCategory.PICKUP_EXCEPTION),
    DAMAGED_GOODS("Damaged Goods", ExceptionTypeCategory.PICKUP_EXCEPTION),
    INCORRECT_QUANTITY("Incorrect Quantity", ExceptionTypeCategory.PICKUP_EXCEPTION),

    // Delivery Exception subtypes
    DELIVERY_FAILED("Delivery Failed", ExceptionTypeCategory.DELIVERY_EXCEPTION),
    WRONG_ADDRESS("Wrong Address", ExceptionTypeCategory.DELIVERY_EXCEPTION),
    RECIPIENT_UNAVAILABLE("Recipient Unavailable", ExceptionTypeCategory.DELIVERY_EXCEPTION);

    private final String displayName;
    private final ExceptionTypeCategory category;

    ExceptionSubtype(String displayName, ExceptionTypeCategory category) {
        this.displayName = displayName;
        this.category = category;
    }

    @Override
    public String getDisplayName() {
        return this.displayName;
    }

    public ExceptionTypeCategory getCategory() {
        return this.category;
    }

}
