package com.dpw.ctms.move.enums;

import java.util.Arrays;
import java.util.List;

/**
 * Enum representing different categories of exceptions with their subtypes
 */
public enum ExceptionTypeCategory implements DisplayableStatusEnum {
    PICKUP_EXCEPTION("Pickup Exception"),
    DELIVERY_EXCEPTION("Delivery Exception");

    private final String displayName;

    ExceptionTypeCategory(String displayName) {
        this.displayName = displayName;
    }

    @Override
    public String getDisplayName() {
        return this.displayName;
    }

    /**
     * Get all subtypes for this exception category
     */
    public List<ExceptionSubtype> getSubtypes() {
        return Arrays.stream(ExceptionSubtype.values())
                .filter(subtype -> subtype.getCategory() == this)
                .toList();
    }
}
