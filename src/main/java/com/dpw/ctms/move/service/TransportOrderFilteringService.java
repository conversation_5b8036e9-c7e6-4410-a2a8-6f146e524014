package com.dpw.ctms.move.service;

import com.dpw.ctms.move.constants.ShipmentFieldConstants;
import com.dpw.ctms.move.constants.TransportOrderFieldConstants;
import com.dpw.ctms.move.constants.TripFieldConstants;
import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.Stop;
import com.dpw.ctms.move.entity.TrailerResource;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.entity.VehicleOperatorResource;
import com.dpw.ctms.move.entity.VehicleResource;
import com.dpw.ctms.move.enums.ShipmentStatus;
import com.dpw.ctms.move.enums.TransportOrderStatus;
import com.dpw.ctms.move.enums.TripStatus;
import com.dpw.ctms.move.mapper.TransportOrderMapper;
import com.dpw.ctms.move.repository.TransportOrderRepository;
import com.dpw.ctms.move.request.DateRange;
import com.dpw.ctms.move.request.TransportOrderListingRequest;
import com.dpw.ctms.move.request.common.Pagination;
import com.dpw.ctms.move.request.common.Sort;
import com.dpw.ctms.move.response.ListResponse;
import com.dpw.ctms.move.response.TransportOrderListingResponse;
import com.dpw.tmsutils.exception.TMSException;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.dpw.ctms.move.constants.TransportOrderFieldConstants.DESTINATION_STOP;
import static com.dpw.ctms.move.constants.TransportOrderFieldConstants.EXTERNAL_CONSIGNMENT_ID;
import static com.dpw.ctms.move.constants.TransportOrderFieldConstants.EXTERNAL_CUSTOMER_ORDER_ID;
import static com.dpw.ctms.move.constants.TransportOrderFieldConstants.EXTERNAL_LOCATION_CODE;
import static com.dpw.ctms.move.constants.TransportOrderFieldConstants.ORIGIN_STOP;
import static com.dpw.ctms.move.constants.TransportOrderFieldConstants.SHIPMENTS;
import static com.dpw.ctms.move.constants.TransportOrderFieldConstants.TRAILER_RESOURCES;
import static com.dpw.ctms.move.constants.TransportOrderFieldConstants.TRIPS;
import static com.dpw.ctms.move.constants.TransportOrderFieldConstants.TRIP_CODE;
import static com.dpw.ctms.move.constants.TransportOrderFieldConstants.VEHICLE_OPERATOR_RESOURCES;
import static com.dpw.ctms.move.constants.TransportOrderFieldConstants.VEHICLE_RESOURCE;
import static com.dpw.ctms.move.constants.TripFieldConstants.DELETED_AT;
import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.UNKNOWN_ERROR;

@Slf4j
@Service
@RequiredArgsConstructor
public class TransportOrderFilteringService {
    private final TransportOrderMapper transportOrderMapper;
    private final TransportOrderRepository transportOrderRepository;
    // Default pagination values
    private static final int DEFAULT_PAGE_NO = 0;
    private static final int DEFAULT_PAGE_SIZE = 20;
    private static final int MAX_PAGE_SIZE = 1000;

    // Default sorting values
    private static final String DEFAULT_SORT_BY = TransportOrderFieldConstants.DEFAULT_SORT_BY;
    private static final String DEFAULT_SORT_ORDER = TransportOrderFieldConstants.DEFAULT_SORT_ORDER;

    @Transactional(readOnly = true)
    public ListResponse<TransportOrderListingResponse> filterTransportOrders(
            TransportOrderListingRequest transportOrderListingRequest) {
        try {
            log.info("Starting transport order filtering with request: {}", transportOrderListingRequest);

            // Build specifications based on filter criteria
            Specification<TransportOrder> specification = buildSpecification(transportOrderListingRequest.getFilter());

            // Create pageable object with sorting
            Pageable pageable = createPageable(transportOrderListingRequest.getPagination(),
                    transportOrderListingRequest.getSort());

            // Execute query with specifications and pagination
            Page<TransportOrder> transportOrderPage = transportOrderRepository.findAll(specification, pageable);

            Long totalElements = transportOrderPage.getTotalElements();
            log.info("Found {} transport orders matching criteria", totalElements);

            // Map entities to response DTOs
            List<TransportOrderListingResponse> transportOrderListingResponses = transportOrderPage.getContent()
                    .stream()
                    .map(transportOrderMapper::mapToResponse)
                    .collect(Collectors.toList());

            // Build and return the enhanced response with pagination metadata
            return ListResponse.<TransportOrderListingResponse>builder()
                    .data(transportOrderListingResponses)
                    .totalRecords(totalElements)
                    .build();

        } catch (Exception e) {
            log.error("Error occurred while filtering transport orders: {}", e.getMessage());
            String message = "Unknown error occurred while filtering transport orders";
            throw new TMSException(UNKNOWN_ERROR.name(), message);
        }
    }

    /**
     * Builds JPA Specification based on filter criteria
     *
     * @param filter The filter criteria
     * @return Combined specification for all filter conditions
     */
    private Specification<TransportOrder> buildSpecification(TransportOrderListingRequest.Filter filter) {
        Specification<TransportOrder> spec = Specification
                .where((root, query, builder) -> builder.isNull(root.get(TransportOrderFieldConstants.DELETED_AT)));

        if (filter == null) {
            return spec;
        }

        // Filter by transport order IDs
        if (!CollectionUtils.isEmpty(filter.getTransportOrderIds())) {
            spec = spec.and(transportOrderIdsIn(filter.getTransportOrderIds()));
        }

        // Filter by transportOrder statuses
        if (!CollectionUtils.isEmpty(filter.getTransportOrderStatuses())) {
            spec = spec.and(statusesIn(filter.getTransportOrderStatuses()));
        }

        // Filter by trip IDs
        if (!CollectionUtils.isEmpty(filter.getTripIds())) {
            spec = spec.and(tripIdsIn(filter.getTripIds()));
        }

        // Filter by shipment IDs
        if (!CollectionUtils.isEmpty(filter.getShipmentIds())) {
            spec = spec.and(shipmentIdsIn(filter.getShipmentIds()));
        }

        // Filter by consignment IDs
        if (!CollectionUtils.isEmpty(filter.getConsignmentIds())) {
            spec = spec.and(consignmentIdsIn(filter.getConsignmentIds()));
        }

        // Filter by trip statuses
        if (!CollectionUtils.isEmpty(filter.getTripStatuses())) {
            spec = spec.and(tripStatusesIn(filter.getTripStatuses()));
        }

        // Filter by shipment statuses
        if (!CollectionUtils.isEmpty(filter.getShipmentStatuses())) {
            spec = spec.and(shipmentStatusesIn(filter.getShipmentStatuses()));
        }

        // Filter by customer order IDs
        if (!CollectionUtils.isEmpty(filter.getCustomerOrderIds())) {
            spec = spec.and(customerOrderIdsIn(filter.getCustomerOrderIds()));
        }

        // Filter by assignment
        if (filter.getAssignment() != null) {
            spec = spec.and(assignmentFilter(filter.getAssignment()));
        }

        // Filter by expected pickup date range of shipment
        if (filter.getExpectedPickupDateRange() != null) {
            spec = spec.and(shipmentDateInRange(TransportOrderFieldConstants.EXPECTED_PICKUP_AT,
                    filter.getExpectedPickupDateRange()));
        }

        // Filter by expected delivery date range of shipment
        if (filter.getExpectedDeliveryDateRange() != null) {
            spec = spec.and(shipmentDateInRange(TransportOrderFieldConstants.EXPECTED_DELIVERY_AT,
                    filter.getExpectedDeliveryDateRange()));
        }

        // Filter by actual pickup date range of shipment
        if (filter.getActualPickupDateRange() != null) {
            spec = spec.and(shipmentDateInRange(TransportOrderFieldConstants.ACTUAL_PICKUP_AT,
                    filter.getActualPickupDateRange()));
        }

        // Filter by actual delivery date range of shipment
        if (filter.getActualDeliveryDateRange() != null) {
            spec = spec.and(shipmentDateInRange(TransportOrderFieldConstants.ACTUAL_DELIVERY_AT,
                    filter.getActualDeliveryDateRange()));
        }

        // Filter by origin location ID of shipment
        if (StringUtils.hasText(filter.getOriginLocationId())) {
            spec = spec.and(shipmentOriginLocationEquals(filter.getOriginLocationId()));
        }

        // Filter by destination location ID of shipment
        if (StringUtils.hasText(filter.getDestinationLocationId())) {
            spec = spec.and(shipmentDestinationLocationEquals(filter.getDestinationLocationId()));
        }

        if (!CollectionUtils.isEmpty(filter.getVehicleIds())) {
            spec = spec.and(tripVehiclesIn(filter.getVehicleIds()));
        }

        if (!CollectionUtils.isEmpty(filter.getVehicleTypes())) {
            spec = spec.and(tripVehicleTypesIn(filter.getVehicleTypes()));
        }

        if (!CollectionUtils.isEmpty(filter.getTrailerIds())) {
            spec = spec.and(tripTrailerIdsIn(filter.getTrailerIds()));
        }

        if (!CollectionUtils.isEmpty(filter.getVehicleOperatorIds())) {
            spec = spec.and(tripVehicleOperatorIdsIn(filter.getVehicleOperatorIds()));
        }

        if (filter.getIsPodAttached() != null) {
            spec = spec.and(isPodAttached(filter.getIsPodAttached()));
        }


        return spec;
    }

    private Specification<TransportOrder> transportOrderIdsIn(List<String> transportOrderIds) {
        return (root, query, builder) -> root.get(TransportOrderFieldConstants.CODE).in(transportOrderIds);
    }

    private Specification<TransportOrder> statusesIn(List<String> statuses) {
        return (root, query, builder) -> {
            List<TransportOrderStatus> transportOrderStatuses = statuses.stream()
                    .map(status -> {
                        try {
                            return TransportOrderStatus.valueOf(status.toUpperCase());
                        } catch (IllegalArgumentException e) {
                            log.warn("Invalid transportOrder status: {}", status);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            return transportOrderStatuses.isEmpty() ? builder.disjunction()
                    : root.get(TransportOrderFieldConstants.STATUS).in(transportOrderStatuses);
        };
    }

    private Specification<TransportOrder> shipmentIdsIn(List<String> shipmentIds) {
        return (root, query, builder) -> {
            query.distinct(true);
            Join<TransportOrder, Shipment> shipmentJoin = root.join(TransportOrderFieldConstants.SHIPMENTS,
                    JoinType.INNER);
            return builder.and(
                    shipmentJoin.get(TransportOrderFieldConstants.SHIPMENT_CODE).in(shipmentIds),
                    builder.isNull(shipmentJoin.get(TransportOrderFieldConstants.DELETED_AT)));
        };
    }

    private Specification<TransportOrder> tripIdsIn(List<String> tripIds) {
        return (root, query, builder) -> {
            query.distinct(true);
            Join<TransportOrder, Trip> tripJoin = root.join(TRIPS, JoinType.INNER);
            return builder.and(
                    tripJoin.get(TRIP_CODE).in(tripIds),
                    builder.isNull(tripJoin.get(TransportOrderFieldConstants.DELETED_AT)));
        };
    }

    private Specification<TransportOrder> tripStatusesIn(List<String> tripStatuses) {
        return (root, query, builder) -> {
            query.distinct(true);
            Join<TransportOrder, Trip> tripJoin = root.join(TRIPS, JoinType.INNER);

            List<TripStatus> statuses = tripStatuses.stream()
                    .map(status -> {
                        try {
                            return TripStatus.valueOf(status.toUpperCase());
                        } catch (IllegalArgumentException e) {
                            log.warn("Invalid trip status: {}", status);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            return statuses.isEmpty() ? builder.disjunction()
                    : builder.and(
                            tripJoin.get(TransportOrderFieldConstants.STATUS).in(statuses),
                            builder.isNull(tripJoin.get(TransportOrderFieldConstants.DELETED_AT)));
        };
    }

    private Specification<TransportOrder> shipmentStatusesIn(List<String> shipmentStatuses) {
        return (root, query, builder) -> {
            query.distinct(true);
            Join<TransportOrder, Shipment> shipmentJoin = root.join(SHIPMENTS, JoinType.INNER);
            List<ShipmentStatus> statuses = shipmentStatuses.stream()
                    .map(status -> {
                        try {
                            return ShipmentStatus.valueOf(status.toUpperCase());
                        } catch (IllegalArgumentException e) {
                            log.warn("Invalid shipment status {}", status);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            return statuses.isEmpty() ? builder.disjunction()
                    : builder.and(
                            shipmentJoin.get(TransportOrderFieldConstants.STATUS).in(statuses),
                            builder.isNull(shipmentJoin.get(TransportOrderFieldConstants.DELETED_AT)));
        };
    }

    private Specification<TransportOrder> consignmentIdsIn(List<String> consignmentIds) {
        return (root, query, builder) -> {
            query.distinct(true);
            Join<TransportOrder, Shipment> shipmentJoin = root.join(SHIPMENTS, JoinType.INNER);
            return builder.and(
                    shipmentJoin.get(EXTERNAL_CONSIGNMENT_ID).in(consignmentIds),
                    builder.isNull(shipmentJoin.get(TransportOrderFieldConstants.DELETED_AT)));
        };
    }

    private Specification<TransportOrder> customerOrderIdsIn(List<String> customerOrderIds) {
        return (root, query, builder) -> {
            query.distinct(true);
            Join<TransportOrder, Shipment> shipmentJoin = root.join(SHIPMENTS, JoinType.INNER);
            return builder.and(
                    shipmentJoin.get(EXTERNAL_CUSTOMER_ORDER_ID).in(customerOrderIds),
                    builder.isNull(shipmentJoin.get(TransportOrderFieldConstants.DELETED_AT)));
        };
    }

    private Specification<TransportOrder> assignmentFilter(TransportOrderListingRequest.Assignment assignment) {
        return (root, query, builder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // Filter by assignment types if provided
            if (!CollectionUtils.isEmpty(assignment.getTypes())) {
                predicates.add(
                        root.get(TransportOrderFieldConstants.ASSIGNMENT_TYPE)
                                .in(assignment.getTypes())
                );
            }

            // Filter by identifiers if provided
            if (!CollectionUtils.isEmpty(assignment.getIdentifiers())) {
                // Validate identifiers
                List<String> validIdentifiers = assignment.getIdentifiers().stream()
                        .filter(id -> {
                            if (!StringUtils.hasText(id)) {
                                log.info("Invalid assignment identifier: {}", id);
                                return false;
                            }
                            return true;
                        })
                        .collect(Collectors.toList());

                if (validIdentifiers.isEmpty()) {
                    log.info("No valid assignment identifiers found");
                    return builder.disjunction();
                }

                predicates.add(root.get(TransportOrderFieldConstants.ASSIGNEE_IDENTIFIER).in(validIdentifiers));
            }

            return predicates.isEmpty() ? builder.conjunction() : builder.and(predicates.toArray(new Predicate[0]));
        };
    }

    private Specification<TransportOrder> shipmentDateInRange(String shipmentDateField, DateRange dateRange) {
        return (root, query, builder) -> {
            Join<TransportOrder, Shipment> shipmentJoin = root.join(SHIPMENTS, JoinType.INNER);
            query.distinct(true);
            if (dateRange.getFrom() != null && dateRange.getTo() != null) {
                return builder.and(
                        builder.greaterThanOrEqualTo(shipmentJoin.get(shipmentDateField).get("epoch"), dateRange.getFrom()),
                        builder.lessThanOrEqualTo(shipmentJoin.get(shipmentDateField).get("epoch"), dateRange.getTo()),
                        builder.isNull(shipmentJoin.get(TransportOrderFieldConstants.DELETED_AT)));
            } else if (dateRange.getFrom() != null) {
                return builder.and(
                        builder.greaterThanOrEqualTo(shipmentJoin.get(shipmentDateField).get("epoch"), dateRange.getFrom()),
                        builder.isNull(shipmentJoin.get(TransportOrderFieldConstants.DELETED_AT)));
            } else if (dateRange.getTo() != null) {
                return builder.and(
                        builder.lessThanOrEqualTo(shipmentJoin.get(shipmentDateField).get("epoch"), dateRange.getTo()),
                        builder.isNull(shipmentJoin.get(TransportOrderFieldConstants.DELETED_AT)));
            }
            return builder.isNull(shipmentJoin.get(TransportOrderFieldConstants.DELETED_AT));
        };
    }

    private Specification<TransportOrder> shipmentOriginLocationEquals(String originLocationCode) {
        return (root, query, builder) -> {
            query.distinct(true);
            Join<TransportOrder, Shipment> shipmentJoin = root.join(SHIPMENTS, JoinType.INNER);
            Join<Shipment, Stop> originStopJoin = shipmentJoin.join(ORIGIN_STOP, JoinType.INNER);
            return builder.and(
                    builder.equal(originStopJoin.get(EXTERNAL_LOCATION_CODE), originLocationCode),
                    builder.isNull(shipmentJoin.get(TransportOrderFieldConstants.DELETED_AT)));
        };
    }

    private Specification<TransportOrder> shipmentDestinationLocationEquals(String destinationLocationCode) {
        return (root, query, builder) -> {
            query.distinct(true);
            Join<TransportOrder, Shipment> shipmentJoin = root.join(SHIPMENTS, JoinType.INNER);
            Join<Shipment, Stop> destinationStopJoin = shipmentJoin.join(DESTINATION_STOP, JoinType.INNER);
            return builder.and(
                    builder.equal(destinationStopJoin.get(EXTERNAL_LOCATION_CODE), destinationLocationCode),
                    builder.isNull(shipmentJoin.get(TransportOrderFieldConstants.DELETED_AT)));
        };
    }

    private Specification<TransportOrder> tripVehicleTypesIn(List<String> vehicleTypes) {
        return (root, query, builder) -> {
            query.distinct(true);
            Join<TransportOrder, Trip> tripJoin = root.join(TransportOrderFieldConstants.TRIPS, JoinType.INNER);
            Join<Trip, VehicleResource> tripVehicleResourceJoin = tripJoin.join(VEHICLE_RESOURCE, JoinType.INNER);
            return builder.and(
                    tripVehicleResourceJoin.get(TransportOrderFieldConstants.EXTERNAL_VEHICLE_TYPE_ID).in(vehicleTypes),
                    builder.isNull(tripJoin.get(TransportOrderFieldConstants.DELETED_AT)),
                    builder.isNull(tripVehicleResourceJoin.get(TransportOrderFieldConstants.DELETED_AT)));
        };
    }

    private Specification<TransportOrder> tripVehiclesIn(List<String> vehicleIds) {
        return (root, query, builder) -> {
            query.distinct(true);
            Join<TransportOrder, Trip> tripJoin = root.join(TransportOrderFieldConstants.TRIPS, JoinType.INNER);
            Join<Trip, VehicleResource> tripVehicleResourceJoin = tripJoin.join(VEHICLE_RESOURCE, JoinType.INNER);
            return builder.and(
                    tripVehicleResourceJoin.get(TransportOrderFieldConstants.VEHICLE_EXTERNAL_RESOURCE_ID)
                            .in(vehicleIds),
                    builder.isNull(tripJoin.get(TransportOrderFieldConstants.DELETED_AT)),
                    builder.isNull(tripVehicleResourceJoin.get(TransportOrderFieldConstants.DELETED_AT)));
        };
    }

    private Specification<TransportOrder> tripTrailerIdsIn(List<String> trailerIds) {
        return (root, query, builder) -> {
            query.distinct(true);
            Join<TransportOrder, Trip> tripJoin = root.join(TransportOrderFieldConstants.TRIPS, JoinType.INNER);
            Join<Trip, TrailerResource> tripTrailerResourceJoin = tripJoin.join(TRAILER_RESOURCES, JoinType.INNER);
            return builder.and(
                    tripTrailerResourceJoin.get(TransportOrderFieldConstants.TRAILER_EXTERNAL_RESOURCE_ID)
                            .in(trailerIds),
                    builder.isNull(tripJoin.get(TransportOrderFieldConstants.DELETED_AT)),
                    builder.isNull(tripTrailerResourceJoin.get(TransportOrderFieldConstants.DELETED_AT)));
        };
    }

    private Specification<TransportOrder> tripVehicleOperatorIdsIn(List<String> vehicleOperatorIds) {
        return (root, query, builder) -> {
            query.distinct(true);
            Join<TransportOrder, Trip> tripJoin = root.join(TransportOrderFieldConstants.TRIPS, JoinType.INNER);
            Join<Trip, VehicleOperatorResource> vehicleOperatorResourceJoin = tripJoin.join(VEHICLE_OPERATOR_RESOURCES,
                    JoinType.INNER);
            return builder.and(
                    vehicleOperatorResourceJoin.get(TransportOrderFieldConstants.VEHICLE_OPERATOR_RESOURCE_ID)
                            .in(vehicleOperatorIds),
                    builder.isNull(tripJoin.get(TransportOrderFieldConstants.DELETED_AT)),
                    builder.isNull(vehicleOperatorResourceJoin.get(TransportOrderFieldConstants.DELETED_AT)));
        };
    }

    private Specification<TransportOrder> isPodAttached(boolean isPodAttached) {
        return (root, query, builder) -> {
            Join<Trip, Shipment> operatorJoin = root.join(TripFieldConstants.SHIPMENTS, JoinType.INNER);
            return builder.and(
                    operatorJoin.get(ShipmentFieldConstants.IS_DOCUMENT_ATTACHED).in(isPodAttached),
                    builder.isNull(operatorJoin.get(DELETED_AT)));
        };
    }

    /**
     * Creates Pageable object with proper validation and defaults
     */
    private Pageable createPageable(Pagination pagination,
            Sort sortRequest) {
        // Handle pagination
        int pageNo = DEFAULT_PAGE_NO;
        int pageSize = DEFAULT_PAGE_SIZE;

        if (pagination != null) {
            pageNo = Math.max(0, pagination.getPageNo());
            pageSize = pagination.getPageSize() > 0 ? Math.min(pagination.getPageSize(), MAX_PAGE_SIZE)
                    : DEFAULT_PAGE_SIZE;
        }

        // Handle sorting
        org.springframework.data.domain.Sort sort = createSort(sortRequest);

        return PageRequest.of(pageNo, pageSize, sort);
    }

    private org.springframework.data.domain.Sort createSort(Sort sortRequest) {
        String sortBy = DEFAULT_SORT_BY;
        org.springframework.data.domain.Sort.Direction direction = org.springframework.data.domain.Sort.Direction
                .fromString(DEFAULT_SORT_ORDER);

        if (sortRequest != null) {
            if (StringUtils.hasText(sortRequest.getSortBy())) {
                sortBy = validateAndMapSortField(sortRequest.getSortBy());
            }

            if (StringUtils.hasText(sortRequest.getSortOrder())) {
                try {
                    direction = org.springframework.data.domain.Sort.Direction.fromString(sortRequest.getSortOrder());
                } catch (IllegalArgumentException e) {
                    log.warn("Invalid sort order: {}, using default DESC", sortRequest.getSortOrder());
                }
            }
        }
        return org.springframework.data.domain.Sort.by(direction, sortBy);
    }

    /**
     * Validates and maps sort field names to actual entity fields
     */
    private String validateAndMapSortField(String sortBy) {
        Map<String, String> fieldMapping = Map.ofEntries(
                Map.entry(TransportOrderFieldConstants.SortFields.API_CODE, TransportOrderFieldConstants.CODE),
                Map.entry(TransportOrderFieldConstants.SortFields.API_STATUS, TransportOrderFieldConstants.STATUS),
                Map.entry(TransportOrderFieldConstants.SortFields.API_CREATED_AT,
                        TransportOrderFieldConstants.CREATED_AT),
                Map.entry(TransportOrderFieldConstants.SortFields.API_UPDATED_AT,
                        TransportOrderFieldConstants.UPDATED_AT),
                Map.entry(TransportOrderFieldConstants.SortFields.API_CREATED_BY,
                        TransportOrderFieldConstants.CREATED_BY),
                Map.entry(TransportOrderFieldConstants.SortFields.API_UPDATED_BY,
                        TransportOrderFieldConstants.UPDATED_BY),

                Map.entry(TransportOrderFieldConstants.SortFields.API_ASSIGNMENT_TYPE,
                        TransportOrderFieldConstants.ASSIGNMENT_TYPE));

        String mappedField = fieldMapping.get(sortBy);
        if (mappedField != null) {
            log.info("Mapped sort field '{}' to entity field '{}'", sortBy, mappedField);
            return mappedField;
        } else {
            log.warn("Invalid sort field '{}', using default '{}'", sortBy, DEFAULT_SORT_BY);
            return DEFAULT_SORT_BY;
        }
    }


}
