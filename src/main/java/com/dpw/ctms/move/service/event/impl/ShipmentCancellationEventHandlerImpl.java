package com.dpw.ctms.move.service.event.impl;

import com.dpw.ctms.move.dto.producer.ShipmentCancellationEventRequestDTO;
import com.dpw.ctms.move.request.common.IntegratorMessageHeader;
import com.dpw.ctms.move.request.common.IntegratorMessageRequest;
import com.dpw.ctms.move.request.common.MessageRequest;
import com.dpw.ctms.move.request.message.ShipmentCancellationAckMessage;
import com.dpw.ctms.move.response.message.IntegratorMessageResponse;
import com.dpw.ctms.move.service.IEventProcessorService;
import com.dpw.ctms.move.service.event.IEntityCancellationEventHandler;
import com.dpw.ctms.move.service.event.IEntityCancellationEventManager;
import com.dpw.tmsutils.annotation.LogExecutionTime;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import static com.dpw.ctms.move.constants.MoveConstants.SOURCE_MOVE;
import static com.dpw.ctms.move.enums.StateMachineEntityType.SHIPMENT;
import static com.dpw.ctms.move.enums.MessageActionType.SHIPMENT_CANCELLATION_CONFIRMATION_EVENT;

@RequiredArgsConstructor
@Service
@Slf4j
@LogExecutionTime
public class ShipmentCancellationEventHandlerImpl implements IEntityCancellationEventHandler {
    private final IEventProcessorService<ShipmentCancellationAckMessage> eventProcessorService;
    private final IEntityCancellationEventManager entityCancellationEventManager;

    @Value("${kafka.producer.topics.ctms-move-shipment-cancellation-ack-events.name}")
    private String shipmentCancellationAckTopicName;

    @PostConstruct
    public void register() {
        entityCancellationEventManager.register(SHIPMENT.name(), this);
    }

    @Override
    public IntegratorMessageResponse acknowledgeEntityCancellationEvent(ShipmentCancellationEventRequestDTO shipmentCancellationEventRequestDTO) {
        try {
            ShipmentCancellationAckMessage shipmentCancellationAckMessage = ShipmentCancellationAckMessage.builder()
                    .shipmentCodes(shipmentCancellationEventRequestDTO.getShipmentCodes())
                    .isCancelled(shipmentCancellationEventRequestDTO.getIsCancelled())
                    .updatedBy(shipmentCancellationEventRequestDTO.getUpdatedBy())
                    .updatedAt(shipmentCancellationEventRequestDTO.getUpdatedAt())
                    .comments(shipmentCancellationEventRequestDTO.getComments())
                    .build();

            IntegratorMessageRequest<ShipmentCancellationAckMessage> messageRequest =
                    IntegratorMessageRequest.<ShipmentCancellationAckMessage>builder()
                            .transactionContext(IntegratorMessageHeader.builder()
                                    .action(SHIPMENT_CANCELLATION_CONFIRMATION_EVENT.name())
                                    .dateTime(System.currentTimeMillis())
                                    .source(SOURCE_MOVE)
                                    .topic(shipmentCancellationAckTopicName)
                                    .correlationId(shipmentCancellationEventRequestDTO.getCorrelationId())
                                    .build())
                            .message(MessageRequest.<ShipmentCancellationAckMessage>builder().
                                    item(shipmentCancellationAckMessage).build())
                            .build();

            IntegratorMessageResponse response =  eventProcessorService.processRequest(SHIPMENT_CANCELLATION_CONFIRMATION_EVENT.name(),messageRequest);
            log.info("Successfully published Kafka message for shipment cancellation ack: shipmentCodes={}, isCancelled={}",
                   shipmentCancellationAckMessage.getShipmentCodes(), shipmentCancellationAckMessage.getIsCancelled());

            return response;

        } catch (Exception e) {
            log.error("Failed to publish Kafka message for shipment status cancellation ack: shipmentCodes={}, isCancelled={}, error={}",
                    shipmentCancellationEventRequestDTO.getShipmentCodes(), shipmentCancellationEventRequestDTO.getIsCancelled(), e.getMessage(), e);
        }
        return   IntegratorMessageResponse.builder().build();
    }
}
