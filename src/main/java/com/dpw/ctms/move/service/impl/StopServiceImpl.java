package com.dpw.ctms.move.service.impl;

import com.dpw.ctms.move.entity.Stop;
import com.dpw.ctms.move.enums.StopStatus;
import com.dpw.ctms.move.service.IStopService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Set;

@RequiredArgsConstructor
@Service
public class StopServiceImpl implements IStopService {
    public void discardStops(Set<Stop> stops) {
        stops.forEach(stop -> {
            stop.setStatus(StopStatus.DISCARDED);
            stop.setDeletedAt(System.currentTimeMillis());
        });
    }
}
