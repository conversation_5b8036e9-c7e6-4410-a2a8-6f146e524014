package com.dpw.ctms.move.service.document.generator;

import com.dpw.ctms.move.config.ConfigService;
import com.dpw.ctms.move.constants.ConfigConstants;
import com.dpw.ctms.move.dto.FacilityDetailsDTO;
import com.dpw.ctms.move.dto.PaginationDTO;
import com.dpw.ctms.move.util.DateTimeUtil;
import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.Stop;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.enums.DocumentType;
import com.dpw.ctms.move.enums.Tenant;
import com.dpw.ctms.move.integration.service.IOmsIntegratorService;
import com.dpw.ctms.move.integration.service.IResourceIntegratorService;
import com.dpw.ctms.move.integration.dto.oms.OmsConsignmentDto;
import com.dpw.ctms.move.integration.dto.resource.ResourceFacilitiesDto;
import com.dpw.ctms.move.integration.dto.resource.ResourceProductDetailsDto;
import com.dpw.ctms.move.integration.dto.resource.ResourceUomDto;
import com.dpw.ctms.move.mapper.DocumentContextMapper;
import com.dpw.ctms.move.dto.ConsignmentDetailsDTO;
import com.dpw.ctms.move.dto.document.DocumentGenerationContextDTO;
import com.dpw.ctms.move.dto.ProductDetailsDTO;
import com.dpw.ctms.move.dto.document.BOLDocumentDataDTO;
import com.dpw.ctms.move.dto.document.TripDocumentDTO;
import com.dpw.ctms.move.service.ITripDataService;
import com.dpw.ctms.move.service.document.DocumentGenerator;
import com.dpw.ctms.move.util.JsonUtils;
import com.dpw.ctms.move.constants.ErrorMessageConstant;
import com.dpw.tmsutils.async.ContextAwarePoolExecutor;
import com.dpw.tmsutils.exception.GenericException;
import com.dpw.tmsutils.exception.TMSException;
import com.dpw.tmsutils.utils.TMSExceptionErrorCode;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.bazaarvoice.jolt.Chainr;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.dpw.ctms.move.constants.ErrorMessageConstant.JOLT_SPECS_INVALID;
import static com.dpw.ctms.move.constants.PropertyConstants.DEFAULT_TIME_ZONE_ID;
import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.ERR_REQUEST_NOT_PROCESSED;

@Service
@RequiredArgsConstructor
@Slf4j
public class BOLDocumentGenerator implements DocumentGenerator<BOLDocumentDataDTO> {

    private final ITripDataService tripDataService;
    private final IOmsIntegratorService omsIntegratorService;
    private final IResourceIntegratorService resourceIntegratorService;
    private final DocumentContextMapper documentContextMapper;
    private final ContextAwarePoolExecutor executor;
    private final ConfigService configService;
    private final DateTimeUtil dateTimeUtil;

    @Override
    public DocumentType getDocumentType() {
        return DocumentType.BOL;
    }

    @Override
    public Object generateJson(String tripCode, Tenant tenant) {
        log.info("Starting JSON generation for tripCode: {}, vendor: {}", tripCode, tenant);
        
        try {
            // Generate document context
            DocumentGenerationContextDTO<BOLDocumentDataDTO> context = generateContext(tripCode, tenant).get();

            // Get and validate document configuration (templateId + JOLT config)
            JsonNode documentConfig = getValidatedDocumentConfig(tenant);
            JsonNode joltConfig = documentConfig.get("joltConfig");
            List<Object> joltSpecList = JsonUtils.fromJsonNode(joltConfig, new TypeReference<List<Object>>() {});

            // Create JOLT transformer
            Chainr chainr = createJoltTransformer(joltSpecList, tripCode, tenant);

            // Transform document data
            Object transformedObject = performJoltTransformation(chainr, context.getDocumentData(), tripCode);

            log.info("Successfully generated JSON for tripCode: {}", tripCode);
            return transformedObject;

        } catch (Exception e) {
            return handleJsonGenerationError(e, tripCode, tenant);
        }
    }

    private JsonNode getValidatedDocumentConfig(Tenant tenant) {
        JsonNode documentConfig = configService.getConfig(ConfigConstants.BOL_CONFIG, tenant);
        if (documentConfig == null || documentConfig.get("joltConfig") == null) {
            throw new TMSException(TMSExceptionErrorCode.INVALID_DATA.name(),
                    String.format(JOLT_SPECS_INVALID, tenant));
        }
        return documentConfig;
    }

    private Chainr createJoltTransformer(List<Object> joltSpecList, String tripCode, Tenant tenant) {
        if (joltSpecList == null || joltSpecList.isEmpty()) {
            throw new TMSException(TMSExceptionErrorCode.INVALID_DATA.name(),
                    String.format(JOLT_SPECS_INVALID, tenant));
        }

        try {
            Chainr chainr = Chainr.fromSpec(joltSpecList);
            if (chainr == null) {
                throw new TMSException(ERR_REQUEST_NOT_PROCESSED.name(),
                        ErrorMessageConstant.JOLT_TRANSFORMATION_FAILED);
            }
            return chainr;
        } catch (Exception e) {
            throw new TMSException(ERR_REQUEST_NOT_PROCESSED.name() ,ErrorMessageConstant.JOLT_TRANSFORMATION_FAILED + e.getMessage());
        }
    }

    private Object performJoltTransformation(Chainr chainr, BOLDocumentDataDTO documentData, String tripCode) {
        try {
            String documentDataJson = JsonUtils.toPrettyJson(documentData);
            Object documentDataObject = JsonUtils.fromJson(documentDataJson, Object.class);

            Object result = chainr.transform(documentDataObject);

            if (result == null) {
                result = new HashMap<>();
            }

            return result;

        } catch (Exception e) {
            throw new TMSException(ERR_REQUEST_NOT_PROCESSED.name(),
                    String.format(ErrorMessageConstant.JOLT_TRANSFORMATION_FAILED, tripCode, e.getMessage()));
        }
    }

    private Object handleJsonGenerationError(Exception e, String tripCode, Tenant tenant) {
        log.error("Failed to generate JSON for tripCode: {}, vendor: {}", tripCode, tenant, e);

        throw switch (e) {
            case TMSException tmsException -> tmsException;
            case GenericException genericException -> genericException;
            case ExecutionException execException -> handleExecutionException(execException, tripCode);
            case InterruptedException interruptedException -> {
                Thread.currentThread().interrupt();
                yield new GenericException("Thread interrupted: " + interruptedException.getMessage());
            }
            default -> new GenericException(
                    String.format(ErrorMessageConstant.DOCUMENT_GENERATION_FAILED, tripCode), 
                    HttpStatus.INTERNAL_SERVER_ERROR, e);
        };
    }
    
    private RuntimeException handleExecutionException(ExecutionException e, String context) {
        Throwable cause = e.getCause();
        return switch (cause) {
            case TMSException tmsException -> tmsException;
            case GenericException genericException -> genericException;
            case RuntimeException runtimeException -> new TMSException(ERR_REQUEST_NOT_PROCESSED.name(), 
                    "Failed to fetch " + context + ": " + runtimeException.getMessage());
            case null, default -> new GenericException(
                    String.format(ErrorMessageConstant.DOCUMENT_GENERATION_FAILED, context), 
                    HttpStatus.INTERNAL_SERVER_ERROR, cause);
        };
    }

    @Override
    public CompletableFuture<DocumentGenerationContextDTO<BOLDocumentDataDTO>> generateContext(
            String tripCode, Tenant tenant) {
        log.info("Starting context generation for tripCode: {}, vendor: {}", tripCode, tenant);
        
        return buildDocumentData(tripCode)
                .thenApply(data -> {
                    DocumentGenerationContextDTO<BOLDocumentDataDTO> context = DocumentGenerationContextDTO.<BOLDocumentDataDTO>builder()
                            .tenant(tenant)
                            .documentType(getDocumentType())
                            .documentData(data)
                            .build();
                    log.info("Completed context generation for tripCode: {}", tripCode);
                    return context;
                });
    }

    private CompletableFuture<BOLDocumentDataDTO> buildDocumentData(String tripCode) {
        return CompletableFuture.supplyAsync(() -> fetchTrip(tripCode), executor)
                .thenCompose(trip -> {
                    BOLDocumentDataDTO.BOLDocumentDataDTOBuilder builder = initBuilder(trip);
                    BOLDocumentDataDTO tempData = builder.build();
                    
                    List<String> externalConsignmentIds = tempData.getExternalConsignmentIds();
                    List<String> externalFacilityCodes = tempData.getExternalFacilityCodes();
                    
                    CompletableFuture<Map<String, ConsignmentDetailsDTO>> consFuture = fetchConsignmentList(externalConsignmentIds);
                    CompletableFuture<Map<String, FacilityDetailsDTO>> facFuture = fetchFacilityList(externalFacilityCodes);
                    
                    return consFuture.thenCombine(facFuture, (consMap, facMap) -> {
                        // Extract timezone information from consignments
                        String pickUpTimeZoneId = extractPickUpTimeZoneId(consMap);
                        String deliveryTimeZoneId = extractDeliveryTimeZoneId(consMap);

                        // Build trip details with timezone-aware date conversion
                        TripDocumentDTO tripDetails = buildTripDetailsWithDateConversion(trip, pickUpTimeZoneId, deliveryTimeZoneId);
                        
                        return tempData.toBuilder()
                                .tripDetails(tripDetails)
                                .consignmentDetailsMap(consMap)
                                .facilityDetailsMap(facMap)
                                .pickUpTimeZoneId(pickUpTimeZoneId)
                                .deliveryTimeZoneId(deliveryTimeZoneId)
                                .build();
                    });
                });
    }

    private Trip fetchTrip(String tripCode) {
        try {
            return tripDataService.getTripByCodeWithAllDetails(tripCode);
        } catch (TMSException e) {
            log.error("Failed to fetch trip with code: {}", tripCode, e);
            throw e;
        }
    }

    private BOLDocumentDataDTO.BOLDocumentDataDTOBuilder initBuilder(Trip trip) {
        List<Long> shipmentIds = trip.getShipments().stream()
                .map(Shipment::getId).distinct().toList();
        List<String> externalConsignmentIds = trip.getShipments().stream()
                .map(Shipment::getExternalConsignmentId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        List<String> externalFacilityCodes = Stream.concat(
                        trip.getStops().stream().map(Stop::getExternalLocationCode),
                        Stream.of(
                                trip.getExternalOriginLocationCode(),
                                trip.getExternalDestinationLocationCode()))
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        return BOLDocumentDataDTO.builder()
                .shipmentIds(shipmentIds.stream().sorted().toList())
                .externalConsignmentIds(externalConsignmentIds.stream().sorted().toList())
                .externalFacilityCodes(externalFacilityCodes);
    }

    private CompletableFuture<Map<String, ConsignmentDetailsDTO>> fetchConsignmentList(List<String> externalConsignmentIds) {
        return CompletableFuture.supplyAsync(() -> {
            if (ObjectUtils.isEmpty(externalConsignmentIds)) {
                return Collections.emptyMap();
            }

            List<OmsConsignmentDto> omsConsignmentDTOs = omsIntegratorService.getOmsConsignmentDtos(externalConsignmentIds);
            
            // Extract product IDs and UOM IDs from OMS data
            List<String> resourceProductIds = omsConsignmentDTOs.stream()
                    .flatMap(dto -> dto.getProducts().stream())
                    .map(product -> String.valueOf(product.getResourceId()))
                    .filter(Objects::nonNull)
                    .distinct()
                    .toList();
            
            List<String> uomIds = omsConsignmentDTOs.stream()
                    .flatMap(dto -> dto.getProducts().stream())
                    .flatMap(product -> product.getProperties().stream())
                    .map(property -> String.valueOf(property.getUnitOfMeasurementId()))
                    .filter(Objects::nonNull)
                    .distinct()
                    .toList();
            
            // Make parallel calls to fetch resource product details and UOM details
            CompletableFuture<List<ResourceProductDetailsDto>> productFuture = 
                    CompletableFuture.supplyAsync(() -> resourceIntegratorService.getProductDetailsDTOs(resourceProductIds), executor);
            
            CompletableFuture<List<ResourceUomDto>> uomFuture = 
                    CompletableFuture.supplyAsync(() -> resourceIntegratorService.getUomDTOs(uomIds), executor);
            
            try {
                // Wait for both calls to complete
                List<ResourceProductDetailsDto> resourceProductDetailsDTOs = productFuture.get();
                List<ResourceUomDto> resourceUomDTOs = uomFuture.get();
                
                // Create lookup maps for efficient access
                Map<String, ResourceProductDetailsDto> productMap = resourceProductDetailsDTOs.stream()
                        .collect(Collectors.toMap(
                                product -> String.valueOf(product.getId()),
                                Function.identity(),
                                (existing, replacement) -> existing));
                
                Map<String, ResourceUomDto> uomMap = resourceUomDTOs.stream()
                        .collect(Collectors.toMap(
                                uom -> String.valueOf(uom.getId()),
                                Function.identity(),
                                (existing, replacement) -> existing));
                
                // Use DocumentContextMapper to combine OMS, Resource, and UOM data
                Map<String, ConsignmentDetailsDTO> result = omsConsignmentDTOs.stream()
                        .map(omsDto -> documentContextMapper.mapToConsignmentDetailsDTO(omsDto, productMap, uomMap))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toMap(
                                ConsignmentDetailsDTO::getConsignmentId,
                                Function.identity(),
                                (existing, replacement) -> existing));
                
                return result;
                
            } catch (ExecutionException e) {
                log.error("Error fetching product details and UOM data in parallel", e);
                throw handleExecutionException(e, "product and UOM data");
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("Thread interrupted while fetching product details and UOM data", e);
                throw new TMSException(ERR_REQUEST_NOT_PROCESSED.name(), "Operation was interrupted while fetching product details and UOM data: " + e.getMessage());
            } catch (Exception e) {
                log.error("Unexpected error fetching product details and UOM data in parallel", e);
                throw new TMSException(ERR_REQUEST_NOT_PROCESSED.name(), "Failed to fetch product details and UOM data: " + e.getMessage());
            }
        }, executor);
    }


    private CompletableFuture<Map<String, FacilityDetailsDTO>> fetchFacilityList(List<String> externalFacilityCodes) {
        return CompletableFuture.supplyAsync(() -> {
            if (ObjectUtils.isEmpty(externalFacilityCodes)) {
                return Collections.emptyMap();
            }
            
            // Use integrator to get Resource facility DTOs
            List<ResourceFacilitiesDto> resourceFacilitiesDTOs = resourceIntegratorService.getFacilitiesDTOs(externalFacilityCodes);
            
            // Convert ResourceFacilitiesDto to FacilityDetailsDTO
            Map<String, FacilityDetailsDTO> result = resourceFacilitiesDTOs.stream()
                    .filter(Objects::nonNull)
                    .map(documentContextMapper::mapToFacilityDetailsDTO)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(
                            facility -> facility.getId().toString(),
                            Function.identity(),
                            (existing, replacement) -> existing));
            
            log.info("Successfully fetched {} facilities", result.size());
            return result;
        }, executor);
    }


    private String extractPickUpTimeZoneId(Map<String, ConsignmentDetailsDTO> consignmentMap) {
        return consignmentMap.values().stream()
                .map(ConsignmentDetailsDTO::getExpectedPickupTime)
                .filter(Objects::nonNull)
                .map(dateTime -> dateTime.getTimeZoneId())
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(DEFAULT_TIME_ZONE_ID);
    }

    private String extractDeliveryTimeZoneId(Map<String, ConsignmentDetailsDTO> consignmentMap) {
        return consignmentMap.values().stream()
                .map(ConsignmentDetailsDTO::getExpectedDeliveryTime)
                .filter(Objects::nonNull)
                .map(dateTime -> dateTime.getTimeZoneId())
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(DEFAULT_TIME_ZONE_ID);
    }

    private TripDocumentDTO buildTripDetailsWithDateConversion(Trip trip, String pickUpTimeZoneId, String deliveryTimeZoneId) {
        return TripDocumentDTO.builder()
                .tripCode(trip.getCode())
                .status(trip.getStatus())
                .externalOriginLocationCode(trip.getExternalOriginLocationCode())
                .externalDestinationLocationCode(trip.getExternalDestinationLocationCode())
                .expectedStartAt(trip.getExpectedStartAt() != null ?
                    dateTimeUtil.fromEpochMillisToDate(trip.getExpectedStartAt().getEpoch(), pickUpTimeZoneId) : null)
                .expectedEndAt(trip.getExpectedEndAt() != null ?
                    dateTimeUtil.fromEpochMillisToDate(trip.getExpectedEndAt().getEpoch(), deliveryTimeZoneId) : null)
                .actualStartAt(trip.getActualStartAt() != null ?
                    dateTimeUtil.fromEpochMillisToDate(trip.getActualStartAt().getEpoch(), pickUpTimeZoneId) : null)
                .actualEndAt(trip.getActualEndAt() != null ?
                    dateTimeUtil.fromEpochMillisToDate(trip.getActualEndAt().getEpoch(), deliveryTimeZoneId) : null)
                .build();
    }
}
