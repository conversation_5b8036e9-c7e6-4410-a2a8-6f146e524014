package com.dpw.ctms.move.service.impl;

import com.dpw.ctms.move.entity.TrailerResource;
import com.dpw.ctms.move.repository.TrailerResourceRepository;
import com.dpw.ctms.move.service.ITrailerService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.Set;

@Service
@RequiredArgsConstructor
public class TrailerServiceImpl implements ITrailerService {
    private final TrailerResourceRepository trailerResourceRepository;
    @Override
    public Set<TrailerResource> getAllByCodes(Set<String> codes) {
        return new HashSet<>(trailerResourceRepository.findAllByCodeInAndDeletedAtIsNull(codes));
    }

    @Override
    public void discardTrailerResources(Set<TrailerResource> trailerResources) {
        trailerResources.forEach(trailerResource -> trailerResource.setDeletedAt(System.currentTimeMillis()));
    }
}
