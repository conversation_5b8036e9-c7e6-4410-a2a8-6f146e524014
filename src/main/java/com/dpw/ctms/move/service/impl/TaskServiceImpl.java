package com.dpw.ctms.move.service.impl;

import com.dpw.ctms.move.constants.TaskServiceConstants;
import com.dpw.ctms.move.dto.ParamValueVehicleOperatorDTO;
import com.dpw.ctms.move.dto.TaskMandatoryCheckChannel;
import com.dpw.ctms.move.dto.taskmanager.TaskRegistrationDTO;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.TaskParam;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.entity.VehicleOperatorResource;
import com.dpw.ctms.move.enums.TaskRole;
import com.dpw.ctms.move.enums.TaskStatus;
import com.dpw.ctms.move.integration.adapter.TaskServiceAdapter;
import com.dpw.ctms.move.integration.dto.resource.VehicleOperatorRoleDTO;
import com.dpw.ctms.move.integration.request.taskmanager.GetTaskByRegistrationCodeRequest;
import com.dpw.ctms.move.integration.request.taskmanager.TaskInstanceDeRegistrationRequest;
import com.dpw.ctms.move.integration.request.taskmanager.TaskInstanceRegistrationRequest;
import com.dpw.ctms.move.integration.response.taskmanager.GetTaskByRegistrationCodeResponse;
import com.dpw.ctms.move.integration.response.taskmanager.TaskInstanceDeRegistrationResponse;
import com.dpw.ctms.move.integration.response.taskmanager.TaskInstanceRegistrationResponse;
import com.dpw.ctms.move.integration.service.IConfigServiceIntegrator;
import com.dpw.ctms.move.mapper.TaskInstanceRegistrationRequestMapper;
import com.dpw.ctms.move.repository.TaskRepository;
import com.dpw.ctms.move.service.ITaskParamService;
import com.dpw.ctms.move.service.ITaskService;
import com.dpw.ctms.move.service.IVehicleOperatorService;
import com.dpw.tmsutils.annotation.MethodLog;
import com.dpw.tmsutils.exception.TMSException;
import com.fasterxml.jackson.core.type.TypeReference;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.dpw.ctms.move.constants.ConfigConstants.TASK_MANDATORY_CHECK_CHANNEL;
import static com.dpw.ctms.move.constants.ErrorMessageConstant.INVALID_TASK_CODE;
import static com.dpw.ctms.move.constants.ErrorMessageConstant.INVALID_TASK_ID;
import static com.dpw.ctms.move.constants.MoveConstants.COMPANY_REFERENCE;
import static com.dpw.ctms.move.constants.MoveConstants.CURRENT_LOCATION;
import static com.dpw.ctms.move.constants.RequestFilterConstants.CURRENT_CLIENT_TYPE_HEADER;
import static com.dpw.ctms.move.constants.RoleType.VEHICLE_OPERATION_ROLE_TYPE;
import static com.dpw.ctms.move.constants.TaskServiceConstants.CURR_CLIENT_CODE;
import static com.dpw.ctms.move.constants.TaskServiceConstants.CURR_LOC_CODE;
import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.DATA_NOT_FOUND;

@Service
@RequiredArgsConstructor
@Slf4j
public class TaskServiceImpl implements ITaskService {

    private final TaskInstanceRegistrationRequestMapper taskInstanceRegistrationRequestMapper;
    private final TaskRepository taskRepository;
    private final TaskServiceAdapter taskServiceAdapter;
    private final ITaskParamService taskParamService;
    private final IVehicleOperatorService vehicleOperatorService;
    private final IConfigServiceIntegrator configServiceIntegrator;

    @Override
    public Task findTaskById(Long taskId) {
        return taskRepository.findById(taskId)
                .orElseThrow(() -> {
                    log.error("Task id {} not found", taskId);
                    return new TMSException(DATA_NOT_FOUND.name(), String.format(INVALID_TASK_ID, taskId));
                });
    }

    @Override
    public Task saveTask(Task task) {
        return taskRepository.save(task);
    }

    @Override
    @Transactional
    @MethodLog
    public void registerTaskInstances(List<Task> taskList, TransportOrder transportOrder) {
        if (CollectionUtils.isEmpty(taskList)) {
            log.info("Task list is empty, nothing to register");
            return;
        }

        if (transportOrder == null) {
            log.info("Transport order is null, cannot register task instances");
            return;
        }

        // Extract vehicle operators for all tasks
        Map<String, List<String>> taskVehicleOperatorMap = extractVehicleOperators(taskList);

        // Build task registration DTOs using crpIds from transport order
        List<TaskRegistrationDTO.TaskInstanceRegisterDetailsDTO> taskInstanceRegisterDetailsDTOList =
                buildTaskRegistrationDTOs(taskList, taskVehicleOperatorMap, transportOrder);

        // Register tasks and update with registration codes
        registerTasksAndUpdateCodes(taskList, taskInstanceRegisterDetailsDTOList);
    }

    private Map<String, List<String>> extractVehicleOperators(List<Task> taskList) {
        Map<String, List<String>> taskVehicleOperatorMap = new HashMap<>();

        taskList.forEach(task -> {
            List<ParamValueVehicleOperatorDTO> vehicleOperators = taskParamService.getVehicleOperators(task);
            if (!vehicleOperators.isEmpty()) {
                List<String> operatorCodes = vehicleOperators.stream()
                        .map(ParamValueVehicleOperatorDTO::getExternalResourceId)
                        .filter(Objects::nonNull)
                        .toList();
                if (!operatorCodes.isEmpty()) {
                    taskVehicleOperatorMap.put(task.getCode(), operatorCodes);
                }
            }
        });

        return taskVehicleOperatorMap;
    }

    private List<TaskRegistrationDTO.TaskInstanceRegisterDetailsDTO> buildTaskRegistrationDTOs(
            List<Task> taskList,
            Map<String, List<String>> taskVehicleOperatorMap,
            TransportOrder transportOrder) {

        return taskList.stream()
                .map(task -> buildSingleTaskRegistrationDTO(task, taskVehicleOperatorMap, transportOrder))
                .toList();
    }

    private TaskRegistrationDTO.TaskInstanceRegisterDetailsDTO buildSingleTaskRegistrationDTO(
            Task task,
            Map<String, List<String>> taskVehicleOperatorMap,
            TransportOrder transportOrder) {

        TaskRegistrationDTO.TaskInstanceRegisterDetailsDTO taskDTO =
                taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterDetailsDTO(task);

        List<TaskRegistrationDTO.RoleAccess> roleAccessList = new ArrayList<>();

        // Add vehicle operator role access
        List<String> vehicleOperatorCodes = taskVehicleOperatorMap.get(task.getCode());
        if (vehicleOperatorCodes != null) {
            addVehicleOperatorRoleAccess(vehicleOperatorCodes, transportOrder, roleAccessList);
        }

        // Add controller role access
        addControllerRoleAccess(roleAccessList);

        taskDTO.setRoleAccessRequest(new TaskRegistrationDTO.RoleAccessDTO(roleAccessList));
        return taskDTO;
    }

    private void addVehicleOperatorRoleAccess(
            List<String> vehicleOperatorCodes,
            TransportOrder transportOrder,
            List<TaskRegistrationDTO.RoleAccess> roleAccessList) {

        VehicleOperatorRoleDTO vehicleOperatorRole = vehicleOperatorService.fetchVehicleOperatorRoleConfig();
        vehicleOperatorCodes.forEach(operatorCode -> {
            String crpCode = fetchCRPCodeFromTransportOrder(operatorCode, transportOrder);
            if (crpCode != null) {
                roleAccessList.add(TaskRegistrationDTO.RoleAccess.builder()
                        .accessTo(crpCode)
                        .accessType(VEHICLE_OPERATION_ROLE_TYPE)
                        .role(vehicleOperatorRole.getRole())
                        .build());
            } else {
                log.warn("CRP code not found for vehicle operator: {}", operatorCode);
            }
        });
    }

    private void addControllerRoleAccess(List<TaskRegistrationDTO.RoleAccess> roleAccessList) {
        roleAccessList.add(TaskRegistrationDTO.RoleAccess.builder()
                .accessTo(TaskRole.ALL.name())
                .accessType(TaskRole.ALL.name())
                .role(MDC.get(CURRENT_CLIENT_TYPE_HEADER))
                .build());
    }

    private void registerTasksAndUpdateCodes(
            List<Task> taskList,
            List<TaskRegistrationDTO.TaskInstanceRegisterDetailsDTO> taskInstanceRegisterDetailsDTOList) {

        TaskRegistrationDTO taskRegistrationDTO = TaskRegistrationDTO.builder()
                .tasks(taskInstanceRegisterDetailsDTOList)
                .build();

        TaskInstanceRegistrationRequest request =
                taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterRequest(taskRegistrationDTO);

        String hierarchyIdentifier = String.format(
                "%s:%s#$#%s:%s",
                CURR_CLIENT_CODE, MDC.get(COMPANY_REFERENCE),
                CURR_LOC_CODE, MDC.get(CURRENT_LOCATION)
        );
        List<TaskInstanceRegistrationResponse> responses =
                taskServiceAdapter.registerTaskInstance(request, TaskServiceConstants.DEFAULT_TENANT_CODE,
                        TaskServiceConstants.DEFAULT_TENANT_SERVICE_CODE, hierarchyIdentifier);

        updateTasksWithRegistrationCodes(taskList, responses);
    }

    private void updateTasksWithRegistrationCodes(
            List<Task> taskList,
            List<TaskInstanceRegistrationResponse> responses) {

        Map<String, Task> taskByCode = taskList.stream()
                .collect(Collectors.toMap(Task::getCode, Function.identity()));

        responses.forEach(response -> {
            Task task = taskByCode.get(response.getExtTaskTransactionCode());
            if (task != null) {
                task.setExternalTaskRegistrationCode(response.getTaskRegistrationCode());
            } else {
                log.warn("Task not found for response code: {}", response.getExtTaskTransactionCode());
            }
        });
    }

    private String fetchCRPCodeFromTransportOrder(
            String vehicleOperatorCode,
            TransportOrder transportOrder) {

        if (transportOrder == null || transportOrder.getTrips() == null) {
            log.info("Transport order or trips are null, cannot fetch CRP code for operator: {}", vehicleOperatorCode);
            return null;
        }

        // Find the VehicleOperatorResource with matching externalResourceId
        return transportOrder.getTrips().stream()
                .filter(Objects::nonNull)
                .filter(trip -> trip.getVehicleOperatorResources() != null)
                .flatMap(trip -> trip.getVehicleOperatorResources().stream())
                .filter(Objects::nonNull)
                .filter(resource -> vehicleOperatorCode.equals(resource.getExternalResourceId()))
                .map(VehicleOperatorResource::getCrpId)
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(null);
    }

    @Override
    public Task findTaskByCode(String taskCode) {
        return taskRepository.findByCodeAndDeletedAtIsNull(taskCode)
                .orElseThrow(() -> {
                    log.error("Task with code {} not found", taskCode);
                    return new TMSException(DATA_NOT_FOUND.name(), String.format(INVALID_TASK_CODE, taskCode));
                });
    }

    @Override
    public Set<Task> getAllByCodes(Set<String> taskCodes) {
        return new HashSet<>(taskRepository.findAllByCodeInAndDeletedAtIsNull(taskCodes));
    }

    @Override
    public void updateTaskMandatoryFlag(List<Task> taskList) {
        if (CollectionUtils.isEmpty(taskList)) {
            log.info("Task list is empty skip mandatory flag update");
            return;
        }
        List<String> taskRegistrationCodes = taskList.stream()
                .map(Task::getExternalTaskRegistrationCode)
                .toList();
        GetTaskByRegistrationCodeRequest taskDetailsRequest = GetTaskByRegistrationCodeRequest.builder()
                .taskRegistrationCodes(taskRegistrationCodes)
                .source("WEB")
                .build();
        List<GetTaskByRegistrationCodeResponse> responses = taskServiceAdapter
                .getTaskByRegistrationCode(taskDetailsRequest);
        Map<String, GetTaskByRegistrationCodeResponse> taskResponseMap = responses.stream()
                .collect(Collectors.toMap(GetTaskByRegistrationCodeResponse::getTaskRegistrationCode, Function.identity()));
        TaskMandatoryCheckChannel mandatoryTaskChannels = configServiceIntegrator.fetchConfig(TASK_MANDATORY_CHECK_CHANNEL,
                new TypeReference<TaskMandatoryCheckChannel>() {
                });
        taskList.forEach(task -> {
            GetTaskByRegistrationCodeResponse response = taskResponseMap.get(task.getExternalTaskRegistrationCode());
            long count = response.getMandatoryConfigs().stream().filter(
                    config -> mandatoryTaskChannels.getMandatoryCheckChannels() != null &&
                            mandatoryTaskChannels.getMandatoryCheckChannels().contains(config.getSource()) &&
                            config.getIsMandatory().equals(Boolean.TRUE)).count();
            task.setMandatory((count > 0) ? Boolean.TRUE : Boolean.FALSE);
        });
    }

    @Override
    public void discardTasks(Set<Task> tasks) {
        Set<Task> discardedTasks = tasks.stream()
                .peek(task -> {task.setStatus(TaskStatus.DISCARDED);
                    task.setDeletedAt(System.currentTimeMillis());
                })
                .collect(Collectors.toSet());

        Set<TaskParam> taskParams = discardedTasks.stream()
                .flatMap(task -> task.getTaskParams().stream())
                .collect(Collectors.toSet());
        taskParamService.discardTaskParams(taskParams);

        TaskInstanceDeRegistrationRequest taskInstanceDeRegistrationRequest = createTaskInstanceDeRegistrationRequest(discardedTasks);
        List<TaskInstanceDeRegistrationResponse> taskInstanceDeRegistrationResponses =
                taskServiceAdapter.deRegisterTaskInstance(taskInstanceDeRegistrationRequest);
        log.info("Task instance de-registration done for task codes {}", taskInstanceDeRegistrationResponses
                .stream().map(TaskInstanceDeRegistrationResponse::getExtTaskTransactionCode)
                .collect(Collectors.toSet()));
    }

    private TaskInstanceDeRegistrationRequest createTaskInstanceDeRegistrationRequest(Set<Task> tasks) {
        List<TaskInstanceDeRegistrationRequest.TaskInstanceDeRegisterDetailsRequest> taskDetails = tasks.stream()
                .filter(Objects::nonNull)
                .map(task -> TaskInstanceDeRegistrationRequest.TaskInstanceDeRegisterDetailsRequest.builder()
                        .taskRegistrationCode(task.getExternalTaskRegistrationCode()) // assuming such a getter exists
                        .build())
                .collect(Collectors.toList());

        return TaskInstanceDeRegistrationRequest.builder()
                .tasks(taskDetails)
                .build();
    }
}