package com.dpw.ctms.move.service;

import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.TransportOrder;

import java.util.List;
import java.util.Set;

public interface ITaskService {
    Task findTaskById(Long taskId);
    Task saveTask(Task task);
    void registerTaskInstances(List<Task> taskList, TransportOrder transportOrder);
    Task findTaskByCode(String taskCode);
    void discardTasks(Set<Task> tasks);
    Set<Task> getAllByCodes(Set<String> taskCodes);
    void updateTaskMandatoryFlag(List<Task> taskList);
}
