package com.dpw.ctms.move.service.impl;

import com.dpw.ctms.move.dto.TransportOrderDTO;
import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.StopTask;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.enums.ShipmentStatus;
import com.dpw.ctms.move.enums.TripStatus;
import com.dpw.ctms.move.helper.EntitySoftDeleteHelper;
import com.dpw.ctms.move.mapper.IEntityRelationshipMapper;
import com.dpw.ctms.move.mapper.TransportOrderMapper;
import com.dpw.ctms.move.repository.TransportOrderRepository;
import com.dpw.ctms.move.request.TransportOrderDiscardRequest;
import com.dpw.ctms.move.request.TransportOrderFTLCreateRequest;
import com.dpw.ctms.move.request.TransportOrderListingRequest;
import com.dpw.ctms.move.response.ListResponse;
import com.dpw.ctms.move.response.TransportOrderListingResponse;
import com.dpw.ctms.move.response.TransportOrderDetailsResponse;
import com.dpw.ctms.move.response.TransportOrderResponse;
import com.dpw.ctms.move.service.ITaskService;
import com.dpw.ctms.move.service.ITransportOrderService;
import com.dpw.ctms.move.service.IVehicleOperatorService;
import com.dpw.ctms.move.service.TransportOrderFilteringService;
import com.dpw.ctms.move.validator.TransportOrderValidator;
import com.dpw.tmsutils.exception.TMSException;
import com.dpw.tmsutils.utils.TMSExceptionErrorCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;


import static com.dpw.ctms.move.constants.ErrorMessageConstant.INVALID_TRANSPORT_ORDER_CODE;
import static com.dpw.ctms.move.constants.ErrorMessageConstant.INVALID_TRANSPORT_ORDER_ID;
import static com.dpw.ctms.move.constants.ErrorMessageConstant.TRANSPORT_ORDER_ALREADY_EXISTS;
import static com.dpw.ctms.move.constants.ErrorMessageConstant.TRANSPORT_ORDER_DB_PERSISTENCE_FAILED;
import static com.dpw.ctms.move.constants.ErrorMessageConstant.TRANSPORT_ORDER_UPDATE_NOT_ALLOWED;
import static com.dpw.ctms.move.constants.ResponseMessageConstants.TRANSPORT_ORDER_CREATION_SUCCESS_MESSAGE;
import static com.dpw.ctms.move.constants.ResponseMessageConstants.TRANSPORT_ORDER_DISCARD_NOT_ALLOWED_MESSAGE;
import static com.dpw.ctms.move.constants.ResponseMessageConstants.TRANSPORT_ORDER_DISCARD_SUCCESS_MESSAGE;
import static com.dpw.ctms.move.constants.ResponseMessageConstants.TRANSPORT_ORDER_UPDATE_SUCCESS_MESSAGE;
import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.DATA_NOT_FOUND;
import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.INVALID_REQUEST;

@Service
@RequiredArgsConstructor
@Slf4j
public class TransportOrderServiceImpl implements ITransportOrderService {
    private final TransportOrderMapper transportOrderMapper;
    private final TransportOrderRepository transportOrderRepository;
    private final List<IEntityRelationshipMapper> relationshipMappers;
    private final TransportOrderFilteringService transportOrderFilteringService;
    private final ITaskService taskService;
    private final EntitySoftDeleteHelper entitySoftDeleteHelper;
    private final TransportOrderValidator transportOrderValidator;
    private final IVehicleOperatorService vehicleOperatorService;

    @Override
    @Transactional
    public TransportOrderResponse createTransportOrderFTLFulfilment(TransportOrderFTLCreateRequest transportOrderFTLCreateRequest) {
        TransportOrder transportOrder = createTransportOrderFTLFulfilmentInternal(transportOrderFTLCreateRequest);
        return buildTransportOrderResponse(transportOrder, TRANSPORT_ORDER_CREATION_SUCCESS_MESSAGE);
    }

    private TransportOrder createTransportOrderFTLFulfilmentInternal(TransportOrderFTLCreateRequest transportOrderFTLCreateRequest) {
        try {
            TransportOrderDTO transportOrderDTO = transportOrderMapper.toDTO(transportOrderFTLCreateRequest);
            TransportOrder transportOrder = transportOrderMapper.toEntity(transportOrderDTO);
            processTransportOrder(transportOrderDTO,transportOrder);
            /* If any entity is already present with the same code and not soft deleted
             * don't allow TO creation */
            if (!transportOrderValidator.validateTransportOrderFTLCreation(transportOrder)) {
                log.error("Transport Order with code {} already exists", transportOrder.getCode());
                throw new TMSException(INVALID_REQUEST.name(),
                        String.format(TRANSPORT_ORDER_ALREADY_EXISTS, transportOrder.getCode()));
            }
            // Extract tasks from transport order
            List<Task> tasks = extractTasks(transportOrder);

            // Populate crpId in VehicleOperatorResource entities
            populateCrpIdInTransportOrder(transportOrder);

            // Register tasks using transport order
            registerTaskInstances(tasks, transportOrder);

            // Check if task is mandatory
            taskService.updateTaskMandatoryFlag(tasks);

            // Save transport order after both operations are complete
            TransportOrder persistedTransportOrder = saveTransportOrder(transportOrder);

            return persistedTransportOrder;
        }
        catch (DataAccessException dataAccessException) {
            throw new TMSException(TMSExceptionErrorCode.INTERNAL_ERROR.name(), TRANSPORT_ORDER_DB_PERSISTENCE_FAILED,
                    Map.of("rootCause", dataAccessException.getClass().getSimpleName(), "errorDetails", dataAccessException.getMessage()));
        }
    }

    @Override
    @Transactional
    public TransportOrderResponse updateTransportOrderFTLFulfilment(TransportOrderFTLCreateRequest transportOrderFTLCreateRequest) {
        TransportOrder existingTransportOrder = findTransportOrderByCode(transportOrderFTLCreateRequest.getCode());
        if (!transportOrderValidator.validateTransportOrderFTLUpdate(existingTransportOrder)) {
            throw new TMSException(INVALID_REQUEST.name(),
                    String.format(TRANSPORT_ORDER_UPDATE_NOT_ALLOWED, existingTransportOrder.getCode()));
        }
        discardTransportOrderInternal(existingTransportOrder);
        TransportOrder transportOrder = createTransportOrderFTLFulfilmentInternal(transportOrderFTLCreateRequest);
        return buildTransportOrderResponse(transportOrder, TRANSPORT_ORDER_UPDATE_SUCCESS_MESSAGE);
    }

    @Override
    public ListResponse<TransportOrderListingResponse> listTransportOrders(TransportOrderListingRequest transportOrderListingRequest) {
        return transportOrderFilteringService.filterTransportOrders(
                transportOrderListingRequest
        );
    }

    @Override
    public TransportOrder findTransportOrderById(Long id) {
        return transportOrderRepository.findById(id).orElseThrow(() -> {
            log.error("Transport Order with id {} not found", id);
            return new TMSException(
                    DATA_NOT_FOUND.name(),
                    String.format(INVALID_TRANSPORT_ORDER_ID, id)
            );
        });
    }

    @Override
    public TransportOrder findTransportOrderByCode(String code) {
        return transportOrderRepository.findByCodeAndDeletedAtIsNull(code).orElseThrow(() -> {
            log.error("Transport Order with code {} not found", code);
            return new TMSException(
                    DATA_NOT_FOUND.name(),
                    String.format(INVALID_TRANSPORT_ORDER_CODE, code)
            );
        });
    }

    @Override
    @Transactional
    public TransportOrderResponse discardTransportOrder(String code, TransportOrderDiscardRequest transportOrderDiscardRequest) {
        if (!canTransportOrderBeDiscarded(code)) {
            log.warn("Transport order with code {} cannot be discarded", code);
            return TransportOrderResponse.builder()
                    .transportOrderCode(code)
                    .isSuccess(false)
                    .message(TRANSPORT_ORDER_DISCARD_NOT_ALLOWED_MESSAGE)
                    .build();
        }
        else {
            TransportOrder transportOrder = findTransportOrderByCode(code);
            discardTransportOrderInternal(transportOrder);
            return TransportOrderResponse.builder()
                    .transportOrderCode(code)
                    .isSuccess(true)
                    .message(TRANSPORT_ORDER_DISCARD_SUCCESS_MESSAGE)
                    .build();
        }
    }

    private boolean canTransportOrderBeDiscarded(String transportOrderCode) {
        TransportOrder transportOrder = findTransportOrderByCode(transportOrderCode);
        return transportOrderValidator.validateTransportOrderFTLUpdate(transportOrder);
    }

    private void discardTransportOrderInternal(TransportOrder transportOrder) {
        entitySoftDeleteHelper.softDeleteEntity(transportOrder);
        saveTransportOrder(transportOrder);
    }

    private TransportOrderResponse buildTransportOrderResponse(TransportOrder transportOrder, String message) {
        return TransportOrderResponse.builder()
                .transportOrderCode(transportOrder.getCode())
                .isSuccess(true)
                .message(message)
                .build();
    }

    private void processTransportOrder(TransportOrderDTO transportOrderDTO, TransportOrder transportOrder) {
        for (IEntityRelationshipMapper entityRelationshipMapper : relationshipMappers) {
            entityRelationshipMapper.mapRelationships(transportOrderDTO, transportOrder);
        }
    }

    @Override
    public TransportOrder saveTransportOrder(TransportOrder transportOrder) {
        TransportOrder persistedTransportOrder = transportOrderRepository.save(transportOrder);
        log.info("Transport Order and its related entities successfully persisted: {}", persistedTransportOrder);
        return persistedTransportOrder;
    }

    private List<Task> extractTasks(TransportOrder transportOrder) {
        return transportOrder.getTrips().stream()
                .filter(Objects::nonNull)
                .flatMap(trip -> trip.getStops().stream())
                .filter(Objects::nonNull)
                .flatMap(stop -> stop.getStopTasks().stream())
                .filter(Objects::nonNull)
                .map(StopTask::getTask)
                .filter(Objects::nonNull)
                .toList();
    }

    private void populateCrpIdInTransportOrder(TransportOrder transportOrder) {
        if (transportOrder == null) {
            log.info("Transport order is null, cannot populate crpId");
            return;
        }

        try {
            vehicleOperatorService.populateCrpIdInTransportOrder(transportOrder);
            log.info("Successfully populated crpId for transport order: {}", transportOrder.getCode());
        } catch (Exception e) {
            log.error("Failed to populate crpId for transport order: {}", transportOrder.getCode(), e);
            throw new TMSException(TMSExceptionErrorCode.INTERNAL_ERROR.name(),
                    "Failed to populate crpId in vehicle operator resources",
                    Map.of("transportOrderCode", transportOrder.getCode(), "errorDetails", e.getMessage()));
        }
    }

    private void registerTaskInstances(List<Task> tasks, TransportOrder transportOrder) {
        if (tasks == null || tasks.isEmpty()) {
            log.info("No tasks found, skipping task registration");
            return;
        }

        if (transportOrder == null) {
            log.info("Transport order is null, skipping task registration");
            return;
        }

        try {
            taskService.registerTaskInstances(tasks, transportOrder);
            log.info("Successfully registered {} task instances", tasks.size());
        } catch (Exception e) {
            log.error("Failed to register task instances for {} tasks", tasks.size(), e);
            throw new TMSException(TMSExceptionErrorCode.INTERNAL_ERROR.name(),
                    "Failed to register task instances",
                    Map.of("taskCount", String.valueOf(tasks.size()), "errorDetails", e.getMessage()));
        }
    }

    @Override
    @Transactional(readOnly = true)
    public TransportOrderDetailsResponse getTransportOrderDetails(String code) {
        TransportOrder transportOrder = findTransportOrderByCode(code);
        return transportOrderMapper.mapToDetailsResponse(transportOrder);
    }

    @Override
    public boolean isTransportOrderFTLFulfilmentCancellable(TransportOrder transportOrder) {
        Set<Trip> trips = transportOrder.getTrips();
        return trips.stream().allMatch(t -> t.getStatus().equals(TripStatus.CANCELLED));
    }

    @Override
    public boolean isTransportOrderLTLFulfilmentCancellable(TransportOrder transportOrder) {
        Set<Shipment> shipments = transportOrder.getShipments();
        return shipments.stream().allMatch(s ->
                s.getTrip() == null && s.getStatus().equals(ShipmentStatus.CANCELLED));
    }
}
