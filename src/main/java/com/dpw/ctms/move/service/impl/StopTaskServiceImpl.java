package com.dpw.ctms.move.service.impl;

import com.dpw.ctms.move.entity.StopTask;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.repository.StopTaskRepository;
import com.dpw.ctms.move.service.IStopTaskService;
import com.dpw.tmsutils.exception.TMSException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.dpw.ctms.move.constants.ErrorMessageConstant.INVALID_TASK_CODE;
import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.DATA_NOT_FOUND;

@Service
@RequiredArgsConstructor
@Slf4j
public class StopTaskServiceImpl implements IStopTaskService {
    private final StopTaskRepository stopTaskRepository;
    @Override
    public StopTask getStopByTask(Task task) {
        return stopTaskRepository.findByTask(task).orElseThrow(() -> {
            log.error("Stop task {} not found for task", task.getCode());
            return new TMSException(
                    DATA_NOT_FOUND.name(),
                    String.format(INVALID_TASK_CODE, task.getCode())
            );
        });
    }
}
