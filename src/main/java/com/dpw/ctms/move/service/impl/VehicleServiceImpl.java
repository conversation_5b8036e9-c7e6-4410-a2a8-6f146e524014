package com.dpw.ctms.move.service.impl;

import com.dpw.ctms.move.entity.VehicleResource;
import com.dpw.ctms.move.repository.VehicleResourceRepository;
import com.dpw.ctms.move.service.IVehicleService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.Set;

@Service
@RequiredArgsConstructor
public class VehicleServiceImpl implements IVehicleService {
    private final VehicleResourceRepository vehicleResourceRepository;

    @Override
    public void discardVehicleResources(Set<VehicleResource> vehicleResources) {
        vehicleResources.forEach(vehicleResource -> vehicleResource.setDeletedAt(System.currentTimeMillis()));
    }
    @Override
    public Set<VehicleResource> getAllByCodes(Set<String> codes) {
        return new HashSet<>(vehicleResourceRepository.findAllByCodeInAndDeletedAtIsNull(codes));
    }
}
