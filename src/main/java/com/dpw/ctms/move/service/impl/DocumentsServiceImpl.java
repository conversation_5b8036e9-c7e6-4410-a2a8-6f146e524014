package com.dpw.ctms.move.service.impl;

import com.dpw.ctms.move.config.ConfigService;
import com.dpw.ctms.move.constants.ConfigConstants;
import com.dpw.ctms.move.dto.DocumentUpdatedEventDTO;
import com.dpw.ctms.move.dto.EntityTypeWithIdsDTO;
import com.dpw.ctms.move.dto.GetDocumentsDto;
import com.dpw.ctms.move.dto.config.RunnerApiConfigDto;
import com.dpw.ctms.move.helper.DocumentHelper;
import com.dpw.ctms.move.integration.service.IConfigServiceIntegrator;
import com.dpw.ctms.move.integration.service.IRunnerService;
import com.dpw.ctms.move.specification.DocumentSpecifications;
import com.dpw.ctms.move.dto.document.DeliveryTaskDocumentDTO;
import com.dpw.ctms.move.dto.document.DocumentDTO;
import com.dpw.ctms.move.entity.Document;
import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.enums.*;
import com.dpw.ctms.move.mapper.DocumentMapper;
import com.dpw.ctms.move.repository.DocumentRepository;
import com.dpw.ctms.move.repository.ShipmentRepository;
import com.dpw.ctms.move.request.EntityDocumentRequest;
import com.dpw.ctms.move.request.documentEvent.PreSignedUrlEvent;
import com.dpw.ctms.move.response.PreSignedUrlResponse;
import com.dpw.ctms.move.response.DocumentDownloadResponse;
import com.dpw.ctms.move.response.DocumentErrorResponse;
import com.dpw.ctms.move.response.EntityDocumentResponse;
import com.dpw.ctms.move.response.FileDownloadPreSignedUrlResponse;
import com.dpw.ctms.move.service.IDocumentsService;
import com.dpw.ctms.move.service.IShipmentService;
import com.dpw.tmsutils.exception.TMSException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import com.dpw.ctms.move.service.document.DocumentGenerator;
import com.dpw.ctms.move.service.document.DocumentGeneratorFactory;
import com.dpw.ctms.move.util.CanonicalChecksum;
import com.dpw.ctms.move.util.DocumentUtil;
import com.dpw.ctms.move.util.JsonUtils;
import com.dpw.tmsutils.schemaobjects.*;
import com.dpw.tmsutils.utils.ObjectMapperUtil;
import com.dpw.tmsutils.service.DocumentService;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.dpw.ctms.move.constants.ErrorMessageConstant.UPLOAD_FAILED_DOCUMENT_MESSAGE;
import static com.dpw.ctms.move.constants.PropertyConstants.*;
import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.EXTERNAL_INVOCATION_EXCEPTION;

@Service
@RequiredArgsConstructor
@Slf4j
public class DocumentsServiceImpl implements IDocumentsService {

    private final DocumentGeneratorFactory documentGeneratorFactory;
    private final DocumentRepository documentRepository;
    private final CanonicalChecksum canonicalChecksum;
    private final DocumentService documentService;
    private final IConfigServiceIntegrator configServiceIntegrator;
    private final DocumentMapper documentMapper;
    private final ShipmentRepository shipmentRepository;
    private final IShipmentService shipmentService;
    private final IRunnerService runnerService;
    private final ApplicationEventPublisher applicationEventPublisher;
    private final DocumentHelper documentHelper;

    @Value("${documentService.write-count:100}")
    private Integer writeCount;

    @Value("${documentService.subsName:ctms_local-move}")
    private String subsName;


    public DocumentDownloadResponse downloadTripBolDocument(
            String tripCode,
            Tenant tenant
    ) {
        log.info("Generating BOL document for tripCode: {}", tripCode);
        DocumentGenerator<?> generator = documentGeneratorFactory.getGenerator(DocumentType.BOL);
        Object jsonObject = generator.generateJson(tripCode, tenant);
        String checksumString = canonicalChecksum.generateChecksum(jsonObject);

        // Build specification for finding BOL document
        Specification<Document> spec = DocumentSpecifications.bolDocumentSpec(
                tripCode, EntityType.TRIP.name(), DocumentStatus.ACTIVE, 
                DocumentOperationType.DOWNLOAD, checksumString, DocumentType.BOL);

        log.info("Finding BOL document with specification: {}", checksumString);
        Optional<Document> document = documentRepository.findOne(spec);

        if (document.isEmpty()) {
            DocumentDTO documentDTO = DocumentDTO.builder()
                    .entityId(tripCode)
                    .entityType(EntityType.TRIP.name())
                    .documentType(DocumentType.BOL)
                    .status(DocumentStatus.ACTIVE)
                    .documentOperationType(DocumentOperationType.DOWNLOAD)
                    .checksum(checksumString)
                    .build();
            return generateAndSaveDocument(documentDTO, tenant, JsonUtils.toJson(jsonObject));
        } else {
            // Always get fresh presigned URL from document service using fileIdentifier
            GetDownloadPreSignedURLRequest downloadPreSignedURLRequest =
                    GetDownloadPreSignedURLRequest
                            .builder()
                            .fileIdentifiers(Optional.ofNullable(document.get().getFileIdentifier())
                                .map(fileId -> Collections.singletonList(UUID.fromString(fileId)))
                                .orElse(Collections.emptyList()))
                            .readExpiryDurationInMinutes(LINK_EXPIRY_DURATION)
                            .build();

            DocumentServiceResponse<List<DownloadPreSignedURLResponse>> documentServiceResponse = documentService.getDownloadPreSignedURLWithToken(downloadPreSignedURLRequest);
            if (documentServiceResponse.isError()) {
                throw new TMSException(EXTERNAL_INVOCATION_EXCEPTION.name(), documentServiceResponse.getErrorDescription());
            }
            String presignedUrl = documentServiceResponse.getData().getFirst().getPreSignedUrl();
            return DocumentDownloadResponse.builder()
                    .presignedDownloadUrl(presignedUrl)
                    .fileIdentifier(document.get().getFileIdentifier())
                    .fileType(document.get().getFileType())
                    .build();
        }
    }

    private DocumentDownloadResponse generateAndSaveDocument(DocumentDTO documentDTO, Tenant tenant, String json) {
        log.info("Generating and saving document for entityId: {}, documentType: {}", documentDTO.getEntityId(), documentDTO.getDocumentType());

        JsonNode config = configServiceIntegrator.fetchConfig(ConfigConstants.BOL_CONFIG);
        String templateId = config.get("templateId").asText();
        log.info("Template id: {}", templateId);
        
        if (templateId == null) {
            throw new IllegalArgumentException("Template ID not found in config for documentType: " + 
                    documentDTO.getDocumentType() + ", vendor: " + tenant);
        }

        PrintBolRequest<JsonNode> printBolRequest = PrintBolRequest.<JsonNode>builder()
                .data(JsonUtils.toJsonNodeFromString(json))
                .bolConfiguration(PrintBolRequest.BolConfiguration.builder()
                        .type(FILE_TYPE_PDF).responseType(PRESIGNED_DOWNLOAD_LINK).linkExpiryDuration(LINK_EXPIRY_DURATION)
                        .build())
                .build();

        DocumentServiceResponse<PrintBolResponse> response = documentService.getBol(printBolRequest, templateId);
        log.info("Response from DocumentService: {}", response);

        if (response.isError()) {
            log.error("Unable to Generate Downlaod Url error: {}", response);
            throw new TMSException(EXTERNAL_INVOCATION_EXCEPTION.name(), response.getErrorDescription());
        }

        Document document = documentMapper.toEntity(documentDTO);
        document.setFileIdentifier(response.getData().getFileIdentifier());

        documentRepository.save(document);
        log.info("Saved document to database for entityId: {}, entityType: {}, documentType: {}", 
                documentDTO.getEntityId(), documentDTO.getEntityType(), documentDTO.getDocumentType());
        
        return DocumentDownloadResponse.builder()
                .presignedDownloadUrl(response.getData().getPresignedDownloadUrl())
                .fileIdentifier(response.getData().getFileIdentifier())
                .fileType(document.getFileType())
                .build();
    }

    @Override
    public PreSignedUrlResponse getPreSignedUrl() {
        GetPreSignedURLRequest getPreSignedURLRequest = GetPreSignedURLRequest.builder()
                .clientIdentifier(UUID.randomUUID().toString())
                .writeCount(writeCount)
                .writeExpiryDurationInMinutes(LINK_EXPIRY_DURATION)
                .subscriptionName(subsName)
                .build();
        DocumentServiceResponse<GetPreSignedURLResponse> preSignedUrlResponse = documentService.getPreSignedURL(getPreSignedURLRequest);
        if (preSignedUrlResponse.isError()) {
            log.error("GetPreSignedURLResponse error: {}", preSignedUrlResponse);
            throw new TMSException(EXTERNAL_INVOCATION_EXCEPTION.name(), preSignedUrlResponse.getErrorDescription());
        }
        return PreSignedUrlResponse.builder()
                .preSignedURL(preSignedUrlResponse.getData().getPreSignedUrl())
                .clientIdentifier(getPreSignedURLRequest.getClientIdentifier()).build();
    }

    @Override
    @Transactional
    public void findAndUpdate(PreSignedUrlEvent preSignedUrlEvent) {
        log.info("Processing pre-signed url event for fileKey: {}", preSignedUrlEvent.getFileKey());
        List<Document> documents = processDocuments(preSignedUrlEvent);
        if (documents.isEmpty()) {
            return;
        }
        List<Document> activatedDocuments = activateOrCreateDocuments(documents);
        DocumentUpdatedEventDTO documentUpdatedEventDTO = DocumentUpdatedEventDTO
                .builder()
                .activatedDocuments(activatedDocuments)
                .build();
        applicationEventPublisher.publishEvent(documentUpdatedEventDTO);
    }

    @Override
    @Transactional
    public void findAndUpdate(DeliveryTaskDocumentDTO deliveryTaskDocumentDTO) {
        log.info("Processing delivery task event for entityId: {}, entityType: {}, operationType: {}",
                deliveryTaskDocumentDTO.getEntityId(),
                deliveryTaskDocumentDTO.getEntityType(), deliveryTaskDocumentDTO.getOperationType());

        //Gives Activated Document and newly inserted documents which are currently inactive
        List<Document> documents = processDocuments(deliveryTaskDocumentDTO);
        
        // Get active entity documents using getDocuments
        GetDocumentsDto activeDocumentFilter = GetDocumentsDto.builder()
                .entityIds(List.of(deliveryTaskDocumentDTO.getEntityId()))
                .entityType(deliveryTaskDocumentDTO.getEntityType())
                .operationType(deliveryTaskDocumentDTO.getOperationType())
                .status(DocumentStatus.ACTIVE)
                .build();
        
        List<Document> activeEntityDocuments = getDocuments(List.of(activeDocumentFilter));
        log.info("Active entity documents: {}", activeEntityDocuments);

        List<Document> documentsToBeDiscarded = activeEntityDocuments.stream()
                .filter(doc -> {
                    String docAsyncUUID = doc.getAsyncMappingUUID();

                    // Skip documents without async mapping UUID
                    if (StringUtils.isBlank(docAsyncUUID)) {
                        return false;
                    }

                    // Discard if UUID is not in delivery task's UUID set
                    return !deliveryTaskDocumentDTO.getAsyncMappingUUIDs().contains(docAsyncUUID);
                })
                .toList();
        
        // Discard documents that are not in the current set and update shipment pod status
        List<Document> discardedDocuments = discardDocuments(documentsToBeDiscarded);

        // Activate inactive documents or  insert new documents and return only activated Documents
        List<Document> activatedDocuments = activateOrCreateDocuments(documents);

        DocumentUpdatedEventDTO documentUpdatedEventDTO = DocumentUpdatedEventDTO
                .builder()
                .activatedDocuments(activatedDocuments)
                .discardedDocuments(discardedDocuments)
                .build();

        applicationEventPublisher.publishEvent(documentUpdatedEventDTO);
    }

    private List<Document> discardDocuments(List<Document> documentsToDiscard) {
        List<Document> discardedDocs = documentHelper.discardDocuments(documentsToDiscard);
        if (!discardedDocs.isEmpty()) {
            return documentRepository.saveAll(discardedDocs);
        }
        return List.of();
    }

    private List<Document> activateOrCreateDocuments(List<Document> documents) {
        if (!ObjectUtils.isEmpty(documents)) {
            List<Document> savedDocuments = documentRepository.saveAll(documents);
            return documentHelper.filterActivatedDocuments(savedDocuments);
        }
        return List.of();
    }

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleDocumentUpdated(DocumentUpdatedEventDTO event) {
        List<Document> activatedDocuments = event.getActivatedDocuments();
        List<Document> discardedDocuments = event.getDiscardedDocuments();
        if(!ObjectUtils.isEmpty(activatedDocuments)) {
            processActiveDocuments(activatedDocuments);
        }
        if (!ObjectUtils.isEmpty(discardedDocuments)) {
            processDiscardedDocuments(discardedDocuments);
        }
    }
    
    private void processActiveDocuments(List<Document> documents) {
        List<Document> activePodDocuments = documentHelper.filterActivePodUploadDocuments(documents);
        
        if (activePodDocuments.isEmpty()) {
            log.info("No active POD documents to process");
            return;
        }
        
        List<EntityTypeWithIdsDTO> entityTypeDTOs = documentHelper.groupDocumentsByEntityType(activePodDocuments);
        
        // Get all shipments in one DB call
        List<Shipment> shipments = shipmentService.getShipmentsForEntities(entityTypeDTOs);
        
        if (shipments.isEmpty()) {
            log.warn("No shipments found for entities: {}, skipping operations", entityTypeDTOs);
            return;
        }
        
        log.info("Processing {} active POD documents for entities: {}",
                activePodDocuments.size(), entityTypeDTOs);
        
        // Update all shipments once
        shipmentService.updateDocumentAttachedStatus(shipments, true);
        JsonNode jsonNode = configServiceIntegrator.fetchConfig(ConfigConstants.ENABLE_RUNNER_API_CALL);
        RunnerApiConfigDto runnerApiConfig = ObjectMapperUtil.getObjectMapper().convertValue(jsonNode, RunnerApiConfigDto.class);
        boolean isRunnerApiEnabled = runnerApiConfig != null && runnerApiConfig.isEnable();
        if(isRunnerApiEnabled) {
            runnerService.sendDocumentsToRunner(activePodDocuments, shipments);
        }
        log.info("Completed processing for {} documents", activePodDocuments.size());
    }


    private void processDiscardedDocuments(List<Document> documents) {
        List<Document> discardedDocuments = documentHelper.filterDiscardedPodUploadDocuments(documents);

        if (discardedDocuments.isEmpty()) {
            log.info("No discarded POD documents to process");
            return;
        }

        // Build filters to check for active POD documents
        List<GetDocumentsDto> activePodDocumentFilters = documentHelper.buildActivePodDocumentFilters(discardedDocuments);
        
        // Fetch active POD documents for all entities
        List<Document> activePodDocuments = getDocuments(activePodDocumentFilters);
        
        // Get documents whose entities have no active POD documents
        List<Document> documentsToProcess = documentHelper.getDocumentsWithNoActiveEntityDocuments(
                discardedDocuments,
                activePodDocuments,
                DocumentType.POD
        );
        
        if (documentsToProcess.isEmpty()) {
            log.info("No entities eligible for POD status update");
            return;
        }

        List<EntityTypeWithIdsDTO> entityTypeDTOs = documentHelper.groupDocumentsByEntityType(documentsToProcess);

        // Get all shipments in one DB call
        List<Shipment> shipments = shipmentService.getShipmentsForEntities(entityTypeDTOs);

        if (shipments.isEmpty()) {
            log.warn("No shipments found for entities: {}, skipping operations", entityTypeDTOs);
            return;
        }

        log.info("Processing {} discarded POD documents for entities: {}",
                documentsToProcess.size(), entityTypeDTOs);

        // Update all shipments once
        shipmentService.updateDocumentAttachedStatus(shipments, false);
    }

    private <T> List<Document> processDocuments(T eventData) {
        List<String> asyncMappingUUIDs = documentHelper.extractAsyncMappingUUID(eventData);
        Specification<Document> spec = DocumentSpecifications.byAsyncMappingUUIDsInSpec(asyncMappingUUIDs);
        List<Document> existingDocuments = documentRepository.findAll(spec);

        Map<String, Document> existingDocumentMap = existingDocuments.stream()
                .collect(Collectors.toMap(Document::getAsyncMappingUUID, Function.identity()));

        return documentHelper.processDocuments(eventData, existingDocumentMap);
    }


    @Override
    public DocumentErrorResponse getAllErrors(List<String> asyncMappingUUIDs) {
        log.info("Checking document status for asyncMappingUUIDs: {}", asyncMappingUUIDs);
        
        Specification<Document> spec = DocumentSpecifications.byAsyncMappingUUIDsInSpec(asyncMappingUUIDs);
        List<Document> documents = documentRepository.findAll(spec);
        DocumentErrorResponse response = new DocumentErrorResponse();
        List<DocumentErrorResponse.FileErrorDetails> errorDetails = new ArrayList<>();
        
        // Get async mapping UUIDs of found documents
        Set<String> foundAsyncMappingUUIDs = documents.stream()
                .map(Document::getAsyncMappingUUID)
                .collect(java.util.stream.Collectors.toSet());

        if (!foundAsyncMappingUUIDs.isEmpty()) {
            // Check for missing async mapping UUIDs
            for (String asyncMappingUUID : asyncMappingUUIDs) {
                if (!foundAsyncMappingUUIDs.contains(asyncMappingUUID)) {
                    errorDetails.add(DocumentErrorResponse.FileErrorDetails.builder()
                            .error(UPLOAD_FAILED_DOCUMENT_MESSAGE)
                            .fileIdentifier(asyncMappingUUID)
                            .build());
                    log.info("Document with asyncMappingUUID: {} not found", asyncMappingUUID);
                }
            }

            // Check for inactive documents
            for (Document document : documents) {
                if (DocumentUtil.isDocumentInactive(document)) {
                    errorDetails.add(DocumentErrorResponse.FileErrorDetails.builder()
                            .error(UPLOAD_FAILED_DOCUMENT_MESSAGE)
                            .asyncMappingUUID(document.getAsyncMappingUUID())
                            .fileName(document.getFileName())
                            .fileSize(document.getFileSize())
                            .fileType(document.getFileType())
                            .fileIdentifier(document.getFileIdentifier())
                            .build());
                    log.info("Document with asyncMappingUUID: {} is inactive", document.getAsyncMappingUUID());
                }
            }
        }
        
        if (errorDetails.isEmpty()) {
            response.setFailedDocumentDetails(null);
            log.info("All documents are active for asyncMappingUUIDs: {}", asyncMappingUUIDs);
        } else {
            response.setFailedDocumentDetails(errorDetails);
            log.info("Found {} failed documents out of {} total asyncMappingUUIDs", errorDetails.size(), asyncMappingUUIDs.size());
        }
        
        return response;
    }

    @Override
    public EntityDocumentResponse getDocumentsByEntity(List<EntityDocumentRequest> entityRequests) {
        log.info("Getting documents for {} entity requests", entityRequests.size());
        
        // Convert EntityDocumentRequest to GetDocumentsDto
        List<GetDocumentsDto> documentFilters = entityRequests.stream()
                .map(request -> GetDocumentsDto.builder()
                        .entityIds(List.of(request.getEntityCode()))
                        .entityType(request.getEntityType())
                        .operationType(DocumentOperationType.UPLOAD)
                        .status(DocumentStatus.ACTIVE)
                        .build())
                .collect(Collectors.toList());
        
        // Get all documents using the existing function
        List<Document> documents = getDocuments(documentFilters);
        
        // Convert documents to FileDetails
        List<EntityDocumentResponse.FileDetail> allFileDetails = documents.stream()
                .map(documentMapper::toFileDetail)
                .collect(Collectors.toList());

        EntityDocumentResponse.ResponseData responseData = EntityDocumentResponse.ResponseData.builder()
                .fileDetails(allFileDetails)
                .build();
                
        return EntityDocumentResponse.builder()
                .data(responseData)
                .build();
    }

    @Override
    public FileDownloadPreSignedUrlResponse getFileDownloadUrls(List<String> externalDocumentIdentifiers) {
        log.info("Getting download URLs for {} external document identifiers", externalDocumentIdentifiers.size());
        
        try {
            List<UUID> fileIdentifierUUIDs = new ArrayList<>();
            
            for (String identifier : externalDocumentIdentifiers) {
                try {
                    UUID uuid = UUID.fromString(identifier);
                    fileIdentifierUUIDs.add(uuid);
                } catch (IllegalArgumentException e) {
                    log.error("Invalid UUID format for identifier: {}", identifier);
                    throw new TMSException(EXTERNAL_INVOCATION_EXCEPTION.name(),"Invalid file identifier format: " + identifier);
                }
            }

            GetDownloadPreSignedURLRequest downloadPreSignedURLRequest = GetDownloadPreSignedURLRequest
                    .builder()
                    .fileIdentifiers(fileIdentifierUUIDs)
                    .readExpiryDurationInMinutes(LINK_EXPIRY_DURATION)
                    .build();

            DocumentServiceResponse<List<DownloadPreSignedURLResponse>> response = 
                    documentService.getDownloadPreSignedURLWithToken(downloadPreSignedURLRequest);
            
            if (response.isError()) {
                log.error("Error getting presigned URLs from document service: {}", response.getErrorDescription());
                throw new TMSException(EXTERNAL_INVOCATION_EXCEPTION.name(), response.getErrorDescription());
            }

            Map<String, String> urlMap = new HashMap<>();
            for (DownloadPreSignedURLResponse urlResponse : response.getData()) {
                urlMap.put(urlResponse.getFileIdentifier(), urlResponse.getPreSignedUrl());
            }
            
            return FileDownloadPreSignedUrlResponse.builder()
                    .data(urlMap)
                    .build();
                    
        } catch (TMSException e) {
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error getting presigned URLs", e);
            throw new TMSException(EXTERNAL_INVOCATION_EXCEPTION.name() ,"Failed to get presigned download URLs");
        }
    }


    @Override
    public List<Document> getDocuments(List<GetDocumentsDto> documentFilters) {
        log.info("Getting documents for {} filters", documentFilters.size());
        
        List<Document> allDocuments = new ArrayList<>();
        
        for (GetDocumentsDto filter : documentFilters) {
            Specification<Document> spec = DocumentSpecifications.byDocumentFilter(filter);
            List<Document> documents = documentRepository.findAll(spec);
            allDocuments.addAll(documents);
        }
        
        return allDocuments;
    }

}
