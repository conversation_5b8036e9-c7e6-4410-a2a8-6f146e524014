package com.dpw.ctms.move.service.document;

import com.dpw.ctms.move.enums.DocumentType;
import com.dpw.ctms.move.dto.document.DocumentGenerationContextDTO;
import com.dpw.ctms.move.enums.Tenant;

import java.util.concurrent.CompletableFuture;

public interface DocumentGenerator<T> {
    /**
     * Fills the document generation context with all required data
     */
    CompletableFuture<DocumentGenerationContextDTO<T>> generateContext(String tripCode, Tenant tenant);

    /**
     * Converts the filled context to JSON object
     */
    Object generateJson(String tripCode, Tenant tenant);

    /**
     * Returns the document type this generator handles
     */
    DocumentType getDocumentType();
}

