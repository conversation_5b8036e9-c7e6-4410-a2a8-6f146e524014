package com.dpw.ctms.move.service.impl;

import com.dpw.ctms.move.dto.EntityTypeWithIdsDTO;
import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.ShipmentTask;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.enums.EntityType;
import com.dpw.ctms.move.enums.ShipmentStatus;
import com.dpw.ctms.move.mapper.ShipmentViewMapper;
import com.dpw.ctms.move.repository.ShipmentRepository;
import com.dpw.ctms.move.repository.ShipmentTaskRepository;
import com.dpw.ctms.move.repository.TaskRepository;
import com.dpw.ctms.move.request.ShipmentListingRequest;
import com.dpw.ctms.move.response.ListResponse;
import com.dpw.ctms.move.response.ShipmentListingResponse;
import com.dpw.ctms.move.response.ShipmentViewResponse;
import com.dpw.ctms.move.service.IShipmentService;
import com.dpw.ctms.move.service.ShipmentFilteringService;
import com.dpw.ctms.move.specification.ShipmentSpecifications;
import com.dpw.tmsutils.exception.TMSException;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import static com.dpw.ctms.move.constants.ErrorMessageConstant.INVALID_SHIPMENT_CODE;
import static com.dpw.ctms.move.constants.ErrorMessageConstant.INVALID_SHIPMENT_ID;
import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.DATA_NOT_FOUND;
import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.INVALID_REQUEST;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;


@Slf4j
@Service
@RequiredArgsConstructor
public class ShipmentServiceImpl implements IShipmentService {

    private final ShipmentFilteringService shipmentFilteringService;
    private final ShipmentRepository shipmentRepository;
    private final ShipmentViewMapper shipmentViewMapper;
    private final TaskRepository taskRepository;
    private final ShipmentTaskRepository shipmentTaskRepository;


    @Override
    public Shipment findShipmentById(Long id) {
        return shipmentRepository.findById(id).orElseThrow(() -> {
            log.error("Shipment id {} not found", id);
            return new TMSException(
                    DATA_NOT_FOUND.name(),
                    String.format(INVALID_SHIPMENT_ID, id)
            );
        });
    }

    @Override
    public ListResponse<ShipmentListingResponse> listShipments(ShipmentListingRequest shipmentListingRequest) {
        return shipmentFilteringService.filterShipments(shipmentListingRequest);
    }

    @Override
    public List<Shipment> getAllByCodes(Set<String> codes) {
        return shipmentRepository.findAllByCodeInAndDeletedAtIsNull(codes);
    }

    @Override
    public Shipment saveShipment(Shipment entity) {
        return shipmentRepository.save(entity);
    }

    @Override
    public Shipment findShipmentByCode(String code) {
        return shipmentRepository.findByCodeAndDeletedAtIsNull(code).orElseThrow(() -> {
            log.error("Shipment code {} not found", code);
            return new TMSException(
                    DATA_NOT_FOUND.name(),
                    String.format(INVALID_SHIPMENT_CODE, code)
            );
        });
    }

    @Override
    @Transactional
    public ShipmentViewResponse getShipmentView(String shipmentCode) {
        Shipment shipment = getShipmentByCodeWithAllDetails(shipmentCode);
        return shipmentViewMapper.toResponse(shipment);
    }
    @Override
    public void discardShipments(Set<Shipment> shipments) {
        shipments.stream()
                .filter(Objects::nonNull)
                .forEach(shipment -> {shipment.setStatus(ShipmentStatus.DISCARDED);
                    shipment.setDeletedAt(System.currentTimeMillis());
                });
    }

    @Override
    public boolean areShipmentsCancellable(List<Shipment> shipments) {
        return shipments.stream()
                .allMatch(shipment ->
                        shipment.getStatus().equals(ShipmentStatus.ASSIGNED) ||
                                shipment.getStatus().equals(ShipmentStatus.ALLOCATED));
    }

    private Shipment getShipmentByCodeWithAllDetails(String shipmentCode) {
        return shipmentRepository.findByCodeAndDeletedAtIsNull(shipmentCode)
                .orElseThrow(() -> new TMSException(INVALID_REQUEST.name(), "Shipment not found with code: " + shipmentCode));
    }

    @Override
    public List<Shipment> getShipmentsForEntities(List<EntityTypeWithIdsDTO> entityTypeDTOs) {
        if (entityTypeDTOs.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<Shipment> allShipments = new ArrayList<>();
        
        try {
            for (EntityTypeWithIdsDTO entityTypeDTO : entityTypeDTOs) {
                String entityType = entityTypeDTO.getEntityType();
                Set<String> entityIds = entityTypeDTO.getEntityIds();
                
                EntityType type = EntityType.valueOf(entityType);
                
                switch (type) {
                    case SHIPMENT -> {
                        List<Shipment> shipments = shipmentRepository.findAllByCodeInAndDeletedAtIsNull(entityIds);
                        allShipments.addAll(shipments);
                    }
                    case TRIP -> {
                        Specification<Shipment> spec = ShipmentSpecifications.tripIdsIn(entityIds.stream().toList())
                                .and(ShipmentSpecifications.notDeleted());
                        allShipments.addAll(shipmentRepository.findAll(spec));
                    }
                    case TASK -> {
                        List<Task> tasks = taskRepository.findAllByCodeInAndDeletedAtIsNull(entityIds);
                        if (!tasks.isEmpty()) {
                            List<Long> taskIds = tasks.stream()
                                    .map(Task::getId)
                                    .collect(Collectors.toList());
                            
                            List<ShipmentTask> shipmentTasks = shipmentTaskRepository.findAllByTaskIdsAndDeletedAtIsNull(taskIds);
                            List<Shipment> taskShipments = shipmentTasks.stream()
                                    .map(ShipmentTask::getShipment)
                                    .filter(Objects::nonNull)
                                    .distinct()
                                    .toList();
                            allShipments.addAll(taskShipments);
                        }
                    }
                    default -> log.warn("Unsupported entity type: {}", entityType);
                }
            }
        } catch (Exception e) {
            log.error("Error getting shipments for entities: {}", entityTypeDTOs, e);
        }
        
        return allShipments.stream()
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateDocumentAttachedStatus(List<Shipment> shipments, boolean isDocumentAttached) {
        log.info("Updating isDocumentAttached flag for {} shipments to {}", shipments.size(), isDocumentAttached);
        shipments.forEach(shipment -> {
            shipment.setIsDocumentAttached(isDocumentAttached);
            log.info("Setting isDocumentAttached={} for shipment: {}", isDocumentAttached, shipment.getCode());
        });

        List<Shipment> savedShipments = shipmentRepository.saveAll(shipments);
        log.info("Updated isDocumentAttached flag for {} shipments", savedShipments.size());
    }
}

