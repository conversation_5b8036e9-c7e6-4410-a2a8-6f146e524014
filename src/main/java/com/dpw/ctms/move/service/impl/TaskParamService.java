package com.dpw.ctms.move.service.impl;

import com.dpw.ctms.move.dto.ParamValueShipmentDTO;
import com.dpw.ctms.move.dto.ParamValueVehicleOperatorDTO;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.TaskParam;
import com.dpw.ctms.move.enums.TaskParamType;
import com.dpw.ctms.move.service.ITaskParamService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class TaskParamService implements ITaskParamService {

    public Optional<ParamValueShipmentDTO> getShipmentCode(Task task) {
        return task.getTaskParams().stream()
                .filter(param -> TaskParamType.SHIPMENT.equals(param.getParamName()))
                .map(TaskParam::getParamValue)
                .filter(ParamValueShipmentDTO.class::isInstance)
                .map(ParamValueShipmentDTO.class::cast)
                .findFirst();
    }

    @Override
    public List<ParamValueVehicleOperatorDTO> getVehicleOperators(Task task) {
        return task.getTaskParams().stream()
                .filter(param -> TaskParamType.VEHICLE_OPERATOR.equals(param.getParamName()))
                .map(TaskParam::getParamValue)
                .filter(ParamValueVehicleOperatorDTO.class::isInstance)
                .map(ParamValueVehicleOperatorDTO.class::cast).collect(Collectors.toList());
    }

    @Override
    public void discardTaskParams(Set<TaskParam> taskParams) {
        taskParams.forEach(taskParam -> taskParam.setDeletedAt(System.currentTimeMillis()));
    }
}
