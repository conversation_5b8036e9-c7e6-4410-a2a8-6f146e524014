package com.dpw.ctms.move.service.event.impl;

import com.dpw.ctms.move.dto.producer.EventRequestDTO;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.enums.StateMachineEntityType;
import com.dpw.ctms.move.request.common.IntegratorMessageHeader;
import com.dpw.ctms.move.request.common.IntegratorMessageRequest;
import com.dpw.ctms.move.request.common.MessageRequest;
import com.dpw.ctms.move.request.message.TripStatusUpdateMessage;
import com.dpw.ctms.move.response.message.IntegratorMessageResponse;
import com.dpw.ctms.move.service.IEventProcessorService;
import com.dpw.ctms.move.service.event.IEntityEventHandler;
import com.dpw.ctms.move.service.event.IEntityEventManager;
import com.dpw.tmsutils.annotation.LogExecutionTime;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import static com.dpw.ctms.move.constants.MoveConstants.SOURCE_MOVE;
import static com.dpw.ctms.move.enums.MessageActionType.TRIP_STATUS_UPDATE;
import static com.dpw.ctms.move.mapper.TimeMapper.toTimeDTO;


@RequiredArgsConstructor
@Service
@Slf4j
@LogExecutionTime
public class TripEventHandlerImpl implements IEntityEventHandler<Trip> {

    private final IEventProcessorService<TripStatusUpdateMessage> eventProcessorService;
    private final IEntityEventManager entityEventManger;

    @Value("${kafka.producer.topics.ctms-move-trip-events.name}")
    private String tripStatusUpdateTopicName;

    @PostConstruct
    public void register() {
        /**TODO not use StateMachineEntityType here rather define and use from KafkaMessageEntityType enum**/
        entityEventManger.register(StateMachineEntityType.TRIP.name(), this);
    }

    @Override
    public IntegratorMessageResponse updateStatusEvent(EventRequestDTO<Trip> eventRequest) {
        Trip originalEntity = eventRequest.getOriginalEntity();
        Trip updatedEntity = eventRequest.getUpdatedEntity();
        try {
            log.info("Processing trip status update event after status update={}→{}",
                    originalEntity.getStatus(), updatedEntity.getStatus());

            TripStatusUpdateMessage tripStatusUpdateMessage = TripStatusUpdateMessage.builder()
                    .currentStatus(updatedEntity.getStatus().name())
                    .previousStatus(originalEntity.getStatus().name())
                    .tripCode(updatedEntity.getCode())
                    .extTripCode(updatedEntity.getCode())
                    .startTime(toTimeDTO(updatedEntity.getActualStartAt()))
                    .endTime(toTimeDTO(updatedEntity.getActualEndAt()))
                    .updatedBy(updatedEntity.getUpdatedBy())
                    .updatedAt(updatedEntity.getUpdatedAt())
                    .eventType(eventRequest.getEventType())
                    .comments(eventRequest.getComments())
                    .build();

            IntegratorMessageRequest<TripStatusUpdateMessage> messageRequest =
                    IntegratorMessageRequest.<TripStatusUpdateMessage>builder()
                            .transactionContext(IntegratorMessageHeader.builder()
                                    .action(TRIP_STATUS_UPDATE.name())
                                    .dateTime(System.currentTimeMillis())
                                    .source(SOURCE_MOVE)
                                    .topic(tripStatusUpdateTopicName)
                                    .build())
                            .message(MessageRequest.<TripStatusUpdateMessage>builder().
                                    item(tripStatusUpdateMessage).build())
                            .build();

            IntegratorMessageResponse response = eventProcessorService.processRequest(TRIP_STATUS_UPDATE.name(),messageRequest);
            log.info("Successfully published Kafka message for trip status update: trip={}, status={}→{}",
                    updatedEntity.getCode(), originalEntity.getStatus(), updatedEntity.getStatus());
            return response;
        } catch (Exception e) {
            log.error("Failed to publish Kafka message for rip status update: trip={}, status={}→{}, error={}",
                    updatedEntity.getCode(), originalEntity.getStatus(), updatedEntity.getStatus(), e.getMessage(), e);
        }

        return IntegratorMessageResponse.builder().build();
    }
}
