package com.dpw.ctms.move.integration.service.impl;

import com.dpw.ctms.move.config.ConfigService;
import com.dpw.ctms.move.enums.Tenant;
import com.dpw.ctms.move.integration.service.IConfigServiceIntegrator;
import com.dpw.tmsutils.threadlocal.TenantContext;
import com.dpw.tmsutils.utils.ObjectMapperUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class UnleashConfigServiceIntegratorImpl implements IConfigServiceIntegrator {
    private final ConfigService configService;
    public JsonNode fetchConfig(String key) {
        return configService.getConfig(key,
                Tenant.valueOf(TenantContext.getCurrentTenant()));
    }

    public <T> T fetchConfig(String key, TypeReference<T> typeReference) {
        JsonNode config = configService.getConfig(key,
                Tenant.valueOf(TenantContext.getCurrentTenant()));
        return ObjectMapperUtil.getObjectMapper().convertValue(config, typeReference);
    }
}
