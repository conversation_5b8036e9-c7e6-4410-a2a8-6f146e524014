package com.dpw.ctms.move.integration.response.oms.customerorder;

import com.dpw.ctms.move.integration.response.LabelValue;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomerOrderRecord {
    private Long id;
    private String code;
    private Long createdAt;
    private Long updatedAt;
    private String createdBy;
    private String updatedBy;
    private Customer customer;
    private Location origin;
    private Location destination;
    private TimeInfo expectedPickupTime;
    private TimeInfo expectedDeliveryTime;
    private LabelValue movementType;
    private LabelValue status;
    private Metadata metadata;
    private List<LineItem> lineItems;
    private List<DocumentDetail> documentDetails;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Metadata {
        private String customerOrderNumber;
        private String internalReferenceNumber;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Customer {
        private String id;
        private LabelValue customerType;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Location {
        private String id;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TimeInfo {
        private Long epoch;
        private String timeZoneCode;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class LineItem {
        private Long id;
        private String code;
        private Long createdAt;
        private Long updatedAt;
        private String createdBy;
        private String updatedBy;
        private Double requestedQuantity;
        private Double acceptedQuantity;
        private String requestedQuantityUom;
        private LabelValue status;
        private LineItemMetadata metadata;
        private List<Consignment> consignments;
        private List<Product> products;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class LineItemMetadata {
        private String specialInstructions;
        private String lineIdentifierNumber1;
        private String lineIdentifierNumber2;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Consignment {
        private Long id;
        private String code;
        private Long createdAt;
        private Long updatedAt;
        private String createdBy;
        private String updatedBy;
        private LabelValue status;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Product {
        private Long id;
        private ResourceDetails resourceDetails;
        private List<Property> properties;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ResourceDetails {
        private String id;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Property {
        private LabelValue propertyName;
        private Double propertyValue;
        private UnitOfMeasurement unitOfMeasurement;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class UnitOfMeasurement {
        private String id;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DocumentDetail {
        private LabelValue type;
        private List<FileDetail> fileDetails;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FileDetail {
        private Long documentId;
        private String asyncMappingUUID;
        private String fileName;
        private Long fileSize;
        private String fileType;
        private String downloadPreSignedURL;
        private String externalDocumentIdentifier;
    }
}