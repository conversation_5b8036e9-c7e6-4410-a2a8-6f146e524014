package com.dpw.ctms.move.integration.response.taskmanager;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetTaskByRegistrationCodeResponse {

    String taskRegistrationCode;
    String extTaskTransactionCode;
    String extTaskMasterCode;
    List<MandatoryConfig> mandatoryConfigs;

    @Data
    @Builder
    @FieldDefaults(level = AccessLevel.PRIVATE)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class MandatoryConfig {
        String source;
        Boolean isMandatory;
    }
}
