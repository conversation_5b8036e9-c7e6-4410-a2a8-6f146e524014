package com.dpw.ctms.move.integration.mapper.oms;

import com.dpw.ctms.move.dto.TimeDTO;
import com.dpw.ctms.move.integration.response.LabelValue;
import com.dpw.ctms.move.integration.response.oms.consignment.ConsignmentRecord;
import com.dpw.ctms.move.integration.dto.oms.OmsConsignmentDto;
import com.dpw.ctms.move.util.DateTimeUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.mapstruct.*;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public abstract class OmsConsignmentMapper {

    @Autowired
    protected DateTimeUtil dateTimeUtil;

    @Mapping(target = "status", source = "status", qualifiedByName = "mapLabelValueToStatusDetails")
    @Mapping(target = "movementType", source = "movementType", qualifiedByName = "mapLabelValueToDto")
    @Mapping(target = "customer", source = "customer", qualifiedByName = "mapCustomer")
    @Mapping(target = "origin", source = "origin", qualifiedByName = "mapLocation")
    @Mapping(target = "destination", source = "destination", qualifiedByName = "mapLocation")
    @Mapping(target = "expectedPickupTime", source = "expectedPickupTime", qualifiedByName = "mapTimeInfoToDto")
    @Mapping(target = "expectedDeliveryTime", source = "expectedDeliveryTime", qualifiedByName = "mapTimeInfoToDto")
    @Mapping(target = "products", source = "products", qualifiedByName = "mapProducts")
    public abstract OmsConsignmentDto mapToOmsConsignmentDto(ConsignmentRecord consignmentRecord);

    @Named("mapLabelValueToStatusDetails")
    public OmsConsignmentDto.StatusDetails mapLabelValueToStatusDetails(LabelValue labelValue) {
        if (ObjectUtils.isEmpty(labelValue)) {
            return null;
        }
        return OmsConsignmentDto.StatusDetails.builder()
                .label(labelValue.getLabel())
                .value(labelValue.getValue())
                .build();
    }

    @Named("mapLabelValueToDto")
    public OmsConsignmentDto.LabelValue mapLabelValueToDto(LabelValue labelValue) {
        if (ObjectUtils.isEmpty(labelValue)) {
            return null;
        }
        return OmsConsignmentDto.LabelValue.builder()
                .label(labelValue.getLabel())
                .value(labelValue.getValue())
                .build();
    }

    @Named("mapCustomer")
    public OmsConsignmentDto.Customer mapCustomer(ConsignmentRecord.Customer customer) {
        if (ObjectUtils.isEmpty(customer)) {
            return null;
        }
        return OmsConsignmentDto.Customer.builder()
                .id(customer.getId())
                .customerType(mapLabelValueToDto(customer.getCustomerType()))
                .build();
    }

    @Named("mapLocation")
    public OmsConsignmentDto.Location mapLocation(ConsignmentRecord.Location location) {
        if (ObjectUtils.isEmpty(location)) {
            return null;
        }
        return OmsConsignmentDto.Location.builder()
                .id(location.getId())
                .build();
    }

    @Named("mapTimeInfoToDto")
    public TimeDTO mapTimeInfoToDto(ConsignmentRecord.TimeInfo timeInfo) {
        if (ObjectUtils.isEmpty(timeInfo)) {
            return null;
        }
        return TimeDTO.builder()
                .epoch(timeInfo.getEpoch())
                .timezone(timeInfo.getTimeZoneCode())
                .build();
    }

    @Named("mapProducts")
    public List<OmsConsignmentDto.Product> mapProducts(List<ConsignmentRecord.Product> products) {
        if (ObjectUtils.isEmpty(products)) {
            return null;
        }
        return products.stream()
                .map(this::mapProductToDto)
                .collect(Collectors.toList());
    }

    private OmsConsignmentDto.Product mapProductToDto(ConsignmentRecord.Product product) {
        if (ObjectUtils.isEmpty(product)) {
            return null;
        }
        return OmsConsignmentDto.Product.builder()
                .id(product.getId())
                .resourceId(!ObjectUtils.isEmpty(product.getResourceDetails()) ? product.getResourceDetails().getId() : null)
                .properties(mapPropertiesToDto(product.getProperties()))
                .build();
    }

    private List<OmsConsignmentDto.Property> mapPropertiesToDto(List<ConsignmentRecord.Product.Property> properties) {
        if (ObjectUtils.isEmpty(properties)) {
            return null;
        }
        return properties.stream()
                .map(this::mapPropertyToDto)
                .collect(Collectors.toList());
    }

    private OmsConsignmentDto.Property mapPropertyToDto(ConsignmentRecord.Product.Property property) {
        if (ObjectUtils.isEmpty(property)) {
            return null;
        }
        return OmsConsignmentDto.Property.builder()
                .propertyNameLabel(!ObjectUtils.isEmpty(property.getPropertyName()) ? property.getPropertyName().getLabel() : null)
                .propertyNameValue(!ObjectUtils.isEmpty(property.getPropertyName()) ? property.getPropertyName().getValue() : null)
                .propertyValue(property.getPropertyValue())
                .unitOfMeasurementId(!ObjectUtils.isEmpty(property.getUnitOfMeasurement()) ? property.getUnitOfMeasurement().getId() : null)
                .build();
    }
}