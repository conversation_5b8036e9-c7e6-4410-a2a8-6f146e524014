package com.dpw.ctms.move.integration.response.resource.operator;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetOperatorDetailsListResponse {
    private Long id;
    private String code;
    private String firstName;
    private String lastName;
    private Long dateOfBirth;
    private String employeeNumber;
    private String licenseNumber;
    private LabelValuePairResponse status;
    private Boolean isAllowedToCarryDangerousGoods;
    private String uniqueIdentifier;
    private LabelValuePairResponse uniqueIdentifierType;
    private VendorDetailsResponse vendorDetails;
    private OrganisationHierarchyLevelDetailsResponse organisationHierarchyLevelDetails;
    private List<String> vehicleOperatorType;
    private ContactDetailsResponse contactDetails;
    private CrpDetailsResponse crpDetails;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ContactDetailsResponse {
        private Long id;
        private String emailId;
        private String primaryPhoneNumber;
        private String primaryCountryCode;
        private String secondaryPhoneNumber;
        private String secondaryCountryCode;
    }
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VendorDetailsResponse {
        private String code;
        private String name;
        private String id;
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrganisationHierarchyLevelDetailsResponse {
        private Long id;
        private String name;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CrpDetailsResponse {
        private String crpUserUUID;
        private String userName;
        private String managerEmailId;
        private String managerName;
        private String managerUUID;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LabelValuePairResponse {
        private String label;
        private String value;
    }

}
