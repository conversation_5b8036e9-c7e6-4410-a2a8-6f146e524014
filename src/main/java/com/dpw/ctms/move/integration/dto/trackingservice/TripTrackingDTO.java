package com.dpw.ctms.move.integration.dto.trackingservice;

import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.integration.dto.resource.ResourceFacilitiesDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TripTrackingDTO {
    private Trip initialTrip;
    private Trip currentTrip;
    private Task task;
    private String eventType;
    private List<String> externalCustomerOrderReferenceNumbers;
    private List<ResourceFacilitiesDto> facilityDetails;
}
