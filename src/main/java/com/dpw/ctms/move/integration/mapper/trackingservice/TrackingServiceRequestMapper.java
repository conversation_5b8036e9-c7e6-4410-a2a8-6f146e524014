package com.dpw.ctms.move.integration.mapper.trackingservice;


import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.entity.VehicleOperatorResource;
import com.dpw.ctms.move.integration.dto.trackingservice.TripTrackingDTO;
import com.dpw.ctms.move.integration.request.trackingservice.TripContext;
import com.dpw.ctms.move.integration.request.trackingservice.TripTrackingRequest;
import com.dpw.ctms.move.util.DateTimeUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

import static com.dpw.ctms.move.constants.MoveConstants.SYSTEM_NAME;
import static com.dpw.ctms.move.constants.TrackingServiceConstants.CLOSE_TRIP;
import static com.dpw.ctms.move.constants.TrackingServiceConstants.START_TRIP;
import static com.dpw.ctms.move.constants.TrackingServiceConstants.TRIP_IDENTIFIER_TYPE;

@Component
@RequiredArgsConstructor
public class TrackingServiceRequestMapper {
    private final DateTimeUtil dateTimeUtil;
    public TripTrackingRequest mapTripTrackingRequest(TripTrackingDTO tripTrackingDTO) {
        return TripTrackingRequest.builder()
                .source(SYSTEM_NAME)
                .tripContext(buildTripContext(tripTrackingDTO))
                .data(buildData(tripTrackingDTO))
                .build();
    }

    private TripContext buildTripContext(TripTrackingDTO tripTrackingDTO) {
        return TripContext.builder()
                .traceId(tripTrackingDTO.getCurrentTrip().getCode())
                .tripTypeCode(tripTrackingDTO.getEventType())
                .systemName(SYSTEM_NAME)
                .build();
    }

    private TripTrackingRequest.DataWrapper buildData(TripTrackingDTO tripTrackingDTO) {
        return TripTrackingRequest.DataWrapper.builder()
                .target(buildTarget(tripTrackingDTO))
                .build();
    }

    private TripTrackingRequest.DataWrapper.Target buildTarget(TripTrackingDTO tripTrackingDTO) {
        return TripTrackingRequest.DataWrapper.Target.builder()
                .trip(buildTrip(tripTrackingDTO))
                .build();
    }

    private TripTrackingRequest.DataWrapper.Target.Trip buildTrip(TripTrackingDTO tripTrackingDTO) {
        Trip currentTrip = tripTrackingDTO.getCurrentTrip();
        Task task = tripTrackingDTO.getTask();
        String eventType = tripTrackingDTO.getEventType();

        TripTrackingRequest.DataWrapper.Target.Trip.TripBuilder builder = TripTrackingRequest.DataWrapper.Target.Trip.builder()
                .tripIdentifierType(TRIP_IDENTIFIER_TYPE)
                .tripIdentifierValue(currentTrip.getCode())
                .tripNumber(currentTrip.getCode())
                .driverUserID(getVehicleOperatorUserId(tripTrackingDTO))
                .addedConsignments(buildAddedConsignments(tripTrackingDTO))
                .wayPoints(buildWayPoints(tripTrackingDTO));

        if (task.getActualStartAt() != null) {
            String timeZone = task.getActualStartAt().getTimezone();
            String timestamp = dateTimeUtil.isoOffsetDateTimeString(task.getActualStartAt().getEpoch(), "");

            builder.timeZone(timeZone);

            switch (eventType) {
                case START_TRIP -> builder.dispatchTime(timestamp);
                case CLOSE_TRIP -> builder.closureTime(timestamp);
            }
        }

        return builder.build();
    }

    private List<TripTrackingRequest.DataWrapper.Target.AddedConsignment> buildAddedConsignments(TripTrackingDTO tripTrackingDTO) {
        return tripTrackingDTO.getExternalCustomerOrderReferenceNumbers().stream()
                .map(externalCustomerReferenceNumber -> TripTrackingRequest.DataWrapper.Target.AddedConsignment.builder()
                        .number(externalCustomerReferenceNumber)
                        .build())
                .toList();
    }
    private List<TripTrackingRequest.DataWrapper.Target.Waypoint> buildWayPoints(TripTrackingDTO tripTrackingDTO) {
        return tripTrackingDTO.getFacilityDetails().stream()
                .map(facilityDetailsDTO -> TripTrackingRequest.DataWrapper.Target.Waypoint.builder()
                        .facilityName(facilityDetailsDTO.getName())
                        .city(getCityName(facilityDetailsDTO))
                        .pincode(getPostalCode(facilityDetailsDTO))
                        .addressLine1(getAddressLine(facilityDetailsDTO))
                        .build())
                .toList();
    }
    
    private String getCityName(com.dpw.ctms.move.integration.dto.resource.ResourceFacilitiesDto facility) {
        if (!ObjectUtils.isEmpty(facility) && !ObjectUtils.isEmpty(facility.getAddressDetails()) && 
            !ObjectUtils.isEmpty(facility.getAddressDetails().getCityDetails())) {
            return facility.getAddressDetails().getCityDetails().getName();
        }
        return null;
    }
    
    private String getPostalCode(com.dpw.ctms.move.integration.dto.resource.ResourceFacilitiesDto facility) {
        if (!ObjectUtils.isEmpty(facility) && !ObjectUtils.isEmpty(facility.getAddressDetails())) {
            return facility.getAddressDetails().getPostalCode();
        }
        return null;
    }
    
    private String getAddressLine(com.dpw.ctms.move.integration.dto.resource.ResourceFacilitiesDto facility) {
        if (!ObjectUtils.isEmpty(facility) && !ObjectUtils.isEmpty(facility.getAddressDetails())) {
            return facility.getAddressDetails().getAddressLine();
        }
        return null;
    }
    private String getVehicleOperatorUserId(TripTrackingDTO tripTrackingDTO) {
        return Optional.ofNullable(tripTrackingDTO.getCurrentTrip().getVehicleOperatorResources())
                .stream()
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .findFirst()
                .map(VehicleOperatorResource::getCrpId)
                .orElse("");
    }
}
