package com.dpw.ctms.move.integration.response.oms.product;

import com.dpw.ctms.move.integration.response.LabelValue;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class OmsProductDetails {
    private Long id;
    private String resourceId;
    private List<Property> properties;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Property {
        private LabelValue propertyName;
        private Double propertyValue;
        private String unitOfMeasurementId;
    }
}