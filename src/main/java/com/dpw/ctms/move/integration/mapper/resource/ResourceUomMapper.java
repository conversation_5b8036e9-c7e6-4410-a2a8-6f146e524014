package com.dpw.ctms.move.integration.mapper.resource;

import com.dpw.ctms.move.integration.dto.resource.ResourceUomDto;
import com.dpw.ctms.move.integration.response.resource.uom.UomRecord;
import org.mapstruct.*;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface ResourceUomMapper {

    ResourceUomDto mapToResourceUomDto(UomRecord uomRecord);
}