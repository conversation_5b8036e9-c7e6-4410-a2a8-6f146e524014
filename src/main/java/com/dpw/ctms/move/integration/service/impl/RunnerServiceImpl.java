package com.dpw.ctms.move.integration.service.impl;

import com.dpw.ctms.move.dto.CustomerOrderDTO;
import com.dpw.ctms.move.dto.PaginationDTO;
import com.dpw.ctms.move.entity.Document;
import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.integration.adapter.OmsServiceAdapter;
import com.dpw.ctms.move.integration.adapter.RunnerServiceAdapter;
import com.dpw.ctms.move.integration.mapper.oms.OmsCustomerOrderMapper;
import com.dpw.ctms.move.integration.request.runner.RunnerDocumentRequest;
import com.dpw.ctms.move.integration.response.oms.OmsListResponse;
import com.dpw.ctms.move.integration.response.oms.customerorder.CustomerOrderRecord;
import com.dpw.ctms.move.integration.response.runner.RunnerDocumentResponse;
import com.dpw.ctms.move.integration.service.IRunnerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class RunnerServiceImpl implements IRunnerService {

    private final RunnerServiceAdapter runnerServiceAdapter;
    private final OmsServiceAdapter omsServiceAdapter;
    private final OmsCustomerOrderMapper omsCustomerOrderMapper;

    @Override
    public void sendDocumentToRunner(Document document, List<Shipment> shipments) {
        sendDocumentsToRunner(List.of(document), shipments);
    }

    @Override
    public void sendDocumentsToRunner(List<Document> documents, List<Shipment> shipments) {
        try {
            log.info("Sending {} documents to Runner for {} shipments", documents.size(), shipments.size());
            
            // Filter shipments that have valid external customer order IDs
            List<Shipment> validShipments = shipments.stream()
                .filter(shipment -> StringUtils.isNotBlank(shipment.getExternalCustomerOrderId()))
                .toList();
            
            if (validShipments.isEmpty()) {
                log.warn("No external customer order IDs found in shipments, skipping Runner integration");
                return;
            }
            
            List<Long> customerOrderIds = validShipments.stream()
                .map(shipment -> Long.parseLong(shipment.getExternalCustomerOrderId()))
                .toList();
            
            // Call OMS list API once with all IDs
            OmsListResponse<CustomerOrderRecord> customerOrdersResponse = omsServiceAdapter.getCustomerOrdersByIds(
                    customerOrderIds,
                    PaginationDTO.builder().pageNo(0).pageSize(customerOrderIds.size()).build()
            );

            List<CustomerOrderRecord> customerOrders = Optional.ofNullable(customerOrdersResponse)
                    .map(OmsListResponse::getResponse)
                    .orElse(Collections.emptyList());
            
            // Create a map for quick lookup
            Map<Long, CustomerOrderDTO> customerOrderMap = customerOrders
                    .stream()
                    .map(omsCustomerOrderMapper::mapToCustomerOrderDTO)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(CustomerOrderDTO::getId, Function.identity(), (existing, replacement) -> existing));
            
            // Process each document-shipment combination as we need to update all CO for that shipment
            for (Document document : documents) {
                for (Shipment shipment : validShipments) {
                    try {
                        Long customerOrderId = Long.parseLong(shipment.getExternalCustomerOrderId());
                        CustomerOrderDTO customerOrder = customerOrderMap.get(customerOrderId);
                        
                        if (ObjectUtils.isEmpty(customerOrder)) {
                            log.warn("No customer order found for customerOrderId: {}, shipment: {}, skipping", 
                                customerOrderId, shipment.getCode());
                            continue;
                        }
                        
                        RunnerDocumentRequest runnerRequest = RunnerDocumentRequest.builder()
                                .fileName(document.getFileName())
                                .fileDownloadLink(document.getPresignedDownloadUrl())
                                .shipmentId(customerOrder.getMetadata().getCustomerOrderNumber())
                                .shipmentReferenceNumber(customerOrder.getMetadata().getInternalReferenceNumber())
                                .build();
                                
                        log.info("Sending POD document to Runner for shipmentId: {}, fileName: {}, entityType: {}", 
                            shipment.getCode(), document.getFileName(), document.getEntityType());
                            
                        RunnerDocumentResponse response = runnerServiceAdapter.sendPodDocument(runnerRequest);
                        
                        log.info("Successfully sent POD document to Runner for shipment: {}. Response: {}", 
                            shipment.getCode(), response);
                            
                    } catch (Exception e) {
                        log.error("Failed to send document {} to Runner for shipment: {}. Error: {}", 
                            document.getAsyncMappingUUID(), shipment.getCode(), e.getMessage(), e);
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("Failed to send documents to Runner. Error: {}", e.getMessage(), e);
            // Don't throw exception to avoid disrupting the document activation flow
        }
    }
}