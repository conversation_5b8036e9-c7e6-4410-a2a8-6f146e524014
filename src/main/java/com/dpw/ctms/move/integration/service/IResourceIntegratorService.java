package com.dpw.ctms.move.integration.service;

import com.dpw.ctms.move.integration.dto.resource.ResourceFacilitiesDto;
import com.dpw.ctms.move.integration.dto.resource.ResourceProductDetailsDto;
import com.dpw.ctms.move.integration.dto.resource.ResourceUomDto;

import java.util.List;

public interface IResourceIntegratorService {
    List<ResourceFacilitiesDto> getFacilitiesDTOs(List<String> externalFacilityCodes);
    List<ResourceProductDetailsDto> getProductDetailsDTOs(List<String> productIds);
    List<ResourceUomDto> getUomDTOs(List<String> uomIds);
}
