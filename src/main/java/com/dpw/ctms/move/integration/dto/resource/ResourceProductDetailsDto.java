package com.dpw.ctms.move.integration.dto.resource;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class ResourceProductDetailsDto {
    private Long id;
    private String name;
    private String code;
    private String unNumber;
    private ProductCategoryDetails productCategoryDetails;
    private StatusDetails status;
    private String description;
    private Boolean isContainerFillingRuleApplied;
    private Double filledPercentage;
    private List<PropertyDetails> properties;
    private Boolean isAttentionNeeded;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ProductCategoryDetails {
        private Long id;
        private String code;
        private String name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class StatusDetails {
        private String label;
        private String value;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class PropertyDetails {
        private Long id;
        private String propertyNameLabel;
        private String propertyNameValue;
        private String propertyValue;
        private Long uomId;
        private String uomName;
        private String uomCode;
        private String productCode;
    }
}