package com.dpw.ctms.move.integration.mapper.oms;

import com.dpw.ctms.move.dto.CustomerOrderDTO;
import com.dpw.ctms.move.dto.CustomerOrderMetaDataDTO;
import com.dpw.ctms.move.integration.response.LabelValue;
import com.dpw.ctms.move.integration.response.oms.customerorder.CustomerOrderRecord;
import org.mapstruct.*;

import java.util.List;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public abstract class OmsCustomerOrderMapper {

    @Mapping(target = "id", source = "id")
    @Mapping(target = "code", source = "code")
    @Mapping(target = "status", source = "status", qualifiedByName = "mapLabelValue")
    @Mapping(target = "metadata", source = "metadata")
    public abstract CustomerOrderDTO mapToCustomerOrderDTO(CustomerOrderRecord customerOrderRecord);

    public abstract List<CustomerOrderDTO> mapToCustomerOrderDTOList(List<CustomerOrderRecord> customerOrderRecords);

    @Mapping(target = "customerOrderNumber", source = "customerOrderNumber")
    @Mapping(target = "internalReferenceNumber", source = "internalReferenceNumber")
    public abstract CustomerOrderMetaDataDTO mapToCustomerOrderMetaDataDTO(CustomerOrderRecord.Metadata metadata);

    @Named("mapLabelValue")
    public String mapLabelValue(LabelValue labelValue) {
        return labelValue != null ? labelValue.getValue() : null;
    }
}