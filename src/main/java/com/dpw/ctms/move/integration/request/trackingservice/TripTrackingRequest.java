package com.dpw.ctms.move.integration.request.trackingservice;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TripTrackingRequest {
    private String source;
    private TripContext tripContext;
    private DataWrapper data;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class DataWrapper {
        private Target target;

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        @Builder
        public static class Target {
            private Trip trip;

            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            @Builder
            public static class Trip {
                private String tripNumber;
                private String tripIdentifierType;
                private String tripIdentifierValue;
                private String driverUserID;
                private String dispatchTime;
                private String closureTime;
                private String timeZone;
                private List<AddedConsignment> addedConsignments;
                private List<Waypoint> wayPoints;
            }

            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            @Builder
            public static class AddedConsignment {
                private String number;
            }

            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            @Builder
            public static class Waypoint {
                private String facilityName;
                private String addressLine1;
                private String addressLine2;
                private String city;
                private String state;
                private String pincode;
            }
        }
    }
}

