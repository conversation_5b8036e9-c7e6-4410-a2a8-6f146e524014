package com.dpw.ctms.move.integration.util;

import com.dpw.ctms.move.dto.PaginationDTO;
import com.dpw.ctms.move.integration.dto.oms.ConsignmentListRequestDTO;
import com.dpw.ctms.move.integration.request.OutboundPaginationRequest;
import com.dpw.ctms.move.integration.request.oms.OmsListRequest;
import com.dpw.ctms.move.integration.request.oms.consignment.ConsignmentListFilter;
import com.dpw.ctms.move.integration.request.oms.customerorder.CustomerOrderListFilter;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Utility class for creating OMS service requests
 */
@Component
public class OmsRequestUtil {

    /**
     * Creates a consignment list request with pagination and optional filter by consignment IDs
     *
     * @param consignmentListRequestDTO Optional to filter list by
     * @param paginationDTO Pagination parameters
     * @return ListRequest with appropriate filter
     */
    public static OmsListRequest<ConsignmentListFilter> createConsignmentListRequest(ConsignmentListRequestDTO consignmentListRequestDTO, PaginationDTO paginationDTO) {
        ConsignmentListFilter filter = null;
        if (consignmentListRequestDTO != null) {
            filter = ConsignmentListFilter.builder()
                    .ids(consignmentListRequestDTO.getIds())
                    .build();
        }

        return OmsListRequest.<ConsignmentListFilter>builder()
                .pagination(convertToPaginationRequest(paginationDTO))
                .filter(filter)
                .build();
    }

    /**
     * Creates a customer order list request with filter by customer order IDs
     *
     * @param customerOrderIds List of customer order IDs to filter by
     * @param paginationDTO Pagination parameters (optional)
     * @return ListRequest with appropriate filter
     */
    public static OmsListRequest<CustomerOrderListFilter> createCustomerOrderListRequest(List<Long> customerOrderIds, PaginationDTO paginationDTO) {
        CustomerOrderListFilter filter = CustomerOrderListFilter.builder()
                .ids(customerOrderIds)
                .build();

        return OmsListRequest.<CustomerOrderListFilter>builder()
                .pagination(convertToPaginationRequest(paginationDTO))
                .filter(filter)
                .build();
    }
    
    /**
     * Converts PaginationDTO to PaginationRequest
     */
    private static OutboundPaginationRequest convertToPaginationRequest(PaginationDTO paginationDTO) {
        if (paginationDTO == null) {
            return OutboundPaginationRequest.builder()
                    .pageNo(0)
                    .pageSize(10)
                    .build();
        }
        
        return OutboundPaginationRequest.builder()
                .pageNo(paginationDTO.getPageNoOrDefault())
                .pageSize(paginationDTO.getPageSizeOrDefault())
                .build();
    }
}
