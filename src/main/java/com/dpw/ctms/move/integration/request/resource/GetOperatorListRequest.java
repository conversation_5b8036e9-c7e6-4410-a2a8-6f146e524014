package com.dpw.ctms.move.integration.request.resource;

import com.dpw.ctms.move.request.common.Pagination;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetOperatorListRequest {
    private Pagination pagination;
    private GetOperatorListFilter filter;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonSerialize
    public static class GetOperatorListFilter {
        private List<Long> ids;
    }
}
