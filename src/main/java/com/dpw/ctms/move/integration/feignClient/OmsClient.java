package com.dpw.ctms.move.integration.feignClient;
import com.dpw.ctms.move.config.FeignConfig;
import com.dpw.ctms.move.constants.OmsServiceConstants;
import com.dpw.ctms.move.integration.request.oms.OmsListRequest;
import com.dpw.ctms.move.integration.request.oms.consignment.ConsignmentListFilter;
import com.dpw.ctms.move.integration.response.oms.OmsApiResponse;
import com.dpw.ctms.move.integration.response.oms.OmsListResponse;
import com.dpw.ctms.move.integration.response.oms.consignment.ConsignmentRecord;
import com.dpw.ctms.move.integration.request.oms.customerorder.CustomerOrderListFilter;
import com.dpw.ctms.move.integration.response.oms.customerorder.CustomerOrderRecord;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "oms-service", url = "${tms.service-url.oms}", configuration = FeignConfig.class)
public interface OmsClient {

    @PostMapping(OmsServiceConstants.Endpoints.GET_CONSIGNMENT_LIST)
    ResponseEntity<OmsApiResponse<OmsListResponse<ConsignmentRecord>>> getConsignmentList(@RequestBody OmsListRequest<ConsignmentListFilter> request);

    @PostMapping(OmsServiceConstants.Endpoints.GET_CUSTOMER_ORDER_LIST)
    ResponseEntity<OmsApiResponse<OmsListResponse<CustomerOrderRecord>>> getCustomerOrderList(@RequestBody OmsListRequest<CustomerOrderListFilter> request);

} 