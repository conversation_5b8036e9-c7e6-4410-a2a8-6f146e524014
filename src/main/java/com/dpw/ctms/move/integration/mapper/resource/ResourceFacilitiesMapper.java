package com.dpw.ctms.move.integration.mapper.resource;

import com.dpw.ctms.move.integration.dto.resource.ResourceFacilitiesDto;
import com.dpw.ctms.move.integration.response.LabelValue;
import com.dpw.ctms.move.integration.response.resource.facility.FacilityRecord;
import org.apache.commons.lang3.ObjectUtils;
import org.mapstruct.*;

import java.util.List;
import java.util.stream.Collectors;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface    ResourceFacilitiesMapper {

    @Mapping(target = "status", source = "status", qualifiedByName = "mapStatusDetails")
    @Mapping(target = "ownershipType", source = "ownershipType", qualifiedByName = "mapOwnershipTypeDetails")
    @Mapping(target = "mappedEntities", source = "mappedEntities", qualifiedByName = "mapMappedEntities")
    @Mapping(target = "pointOfContactDetails", source = "pointOfContactDetails", qualifiedByName = "mapPointOfContactDetails")
    @Mapping(target = "addressDetails", source = "addressDetails", qualifiedByName = "mapAddressDetails")
    ResourceFacilitiesDto mapToResourceFacilitiesDto(FacilityRecord facilityRecord);

    @Named("mapStatusDetails")
    default ResourceFacilitiesDto.StatusDetails mapStatusDetails(LabelValue status) {
        if (ObjectUtils.isEmpty(status)) {
            return null;
        }
        return ResourceFacilitiesDto.StatusDetails.builder()
                .label(status.getLabel())
                .value(status.getValue())
                .build();
    }

    @Named("mapOwnershipTypeDetails")
    default ResourceFacilitiesDto.OwnershipTypeDetails mapOwnershipTypeDetails(LabelValue ownershipType) {
        if (ObjectUtils.isEmpty(ownershipType)) {
            return null;
        }
        return ResourceFacilitiesDto.OwnershipTypeDetails.builder()
                .label(ownershipType.getLabel())
                .value(ownershipType.getValue())
                .build();
    }

    @Named("mapMappedEntities")
    default List<ResourceFacilitiesDto.MappedEntity> mapMappedEntities(
            List<FacilityRecord.IdCodeNameResponse> mappedEntities) {
        if (ObjectUtils.isEmpty(mappedEntities)) {
            return null;
        }
        return mappedEntities.stream()
                .map(this::mapMappedEntity)
                .collect(Collectors.toList());
    }

    default ResourceFacilitiesDto.MappedEntity mapMappedEntity(FacilityRecord.IdCodeNameResponse entity) {
        if (ObjectUtils.isEmpty(entity)) {
            return null;
        }
        return ResourceFacilitiesDto.MappedEntity.builder()
                .id(entity.getId())
                .name(entity.getName())
                .code(entity.getCode())
                .build();
    }

    @Named("mapPointOfContactDetails")
    default ResourceFacilitiesDto.PointOfContactDetails mapPointOfContactDetails(
            FacilityRecord.PointOfContactResponse pointOfContact) {
        if (ObjectUtils.isEmpty(pointOfContact)) {
            return null;
        }
        return ResourceFacilitiesDto.PointOfContactDetails.builder()
                .id(pointOfContact.getId())
                .firstName(pointOfContact.getFirstName())
                .lastName(pointOfContact.getLastName())
                .designation(pointOfContact.getDesignation())
                .emailId(pointOfContact.getEmailId())
                .phoneNumber(pointOfContact.getPhoneNumber())
                .countryCode(pointOfContact.getCountryCode())
                .additionalEmailIds(pointOfContact.getAdditionalEmailIds())
                .build();
    }

    @Named("mapAddressDetails")
    default ResourceFacilitiesDto.AddressDetails mapAddressDetails(FacilityRecord.AddressResponse address) {
        if (ObjectUtils.isEmpty(address)) {
            return null;
        }
        return ResourceFacilitiesDto.AddressDetails.builder()
                .externalAddressCode(address.getExternalAddressCode())
                .addressLine(address.getAddressLine())
                .postalCode(address.getPostalCode())
                .suburbDetails(mapSuburbDetails(address.getSuburbDetails()))
                .cityDetails(mapCityDetails(address.getCityDetails()))
                .provinceDetails(mapProvinceDetails(address.getProvinceDetails()))
                .countryDetails(mapCountryDetails(address.getCountryDetails()))
                .point(mapPinPointDetails(address.getPoint()))
                .build();
    }

    default ResourceFacilitiesDto.AddressDetails.SuburbDetails mapSuburbDetails(
            FacilityRecord.AddressResponse.SuburbResponse suburb) {
        if (ObjectUtils.isEmpty(suburb)) {
            return null;
        }
        return ResourceFacilitiesDto.AddressDetails.SuburbDetails.builder()
                .name(suburb.getName())
                .externalSuburbCode(suburb.getExternalSuburbCode())
                .build();
    }

    default ResourceFacilitiesDto.AddressDetails.CityDetails mapCityDetails(
            FacilityRecord.AddressResponse.CityResponse city) {
        if (ObjectUtils.isEmpty(city)) {
            return null;
        }
        return ResourceFacilitiesDto.AddressDetails.CityDetails.builder()
                .name(city.getName())
                .externalCityCode(city.getExternalCityCode())
                .build();
    }

    default ResourceFacilitiesDto.AddressDetails.ProvinceDetails mapProvinceDetails(
            FacilityRecord.AddressResponse.ProvinceResponse province) {
        if (ObjectUtils.isEmpty(province)) {
            return null;
        }
        return ResourceFacilitiesDto.AddressDetails.ProvinceDetails.builder()
                .id(province.getId())
                .name(province.getName())
                .externalProvinceCode(province.getExternalProvinceCode())
                .build();
    }

    default ResourceFacilitiesDto.AddressDetails.CountryDetails mapCountryDetails(
            FacilityRecord.AddressResponse.CountryResponse country) {
        if (ObjectUtils.isEmpty(country)) {
            return null;
        }
        return ResourceFacilitiesDto.AddressDetails.CountryDetails.builder()
                .name(country.getName())
                .externalCountryCode(country.getExternalCountryCode())
                .build();
    }

    default ResourceFacilitiesDto.AddressDetails.PinPointDetails mapPinPointDetails(
            FacilityRecord.AddressResponse.PinPointResponse point) {
        if (ObjectUtils.isEmpty(point)) {
            return null;
        }
        return ResourceFacilitiesDto.AddressDetails.PinPointDetails.builder()
                .lat(point.getLat())
                .lng(point.getLng())
                .build();
    }
}