package com.dpw.ctms.move.integration.response.resource.product;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ProductRecord {
    private Long id;
    private String name;
    private String code;
    private String unNumber;
    private ProductCategoryDetails productCategoryDetails;
    private StatusDetails status;
    private String description;
    private Boolean isContainerFillingRuleApplied;
    private Double filledPercentage;
    private List<PropertyDetails> properties;
    private Boolean isAttentionNeeded;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ProductCategoryDetails {
        private Long id;
        private String code;
        private String name;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class StatusDetails {
        private String label;
        private String value;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PropertyDetails {
        private Long id;
        private PropertyName propertyName;
        private String propertyValue;
        private UomDetails uomDetails;
        private String productCode;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PropertyName {
        private String label;
        private String value;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class UomDetails {
        private Long id;
        private String name;
        private String code;
    }
}