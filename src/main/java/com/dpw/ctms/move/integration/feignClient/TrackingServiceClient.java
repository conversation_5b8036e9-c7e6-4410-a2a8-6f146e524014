package com.dpw.ctms.move.integration.feignClient;

import com.dpw.ctms.move.config.FeignConfig;
import com.dpw.ctms.move.integration.request.trackingservice.TripTrackingRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import static com.dpw.ctms.move.constants.TrackingServiceConstants.TRIP_TRACKING;

@FeignClient(name = "trackingService", url = "${cargoes-flow.tracking-service-url:http://localhost:8081}", configuration = FeignConfig.class)
public interface TrackingServiceClient {
    @PostMapping(value = TRIP_TRACKING,
            produces = MediaType.TEXT_PLAIN_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    String trackTrip(
            @RequestParam("code") String code,
            @RequestBody TripTrackingRequest tripTrackingRequest
    );
}
