package com.dpw.ctms.move.integration.adapter;

import com.dpw.ctms.move.integration.feignClient.TrackingServiceClient;
import com.dpw.ctms.move.integration.request.trackingservice.TripTrackingRequest;
import com.dpw.ctms.move.integration.response.trackingservice.TripTrackingResponse;
import com.dpw.tmsutils.utils.ObjectMapperUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import static com.dpw.ctms.move.constants.TrackingServiceConstants.SERVICE_NAME;
import static com.dpw.ctms.move.constants.TrackingServiceConstants.TRIP_TRACKING_SERVICE_CONTEXT;


@Component
@RequiredArgsConstructor
public class TrackingServiceAdapter extends BaseServiceAdapter{
    private final TrackingServiceClient trackingServiceClient;

    @Value("${cargoes-flow.tracking-service-path-variable}")
    private String trackingServicePathVariable;


    public TripTrackingResponse trackTrip (
            TripTrackingRequest tripTrackingRequest) throws JsonProcessingException {
        String response =
                execute(TRIP_TRACKING_SERVICE_CONTEXT, () -> trackingServiceClient
                        .trackTrip(trackingServicePathVariable, tripTrackingRequest));
        return ObjectMapperUtil.getObjectMapper().readValue(response, TripTrackingResponse.class);
    }

    @Override
    public String getServiceName() {
        return SERVICE_NAME;
    }
}
