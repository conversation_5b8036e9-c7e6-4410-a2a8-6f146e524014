package com.dpw.ctms.move.integration.dto.resource;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class ResourceFacilitiesDto {
    private Long id;
    private String code;
    private String name;
    private StatusDetails status;
    private OwnershipTypeDetails ownershipType;
    private List<MappedEntity> mappedEntities;
    private String instructions;
    private PointOfContactDetails pointOfContactDetails;
    private AddressDetails addressDetails;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class StatusDetails {
        private String label;
        private String value;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class OwnershipTypeDetails {
        private String label;
        private String value;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class MappedEntity {
        private Long id;
        private String name;
        private String code;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class PointOfContactDetails {
        private Long id;
        private String firstName;
        private String lastName;
        private String designation;
        private String emailId;
        private String phoneNumber;
        private String countryCode;
        private List<String> additionalEmailIds;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class AddressDetails {
        private String externalAddressCode;
        private String addressLine;
        private String postalCode;
        private SuburbDetails suburbDetails;
        private CityDetails cityDetails;
        private ProvinceDetails provinceDetails;
        private CountryDetails countryDetails;
        private PinPointDetails point;

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        @SuperBuilder
        public static class SuburbDetails {
            private String name;
            private String externalSuburbCode;
        }

        @Data
        @AllArgsConstructor
        @NoArgsConstructor
        @SuperBuilder
        public static class CityDetails {
            private String name;
            private String externalCityCode;
        }

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        @SuperBuilder
        public static class ProvinceDetails {
            private Long id;
            private String name;
            private String externalProvinceCode;
        }

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        @SuperBuilder
        public static class CountryDetails {
            private String name;
            private String externalCountryCode;
        }

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        @SuperBuilder
        public static class PinPointDetails {
            private String lat;
            private String lng;
        }
    }
}