package com.dpw.ctms.move.integration.service.impl;

import com.dpw.ctms.move.dto.PaginationDTO;
import com.dpw.ctms.move.integration.adapter.OmsServiceAdapter;
import com.dpw.ctms.move.integration.dto.oms.ConsignmentListRequestDTO;
import com.dpw.ctms.move.integration.dto.oms.OmsConsignmentDto;
import com.dpw.ctms.move.integration.mapper.oms.OmsConsignmentMapper;
import com.dpw.ctms.move.integration.response.oms.OmsListResponse;
import com.dpw.ctms.move.integration.response.oms.consignment.ConsignmentRecord;
import com.dpw.ctms.move.integration.service.IOmsIntegratorService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class OmsIntegratorServiceImpl implements IOmsIntegratorService {
    private final OmsServiceAdapter omsServiceAdapter;
    private final OmsConsignmentMapper omsConsignmentMapper;

    @Override
    public List<OmsConsignmentDto> getOmsConsignmentDtos(List<String> externalConsignmentIds) {
        OmsListResponse<ConsignmentRecord> consignmentResponse = omsServiceAdapter.getConsignmentList(
                ConsignmentListRequestDTO.builder().ids(externalConsignmentIds).build(),
                PaginationDTO.builder().pageNo(0).pageSize(externalConsignmentIds.size()).build());

        List<ConsignmentRecord> records = Optional.ofNullable(consignmentResponse.getResponse())
                .orElse(Collections.emptyList());

        return records.stream()
                .map(omsConsignmentMapper::mapToOmsConsignmentDto)
                .filter(Objects::nonNull)
                .toList();
    }
}
