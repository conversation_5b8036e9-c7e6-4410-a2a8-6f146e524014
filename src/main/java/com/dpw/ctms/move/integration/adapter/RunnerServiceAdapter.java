package com.dpw.ctms.move.integration.adapter;

import com.dpw.ctms.move.constants.RunnerServiceConstants;
import com.dpw.ctms.move.integration.feignClient.RunnerClient;
import com.dpw.ctms.move.integration.request.runner.RunnerDocumentRequest;
import com.dpw.ctms.move.integration.response.runner.RunnerDocumentResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class RunnerServiceAdapter extends BaseServiceAdapter {

    private final RunnerClient runnerClient;

    public RunnerServiceAdapter(RunnerClient runnerClient) {
        this.runnerClient = runnerClient;
    }

    public RunnerDocumentResponse sendPodDocument(RunnerDocumentRequest request) {
        return execute(RunnerServiceConstants.SEND_POD_DOCUMENT, () -> {
            log.info("Calling Runner service to add file for TMS. ShipmentId: {}, FileName: {}", 
                    request.getShipmentId(), request.getFileName());
            
            RunnerDocumentResponse response = runnerClient.sendPodDocument(request);
            
            log.info("Successfully added file to Runner for shipmentId: {}", request.getShipmentId());
            return response;
        });
    }
    
    @Override
    public String getServiceName() {
        return RunnerServiceConstants.RUNNER_SERVICE;
    }
}