package com.dpw.ctms.move.integration.util;

import com.dpw.ctms.move.dto.PaginationDTO;
import com.dpw.ctms.move.integration.dto.resource.FacilityListRequestDTO;
import com.dpw.ctms.move.integration.dto.resource.ProductListRequestDTO;
import com.dpw.ctms.move.integration.dto.resource.UomListRequestDTO;
import com.dpw.ctms.move.integration.request.OutboundPaginationRequest;
import com.dpw.ctms.move.integration.request.resource.ResourceListRequest;
import com.dpw.ctms.move.integration.request.resource.ProductListFilter;
import com.dpw.ctms.move.integration.request.resource.UomListFilter;
import com.dpw.ctms.move.integration.request.resource.facility.FacilityListFilter;
import org.springframework.stereotype.Component;

/**
 * Utility class for creating Resource service requests
 */
@Component
public class ResourceRequestUtil {

    /**
     * Creates a facility list request with pagination and optional filter by facility IDs
     *
     * @param facilityListRequestDTO Optional to filter list by
     * @param paginationDTO Pagination parameters
     * @return ListRequest with appropriate filter
     */
    public static ResourceListRequest<FacilityListFilter> createFacilityListRequest(FacilityListRequestDTO facilityListRequestDTO, PaginationDTO paginationDTO) {
        FacilityListFilter filter = null;
        if (facilityListRequestDTO != null) {
            filter = FacilityListFilter.builder()
                    .ids(facilityListRequestDTO.getFacilityCodes())
                    .build();
        }

        return ResourceListRequest.<FacilityListFilter>builder()
                .pagination(convertToPaginationRequest(paginationDTO))
                .filter(filter)
                .build();
    }

    /**
     * Creates a UOM list request with pagination and optional filter by UOM IDs
     * @param uomListRequestDTO Optional to filter list by
     * @param paginationDTO Pagination parameters
     */
    public static ResourceListRequest<UomListFilter> createUomListRequest(UomListRequestDTO uomListRequestDTO, PaginationDTO paginationDTO) {
        UomListFilter filter = null;
        if (uomListRequestDTO != null) {
            filter = UomListFilter.builder()
                    .ids(uomListRequestDTO.getIds())
                    .codes(uomListRequestDTO.getCodes())
                    .build();
        }

        return ResourceListRequest.<UomListFilter>builder()
                .pagination(convertToPaginationRequest(paginationDTO))
                .filter(filter)
                .build();
    }

    /**
     * Creates a product list request with pagination and optional filter by product IDs
     * @param productListRequestDTO Optional to filter list by
     * @param paginationDTO Pagination parameters
     */
    public static ResourceListRequest<ProductListFilter> createProductListRequest(ProductListRequestDTO productListRequestDTO, PaginationDTO paginationDTO) {
        ProductListFilter filter = null;
        if (productListRequestDTO != null) {
            filter = ProductListFilter.builder()
                    .ids(productListRequestDTO.getIds())
                    .build();
        }

        return ResourceListRequest.<ProductListFilter>builder()
                .pagination(convertToPaginationRequest(paginationDTO))
                .filter(filter)
                .build();
    }
    
    /**
     * @deprecated Use {@link #createFacilityListRequest(FacilityListRequestDTO, PaginationDTO)} instead
     */
    @Deprecated
    public static ResourceListRequest<FacilityListFilter> createFacilityListRequest(FacilityListRequestDTO facilityListRequestDTO, int pageNo, int pageSize) {
        return createFacilityListRequest(facilityListRequestDTO, PaginationDTO.builder()
                .pageNo(pageNo)
                .pageSize(pageSize)
                .build());
    }
    
    /**
     * @deprecated Use {@link #createUomListRequest(UomListRequestDTO, PaginationDTO)} instead
     */
    @Deprecated
    public static ResourceListRequest<UomListFilter> createUomListRequest(UomListRequestDTO uomListRequestDTO, int pageNo, int pageSize) {
        return createUomListRequest(uomListRequestDTO, PaginationDTO.builder()
                .pageNo(pageNo)
                .pageSize(pageSize)
                .build());
    }
    
    /**
     * Converts PaginationDTO to PaginationRequest
     */
    private static OutboundPaginationRequest convertToPaginationRequest(PaginationDTO paginationDTO) {
        if (paginationDTO == null) {
            return OutboundPaginationRequest.builder()
                    .pageNo(0)
                    .pageSize(10)
                    .build();
        }
        
        return OutboundPaginationRequest.builder()
                .pageNo(paginationDTO.getPageNoOrDefault())
                .pageSize(paginationDTO.getPageSizeOrDefault())
                .build();
    }

}
