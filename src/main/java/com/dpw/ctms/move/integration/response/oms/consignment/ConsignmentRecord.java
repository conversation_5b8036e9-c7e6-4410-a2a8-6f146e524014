package com.dpw.ctms.move.integration.response.oms.consignment;

import com.dpw.ctms.move.integration.response.LabelValue;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConsignmentRecord {
    private Long id;
    private String code;
    private Long lineItemId;
    private String lineItemCode;
    private Long customerOrderId;
    private String customerOrderCode;
    private CustomerOrderMetadata customerOrderMetadata;
    private LineItemMetadata lineItemMetadata;
    private Map<String, String> consignmentMetadata;
    private Customer customer;
    private Location origin;
    private Location destination;
    private TimeInfo expectedPickupTime;
    private TimeInfo expectedDeliveryTime;
    private LabelValue status;
    private LabelValue movementType;
    private Long createdAt;
    private Long updatedAt;
    private String createdBy;
    private String updatedBy;
    private List<Product> products;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CustomerOrderMetadata {
        private String customerOrderNumber;
        private String internalReferenceNumber;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LineItemMetadata {
        private String specialInstructions;
        private String lineIdentifierNumber1;
        private String lineIdentifierNumber2;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Customer {
        private String id;
        private LabelValue customerType;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Location {
        private String id;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TimeInfo {
        private Long epoch;
        private String timeZoneCode;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Product {
        private Long id;
        private ResourceDetails resourceDetails;
        private List<Property> properties;

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class ResourceDetails {
            private Long id;
        }

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class Property {
            private LabelValue propertyName;
            private double propertyValue;
            private UnitOfMeasurement unitOfMeasurement;

            @Data
            @Builder
            @NoArgsConstructor
            @AllArgsConstructor
            public static class UnitOfMeasurement {
                private Long id;
            }
        }
    }
}
