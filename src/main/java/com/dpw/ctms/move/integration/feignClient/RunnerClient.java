package com.dpw.ctms.move.integration.feignClient;

import com.dpw.ctms.move.config.RunnerFeignConfig;
import com.dpw.ctms.move.constants.RunnerServiceConstants;
import com.dpw.ctms.move.integration.request.runner.RunnerDocumentRequest;
import com.dpw.ctms.move.integration.response.runner.RunnerDocumentResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = RunnerServiceConstants.Config.RUNNER_CLIENT_NAME, 
             url = "${runner.url}", 
             configuration = RunnerFeignConfig.class)
public interface RunnerClient {

    @PostMapping(value = RunnerServiceConstants.Endpoints.ADD_FILE_TMS,
            consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    RunnerDocumentResponse sendPodDocument(@RequestBody RunnerDocumentRequest request);
}