package com.dpw.ctms.move.integration.dto.oms;

import com.dpw.ctms.move.dto.TimeDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class OmsConsignmentDto {
    private Long id;
    private String code;
    private Long lineItemId;
    private String lineItemCode;
    private Long customerOrderId;
    private String customerOrderCode;
    private CustomerOrderMetadata customerOrderMetadata;
    private LineItemMetadata lineItemMetadata;
    private Map<String, String> consignmentMetadata;
    private Customer customer;
    private Location origin;
    private Location destination;
    private TimeDTO expectedPickupTime;
    private TimeDTO expectedDeliveryTime;
    private StatusDetails status;
    private LabelValue movementType;
    private Long createdAt;
    private Long updatedAt;
    private String createdBy;
    private String updatedBy;
    private List<Product> products;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class CustomerOrderMetadata {
        private String customerOrderNumber;
        private String internalReferenceNumber;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class LineItemMetadata {
        private String specialInstructions;
        private String lineIdentifierNumber1;
        private String lineIdentifierNumber2;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class Customer {
        private String id;
        private LabelValue customerType;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class Location {
        private String id;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class Product {
        private Long id;
        private Long resourceId;
        private List<Property> properties;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class Property {
        private String propertyNameLabel;
        private String propertyNameValue;
        private Double propertyValue;
        private Long unitOfMeasurementId;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class StatusDetails {
        private String label;
        private String value;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class LabelValue {
        private String label;
        private String value;
    }
}