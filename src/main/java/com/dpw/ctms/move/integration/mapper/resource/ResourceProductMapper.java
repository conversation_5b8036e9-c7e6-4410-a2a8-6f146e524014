package com.dpw.ctms.move.integration.mapper.resource;

import com.dpw.ctms.move.integration.dto.resource.ResourceProductDetailsDto;
import com.dpw.ctms.move.integration.response.resource.product.ProductRecord;
import org.apache.commons.lang3.ObjectUtils;
import org.mapstruct.*;

import java.util.List;
import java.util.stream.Collectors;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface ResourceProductMapper {

    @Mapping(target = "productCategoryDetails", source = "productCategoryDetails", qualifiedByName = "mapProductCategoryDetails")
    @Mapping(target = "status", source = "status", qualifiedByName = "mapStatusDetails")
    @Mapping(target = "properties", source = "properties", qualifiedByName = "mapProperties")
    ResourceProductDetailsDto mapToResourceProductDetailsDto(ProductRecord productRecord);

    @Named("mapProductCategoryDetails")
    default ResourceProductDetailsDto.ProductCategoryDetails mapProductCategoryDetails(
            ProductRecord.ProductCategoryDetails productCategoryDetails) {
        if (ObjectUtils.isEmpty(productCategoryDetails)) {
            return null;
        }
        return ResourceProductDetailsDto.ProductCategoryDetails.builder()
                .id(productCategoryDetails.getId())
                .code(productCategoryDetails.getCode())
                .name(productCategoryDetails.getName())
                .build();
    }

    @Named("mapStatusDetails")
    default ResourceProductDetailsDto.StatusDetails mapStatusDetails(ProductRecord.StatusDetails status) {
        if (ObjectUtils.isEmpty(status)) {
            return null;
        }
        return ResourceProductDetailsDto.StatusDetails.builder()
                .label(status.getLabel())
                .value(status.getValue())
                .build();
    }

    @Named("mapProperties")
    default List<ResourceProductDetailsDto.PropertyDetails> mapProperties(
            List<ProductRecord.PropertyDetails> properties) {
        if (ObjectUtils.isEmpty(properties)) {
            return null;
        }
        return properties.stream()
                .map(this::mapPropertyDetails)
                .collect(Collectors.toList());
    }

    default ResourceProductDetailsDto.PropertyDetails mapPropertyDetails(
            ProductRecord.PropertyDetails property) {
        if (ObjectUtils.isEmpty(property)) {
            return null;
        }
        return ResourceProductDetailsDto.PropertyDetails.builder()
                .id(property.getId())
                .propertyNameLabel(!ObjectUtils.isEmpty(property.getPropertyName()) ? 
                        property.getPropertyName().getLabel() : null)
                .propertyNameValue(!ObjectUtils.isEmpty(property.getPropertyName()) ? 
                        property.getPropertyName().getValue() : null)
                .propertyValue(property.getPropertyValue())
                .uomId(!ObjectUtils.isEmpty(property.getUomDetails()) ? 
                        property.getUomDetails().getId() : null)
                .uomName(!ObjectUtils.isEmpty(property.getUomDetails()) ? 
                        property.getUomDetails().getName() : null)
                .uomCode(!ObjectUtils.isEmpty(property.getUomDetails()) ? 
                        property.getUomDetails().getCode() : null)
                .productCode(property.getProductCode())
                .build();
    }
}