package com.dpw.ctms.move.integration.service.impl;

import com.dpw.ctms.move.dto.PaginationDTO;
import com.dpw.ctms.move.integration.adapter.ResourceServiceAdapter;
import com.dpw.ctms.move.integration.dto.resource.FacilityListRequestDTO;
import com.dpw.ctms.move.integration.dto.resource.UomListRequestDTO;
import com.dpw.ctms.move.integration.dto.resource.ProductListRequestDTO;
import com.dpw.ctms.move.integration.dto.resource.ResourceFacilitiesDto;
import com.dpw.ctms.move.integration.dto.resource.ResourceProductDetailsDto;
import com.dpw.ctms.move.integration.dto.resource.ResourceUomDto;
import com.dpw.ctms.move.integration.mapper.resource.ResourceFacilitiesMapper;
import com.dpw.ctms.move.integration.mapper.resource.ResourceProductMapper;
import com.dpw.ctms.move.integration.mapper.resource.ResourceUomMapper;
import com.dpw.ctms.move.integration.response.ListResponse;
import com.dpw.ctms.move.integration.response.resource.facility.FacilityRecord;
import com.dpw.ctms.move.integration.response.resource.product.ProductRecord;
import com.dpw.ctms.move.integration.response.resource.uom.UomRecord;
import com.dpw.ctms.move.integration.service.IResourceIntegratorService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;


@Service
@RequiredArgsConstructor
public class ResourceIntegratorServiceImpl implements IResourceIntegratorService {
    private final ResourceServiceAdapter resourceServiceAdapter;
    private final ResourceFacilitiesMapper facilitiesMapper;
    private final ResourceProductMapper productMapper;
    private final ResourceUomMapper uomMapper;
    @Override
    public List<ResourceFacilitiesDto> getFacilitiesDTOs(List<String> externalFacilityCodes) {
        ListResponse<FacilityRecord> facilityResponse = resourceServiceAdapter.getFacilityList(
                FacilityListRequestDTO.builder()
                        .facilityCodes(externalFacilityCodes.stream().map(Objects::toString).toList())
                        .build(),
                PaginationDTO.builder().pageNo(0).pageSize(externalFacilityCodes.size()).build());

        List<FacilityRecord> records = Optional.ofNullable(facilityResponse.getRecords())
                .orElse(Collections.emptyList());

        return records.stream()
                .map(facilitiesMapper::mapToResourceFacilitiesDto)
                .filter(Objects::nonNull)
                .toList();
    }

    @Override
    public List<ResourceProductDetailsDto> getProductDetailsDTOs(List<String> productIds) {
        ListResponse<ProductRecord> productResponse = resourceServiceAdapter.getProductList(
                ProductListRequestDTO.builder()
                        .ids(productIds)
                        .build(),
                PaginationDTO.builder().pageNo(0).pageSize(productIds.size()).build());

        List<ProductRecord> records = Optional.ofNullable(productResponse.getRecords())
                .orElse(Collections.emptyList());

        return records.stream()
                .map(productMapper::mapToResourceProductDetailsDto)
                .filter(Objects::nonNull)
                .toList();
    }

    @Override
    public List<ResourceUomDto> getUomDTOs(List<String> uomIds) {
        ListResponse<UomRecord> uomResponse = resourceServiceAdapter.getUomList(
                UomListRequestDTO.builder()
                        .ids(uomIds)
                        .build(),
                PaginationDTO.builder().pageNo(0).pageSize(uomIds.size()).build());

        List<UomRecord> records = Optional.ofNullable(uomResponse.getRecords())
                .orElse(Collections.emptyList());

        return records.stream()
                .map(uomMapper::mapToResourceUomDto)
                .filter(Objects::nonNull)
                .toList();
    }
}
