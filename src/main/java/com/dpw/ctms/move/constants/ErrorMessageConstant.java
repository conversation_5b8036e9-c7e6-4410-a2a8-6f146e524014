package com.dpw.ctms.move.constants;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class
ErrorMessageConstant {
    public static final String ERR_DOCUMENT_FAILED_DELETE = "Unable to delete document";
    public static final String ERR_DOCUMENT_FAILED_DOWNLOAD_IO_EXCEPTION = "Unable to download document due to input output exception " +
            "for reference id %s";
    public static final String ERR_TOKEN_GENERATION = "Unable to generate the token";
    public static final String ERR_DOCUMENT_FAILED_MERGE = "Unable to merge documents";
    public static final String INVALID_SHIPMENT_CODE = "Cannot find shipment with code: %s";
    public static final String INVALID_TRIP_CODE = "Cannot find trip with code: %s";
    public static final String INVALID_STOP_CODE = "Cannot find stop with code: %s";
    public static final String ERR_CALLING_EXTERNAL_SERVICE = "Error while calling external api";
    public static final String ERROR_READING_JSON_FILE = "Error reading JSON file";
    public static final String INVALID_TASK_ID = "Cannot find task with id: %s";
    public static final String CONFIG_SERVICE_NOT_FOUND = "No config service found for type: %s";
    public static final String STATE_MACHINE_CONFIG_NOT_FOUND = "No state machine config found for tenantId: %s";
    public static final String ERROR_CREATING_STATE_MACHINE = "Exception occurred while creating state machine: %s";
    public static final String UNSUPPORTED_STATE_MACHINE_TYPE = "Unsupported state machine type: %s";
    public static final String INVALID_TASK_CODE = "Cannot find task with code: %s";
    public static final String INVALID_TASK_PARAM_STRATEGY = "Cannot find task param strategy for param type: %s";
    public static final String INVALID_ENTITY_TASK_MAPPING_STRATEGY = "Cannot find entity task mapping strategy for param type: %s";
    public static final String PARAM_VALUE_VALIDATIONS_FAILED = "Validations failed for task param type %s for fields: %s";
    public static final String INVALID_SHIPMENT_ID = "Cannot find shipment with id: %s";
    public static final String INVALID_TRIP_ID = "Cannot find trip with id: %s";
    public static final String UNABLE_TO_RESOLVE_TENANT = "Unable to resolve tenant";
    public static final String DOCUMENT_GENERATION_FAILED = "Failed to generate document for trip: %s";
    public static final String JOLT_TRANSFORMATION_FAILED = "JOLT transformation failed for trip: %s with error: %s";
    public static final String JOLT_SPECS_INVALID = "JOLT specs are null or empty for vendor: %s";
    public static final String INVALID_TRANSPORT_ORDER_ID = "Cannot find transport order with id: %s";
    public static final String INVALID_TRANSPORT_ORDER_CODE = "Cannot find transport order with code: %s";
    public static final String TRANSPORT_ORDER_DB_PERSISTENCE_FAILED = "Failed to persist transport order and related entities in database";
    public static final String TRANSPORT_ORDER_ALREADY_EXISTS = "Transport order code or related entities already exists: %s";
    public static final String TRANSPORT_ORDER_UPDATE_NOT_ALLOWED = "Transport order update is not allowed: %s";
    public static final String UPLOAD_FAILED_DOCUMENT_MESSAGE = "Failed to upload document";
}
