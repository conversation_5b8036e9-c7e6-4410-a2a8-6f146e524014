package com.dpw.ctms.move.constants;

public class UnleashConstant {

    public static final String TENANT = "tenant";

    public static class FeatureFlags {
        public static final String MOVE_MANIFEST_CONFIG = "move_manifest_config";
        public static final String MOVE_BOL_DOCUMENT_CONFIG = "move_bol_document_config";
        public static final String MOVE_ENABLE_RUNNER_API_CALL = "move_enable_runner_api_call";
        public static final String MOVE_VEHICLE_OPERATOR_ROLE_CONFIG = "move_vehicle_operator_role_config";
        public static final String TIMEZONE_CODE_TO_OFFSET = "timezone_code_to_offset";
        public static final String OFFSET_TO_TIMEZONE_MAP = "offset_to_timezone_map";
        public static final String MOVE_SHIPMENT_STATE_MACHINE_CONFIG = "move_shipment_state_machine_config";
        public static final String MOVE_TRIP_STATE_MACHINE_CONFIG = "move_trip_state_machine_config";
        public static final String MOVE_TASK_STATE_MACHINE_CONFIG = "move_task_state_machine_config";
        public static final String MOVE_TO_STATE_MACHINE_CONFIG = "move_to_state_machine_config";
        public static final String MOVE_TASK_MANDATORY_CHECK_CHANNEL = "move_task_mandatory_check_channel";
            public static final String MOVE_TASK_CODE_MAPPING = "move_task_code_mapping";
        public static final String MOVE_TRACKING_SERVICE_INTEGRATION_TOGGLE_CONFIG = "move_tracking_service_integration_toggle_config";
    }
}
