package com.dpw.ctms.move.constants;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * Constants class containing field names used in Shipment entity queries and
 * mappings.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ShipmentFieldConstants {

    // ===== BASIC SHIPMENT ENTITY FIELDS =====
    public static final String CODE = "code";
    public static final String STATUS = "status";
    public static final String CREATED_AT = "createdAt";
    public static final String UPDATED_AT = "updatedAt";
    public static final String DELETED_AT = "deletedAt";

    // ===== SHIPMENT SPECIFIC FIELDS =====
    public static final String EXTERNAL_CONSIGNMENT_ID = "externalConsignmentId";
    public static final String EXTERNAL_CUSTOMER_ORDER_ID = "externalCustomerOrderId";
    public static final String VOLUME = "volume";
    public static final String WEIGHT = "weight";

    // ===== SHIPMENT TIMESTAMP FIELDS =====
    public static final String EXPECTED_PICKUP_AT = "expectedPickupAt";
    public static final String EXPECTED_DELIVERY_AT = "expectedDeliveryAt";
    public static final String ACTUAL_PICKUP_AT = "actualPickupAt";
    public static final String ACTUAL_DELIVERY_AT = "actualDeliveryAt";

    // ===== RELATIONSHIP FIELD NAMES =====
    public static final String TRIP = "trip";
    public static final String TRANSPORT_ORDER = "transportOrder";
    public static final String ORIGIN_STOP = "originStop";
    public static final String DESTINATION_STOP = "destinationStop";
    public static final String TASK = "task";
        public static final String SHIPMENT_TASKS = "shipmentTasks";

    // ===== STOP FIELDS =====
    public static final String EXTERNAL_LOCATION_CODE = "externalLocationCode";

    // ===== TRIP FIELDS =====
    public static final String TRIP_CODE = "trip.code";
    public static final String TRIP_STATUS = "trip.status";

    // ===== TRANSPORT ORDER FIELDS =====
    public static final String TRANSPORT_ORDER_CODE = "transportOrder.code";
    public static final String ASSIGNEE_IDENTIFIER = "assigneeIdentifier";

    // ===== VEHICLE RESOURCE FIELDS =====
    public static final String VEHICLE_RESOURCE = "vehicleResource";
    public static final String EXTERNAL_VEHICLE_TYPE_ID = "externalVehicleTypeId";
    public static final String EXTERNAL_RESOURCE_ID = "externalResourceId";

    // ===== TRAILER RESOURCE FIELDS =====
    public static final String TRAILER_RESOURCES = "trailerResources";

    // ===== VEHICLE OPERATOR RESOURCE FIELDS =====
    public static final String VEHICLE_OPERATOR_RESOURCES = "vehicleOperatorResources";

    // ===== DOCUMENT ATTACHED FIELDS =====
    public static final String IS_DOCUMENT_ATTACHED = "isDocumentAttached";

    public static class SortFields {
        // API field names (keys) to entity field names (values)
        public static final String API_CODE = "code";
        public static final String API_STATUS = "status";
        public static final String API_CREATED_AT = "createdAt";
        public static final String API_UPDATED_AT = "updatedAt";
        public static final String API_TRIP_CODE = "tripCode";
        public static final String API_TRANSPORT_ORDER_CODE = "transportOrderCode";
        public static final String API_CUSTOMER_ORDER_ID = "customerOrderId";
        public static final String API_CONSIGNMENT_ID = "consignmentId";
        public static final String API_EXPECTED_PICKUP_AT = "expectedPickupAt";
        public static final String API_EXPECTED_DELIVERY_AT = "expectedDeliveryAt";
        public static final String API_ACTUAL_PICKUP_AT = "actualPickupAt";
        public static final String API_ACTUAL_DELIVERY_AT = "actualDeliveryAt";
        public static final String API_VOLUME = "volume";
        public static final String API_WEIGHT = "weight";
    }

    // ===== DEFAULT VALUES =====
    public static final String DEFAULT_SORT_BY = CREATED_AT;
    public static final String DEFAULT_SORT_ORDER = "DESC";
}
