package com.dpw.ctms.move.constants;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

public class RunnerServiceConstants {
    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static class Endpoints {
        public static final String ADD_FILE_TMS = "/runner/v2/documentManagement/files-management/addFileTms";
    }
    
    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static class Headers {
        public static final String ACCEPT = "accept";
        public static final String CONTENT_TYPE = "Content-Type";
        public static final String SOURCE = "source";
        public static final String X_API_KEY = "x-api-key";
        public static final String TENANT_ID = "tenant-id";
        
        public static final String DEFAULT_ACCEPT = "application/json, text/plain, */*";
        public static final String DEFAULT_CONTENT_TYPE = "application/json";
        public static final String DEFAULT_SOURCE = "TMS";
    }
    
    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static class Config {
        public static final String RUNNER_CLIENT_NAME = "runner-client";
    }
    
    public static final String RUNNER_SERVICE = "Runner Service";
    public static final String SEND_POD_DOCUMENT = "SendPodDocument";
}