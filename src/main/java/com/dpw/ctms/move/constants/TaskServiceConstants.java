package com.dpw.ctms.move.constants;


import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class TaskServiceConstants {
    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static class Endpoints {
        public static final String TASK_INSTANCE_REGISTER = "/v1/api/task-instances/register";
        public static final String TASK_INSTANCE_DE_REGISTER = "/v1/api/task-instances/de-register";
        public static final String GET_TASK_BY_REGISTRATION_CODE = "/v1/api/task-execution/tasks/get-all";
    }
    public static final String DEFAULT_TENANT_CODE = "CTMS";
    public static final String DEFAULT_TENANT_SERVICE_CODE = "TASK_MASTER";
    public static final String CURR_CLIENT_CODE = "CURR_CLIENT_CODE";
    public static final String CURR_LOC_CODE = "CURR_LOC_CODE";
}
