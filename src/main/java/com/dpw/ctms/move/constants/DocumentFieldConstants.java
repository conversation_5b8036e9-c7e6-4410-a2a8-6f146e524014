package com.dpw.ctms.move.constants;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class DocumentFieldConstants {

    // ===== BASIC DOCUMENT ENTITY FIELDS =====
    public static final String ID = "id";
    public static final String ENTITY_ID = "entityId";
    public static final String ENTITY_TYPE = "entityType";
    public static final String DOCUMENT_TYPE = "documentType";
    public static final String STATUS = "status";
    public static final String DOCUMENT_OPERATION_TYPE = "documentOperationType";
    public static final String CHECKSUM = "checksum";
    public static final String FILE_IDENTIFIER = "fileIdentifier";
    public static final String CLIENT_IDENTIFIER = "clientIdentifier";
    public static final String ASYNC_MAPPING_UUID = "asyncMappingUUID";
    public static final String FILE_NAME = "fileName";
    public static final String FILE_SIZE = "fileSize";
    public static final String FILE_TYPE = "fileType";
    public static final String PRESIGNED_DOWNLOAD_URL = "presignedDownloadUrl";
    public static final String FILE_METADATA = "fileMetadata";

    // ===== BASE ENTITY FIELDS =====
    public static final String CREATED_AT = "createdAt";
    public static final String UPDATED_AT = "updatedAt";
    public static final String CREATED_BY = "createdBy";
    public static final String UPDATED_BY = "updatedBy";
}