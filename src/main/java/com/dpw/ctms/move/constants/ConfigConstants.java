package com.dpw.ctms.move.constants;

public class ConfigConstants {
    public static final String BOL_CONFIG = "BOL_CONFIG";
    public static final String TIMEZONE_CODE_TO_OFFSET = "timezone_code_to_offset";
    public static final String OFFSET_TO_TIMEZONE_MAP = "offset_to_timezone_map";
    public static final String ENABLE_RUNNER_API_CALL = "ENABLE_RUNNER_API_CALL";

    // Configuration validation constants
    public static final String TEMPLATE_ID_FIELD = "templateId";
    public static final String JOLT_CONFIG_FIELD = "joltConfig";

    public static final String VEHICLE_OPERATOR_ROLE_FIELD = "role";

    public static final String STATE_MACHINE_STATES_KEY = "states";
    public static final String STATE_MACHINE_TRANSITIONS_KEY = "transitions";

    public static final String TRACKING_SERVICE_INTEGRATION_TOGGLE_KEY = "isEnabled";


    public static final String VEHICLE_OPERATOR_ROLE_CONFIG = "VEHIC<PERSON>_OPERATOR_ROLE_CONFIG";
    public static final String SHIPMENT_STATE_MACHINE_CONFIG = "SHIPMENT_STATE_MACHINE_CONFIG";
    public static final String TRIP_STATE_MACHINE_CONFIG = "TRIP_STATE_MACHINE_CONFIG";
    public static final String TASK_STATE_MACHINE_CONFIG = "TASK_STATE_MACHINE_CONFIG";
    public static final String TO_STATE_MACHINE_CONFIG = "TO_STATE_MACHINE_CONFIG";
    public static final String TASK_MANDATORY_CHECK_CHANNEL = "TASK_MANDATORY_CHECK_CHANNEL";
    public static final String TASK_CODE_MAPPING = "TASK_CODE_MAPPING";

    public static final String ENABLE_RUNNER_API_CALL_KEY= "enable";

    public static final String TRACKING_SERVICE_INTEGRATION_TOGGLE_CONFIG = "TRACKING_SERVICE_INTEGRATION_TOGGLE_CONFIG";

}
