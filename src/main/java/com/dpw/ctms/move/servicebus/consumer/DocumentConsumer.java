package com.dpw.ctms.move.servicebus.consumer;

import com.azure.messaging.servicebus.ServiceBusClientBuilder;
import com.azure.messaging.servicebus.ServiceBusErrorContext;
import com.azure.messaging.servicebus.ServiceBusProcessorClient;
import com.azure.messaging.servicebus.ServiceBusReceivedMessage;
import com.azure.messaging.servicebus.ServiceBusReceivedMessageContext;
import com.dpw.ctms.move.request.documentEvent.PreSignedUrlEvent;
import com.dpw.ctms.move.service.IDocumentsService;
import com.dpw.tmsutils.threadlocal.TenantContext;
import jakarta.annotation.PreDestroy;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.function.Consumer;

import static com.dpw.tmsutils.utils.ObjectMapperUtil.objectMapper;

@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "asb-config", name = "connect", havingValue = "true")
public class DocumentConsumer {

    private ServiceBusProcessorClient serviceBusProcessorClient;
    private final IDocumentsService documentService;

    @Value("${asb-config.topic.name}")
    private String topicName;

    @Value("${asb-config.subscription.name}")
    private String subscriptionName;

    @Value("${asb-config.connectionString}")
    private String connectionString;

    @EventListener(ApplicationReadyEvent.class)
    public void registerSubscriber() {
        log.info("Subscribing to topic {} using connection string {} and subscription {}", topicName, connectionString, subscriptionName);
        Consumer<ServiceBusReceivedMessageContext> onMessage =
                context -> {
                    ServiceBusReceivedMessage message = context.getMessage();
                    try {
                        processMessage(message);
                        context.complete();
                    } catch (Exception e) {
                        log.error("FileUpload Subscriber Error while processing message: {}", message, e);
                        context.abandon();
                    }
                };

        Consumer<ServiceBusErrorContext> onError =
                context -> {
                    Exception e = (Exception) context.getException();
                    log.error(e.getMessage());
                };

        ServiceBusProcessorClient serviceBusProcessorClient = null;
        try {
            serviceBusProcessorClient = new ServiceBusClientBuilder()
                    .connectionString(connectionString)
                    .processor()
                    .topicName(topicName)
                    .subscriptionName(subscriptionName)
                    .processMessage(onMessage)
                    .processError(onError)
                    .buildProcessorClient();

            serviceBusProcessorClient.start();
            log.info("Subscriber started");

            this.serviceBusProcessorClient = serviceBusProcessorClient;

        } catch (Exception e) {
            log.error("Error occurred while trying to start serviceBusProcessorClient", e);
            if (serviceBusProcessorClient != null) {
                serviceBusProcessorClient.close();
            }
        }
    }

    @PreDestroy
    public void shutdown() {
        if (serviceBusProcessorClient != null) {
            log.info("Shutting down ServiceBus processor client");
            serviceBusProcessorClient.close();
        }
    }

    private void processMessage(ServiceBusReceivedMessage message) {
        String messageBody = message.getBody().toString();
        log.info("Received message from queue: {}", messageBody);
        try {
            PreSignedUrlEvent preSignedURLEvent = objectMapper.readValue(messageBody, PreSignedUrlEvent.class);
            TenantContext.setCurrentTenant(preSignedURLEvent.getFileMetadata().getTenant());
            documentService.findAndUpdate(preSignedURLEvent);
        } catch (Exception e) {
            log.error("Error deserializing message body: {}", messageBody, e);
            throw new RuntimeException("Failed to process message", e);
        }
    }
}