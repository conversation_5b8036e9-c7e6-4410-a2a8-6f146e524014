package com.dpw.ctms.move.request.documentEvent;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.ToString;

@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
public class PreSignedUrlEvent {
    private String client;
    private String fileName;
    private Integer remainingWriteOperations;
    @NonNull
    private String clientIdentifier;
    @NonNull
    private String fileIdentifier;
    private String fileKey;
    private String writeExpiryTimeStamp;
    private String presignedUploadUrl;
    private String presignedDownloadUrl;
    private Integer fileSize;
    private String fileType;
    private String subscriptionName;

    @Valid
    @NotNull
    private FileMetadata fileMetadata;
}