package com.dpw.ctms.move.request.message;

import com.dpw.ctms.move.dto.TimeDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShipmentStatusUpdateMessage {
    private String shipmentCode;
    private String extShipmentCode;
    private String externalConsignmentId;
    private String previousStatus;
    private String currentStatus;
    private TimeDTO pickupTime;
    private TimeDTO deliveryTime;
    private String updatedBy;
    private Long updatedAt;
    private String comments;
    private String eventType;
}
