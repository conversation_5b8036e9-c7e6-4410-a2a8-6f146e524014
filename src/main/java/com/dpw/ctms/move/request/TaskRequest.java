package com.dpw.ctms.move.request;

import com.dpw.ctms.move.annotation.ValidEnum;
import com.dpw.ctms.move.enums.TaskStatus;
import com.dpw.ctms.move.request.common.TimeRequest;
import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TaskRequest {
    @NotBlank(message = "Task code cannot be blank or null")
    @Schema(
            name = "code",
            description = "Uniquely identifies a task",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    String code;
    @NotNull(message = "Task status cannot be null")
    @Schema(
            name = "status",
            description = "status of the task",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @ValidEnum(enumClass = TaskStatus.class, message = "Invalid task status. Allowed values: {enumValues}")
    String status;


    @Schema(
            name = "sequence",
            description = "sequence of the task"
    )
    Integer sequence;

    @Valid
    @NotNull(message = "Task params cannot be null")
    @Schema(
            name = "taskParams",
            description = "params associated with a task",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    List<TaskParamRequest> taskParams;

    TimeRequest expectedStartAt;
    TimeRequest expectedEndAt;

    @NotBlank(message = "external task master code cannot be null or blank")
    @Schema(
            name = "externalTaskCode",
            description = "external task code of task management service",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    String externalTaskMasterCode;
    JsonNode details;
}
