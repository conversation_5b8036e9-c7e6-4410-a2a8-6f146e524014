package com.dpw.ctms.move.request;

import com.dpw.ctms.move.enums.AssignmentType;
import com.dpw.ctms.move.request.common.Pagination;
import com.dpw.ctms.move.request.common.Sort;
import jakarta.validation.Valid;
import jakarta.validation.constraints.AssertTrue;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
// TODO: Replace ids with codes where applicable
public class TripListingRequest {

    private Pagination pagination;

    private Sort sort;

    private Filter filter;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Filter {

        private List<String> tripIds;

        private List<String> shipmentIds;

        private List<String> consignmentIds;

        private List<String> tripStatuses;

        private List<String> transportOrderStatuses;

        private List<String> shipmentStatuses;

        private List<String> transportOrderIds;

        private List<String> customerOrderIds;

        private Assignment assignment;

        private List<String> vehicleTypes;

        private List<String> vehicleIds;

        private List<String> trailerIds;

        private List<String> vehicleOperatorIds;

        private String originLocationId;

        private String destinationLocationId;

        @Valid
        private DateRange expectedStartDateRange;

        @Valid
        private DateRange expectedEndDateRange;

        @Valid
        private DateRange actualStartDateRange;

        @Valid
        private DateRange actualEndDateRange;

        private Boolean isPodAttached;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Assignment{
        private List<AssignmentType> types;

        private List<String> identifiers;

         @AssertTrue(message = "Assignment type is required when identifiers are present")
         private boolean isValid() {
             return identifiers == null || (identifiers.isEmpty() || (!CollectionUtils.isEmpty(types)));
         }
    }
}
