package com.dpw.ctms.move.request;

import com.dpw.ctms.move.annotation.ValidEnum;
import com.dpw.ctms.move.enums.AssignmentStatus;
import com.dpw.ctms.move.enums.ResourceAssignmentType;
import jakarta.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ResourceAssignmentDetailsRequest {
    @NotNull(message = "Assignment status cannot be null")
    @ValidEnum(enumClass = AssignmentStatus.class, message = "Invalid resource assignment status. Allowed values: {enumValues}")
    String assignmentStatus;

    @ValidEnum(enumClass = ResourceAssignmentType.class, message = "Invalid resource assignment type. Allowed values: {enumValues}")
    String resourceAssignmentType;

    Long assignedAt;

    String assignedBy;
}
