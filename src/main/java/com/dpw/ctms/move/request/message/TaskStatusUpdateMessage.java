package com.dpw.ctms.move.request.message;

import com.dpw.ctms.move.dto.TimeDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskStatusUpdateMessage {
    private String taskCode;
    private String extTaskCode;
    private String previousStatus;
    private String currentStatus;
    private TimeDTO startTime;
    private TimeDTO endTime;
    private String updatedBy;
    private Long updatedAt;
    private String comments;
    private String taskRegistrationCode;
    private String eventType;
}
